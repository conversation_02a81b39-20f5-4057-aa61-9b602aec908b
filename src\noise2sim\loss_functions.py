"""
Enhanced loss functions for Noise2Sim training
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class Noise2SimLoss(nn.Module):
    """
    Enhanced loss function for Noise2Sim training with multiple components
    """
    
    def __init__(self, 
                 loss_config: Dict,
                 sampling_rate: int = 1000,
                 reduction: str = 'mean'):
        """
        Initialize enhanced loss function
        
        Args:
            loss_config: Loss configuration dictionary
            sampling_rate: Sampling rate for frequency analysis
            reduction: Reduction method ('mean', 'sum', 'none')
        """
        super().__init__()
        self.loss_config = loss_config
        self.sampling_rate = sampling_rate
        self.reduction = reduction
        
        # Base MSE loss
        self.mse_loss = nn.MSELoss(reduction='none')
        
        # Initialize wavelet transform if needed
        if loss_config.get('use_wavelet_loss', False):
            try:
                import pywt
                self.wavelet_name = loss_config.get('wavelet_name', 'db4')
                self.wavelet_levels = loss_config.get('wavelet_levels', 4)
                self.pywt = pywt
            except ImportError:
                logger.warning("PyWavelets not available, disabling wavelet loss")
                loss_config['use_wavelet_loss'] = False
        
        # Loss weights
        self.loss_weights = loss_config.get('loss_weights', {
            'mse': 1.0,
            'wavelet': 0.1,
            'amplitude': 0.05,
            'frequency': 0.1,
            'r_wave': 0.1
        })
        
        logger.info(f"Enhanced loss function initialized with components: {list(self.loss_weights.keys())}")
    
    def forward(self, 
                predictions: torch.Tensor,
                targets: torch.Tensor,
                mask_indices: torch.Tensor,
                original_signals: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Compute enhanced loss with multiple components
        
        Args:
            predictions: Model predictions (batch_size, 9, patch_length)
            targets: Target values at masked positions (batch_size, 9, n_mask_points)
            mask_indices: Mask indices (batch_size, n_mask_points)
            original_signals: Original signals for additional constraints (optional)
            
        Returns:
            Dictionary of loss components and total loss
        """
        losses = {}
        
        # 1. Base MSE loss (only on masked positions)
        mse_loss = self._compute_mse_loss(predictions, targets, mask_indices)
        losses['mse'] = mse_loss
        
        # 2. Amplitude constraint loss
        if self.loss_config.get('use_amplitude_constraint', False) and original_signals is not None:
            amp_loss = self._compute_amplitude_constraint_loss(predictions, original_signals)
            losses['amplitude'] = amp_loss
        
        # 3. Wavelet coefficient loss
        if self.loss_config.get('use_wavelet_loss', False):
            wavelet_loss = self._compute_wavelet_loss(predictions, targets, mask_indices)
            losses['wavelet'] = wavelet_loss
        
        # 4. Frequency domain loss
        if self.loss_config.get('use_frequency_loss', False):
            freq_loss = self._compute_frequency_loss(predictions, targets, mask_indices)
            losses['frequency'] = freq_loss
        
        # 5. R-wave preservation loss
        if self.loss_config.get('use_r_wave_preservation', False):
            r_wave_loss = self._compute_r_wave_loss(predictions, targets, mask_indices)
            losses['r_wave'] = r_wave_loss
        
        # Combine losses
        total_loss = torch.tensor(0.0, device=predictions.device)
        for loss_name, loss_value in losses.items():
            if loss_name in self.loss_weights:
                weight = self.loss_weights[loss_name]
                total_loss += weight * loss_value
        
        losses['total'] = total_loss
        return losses
    
    def _compute_mse_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                         mask_indices: torch.Tensor) -> torch.Tensor:
        """
        Compute MSE loss only on masked positions
        
        Args:
            predictions: Model predictions (batch_size, 9, patch_length)
            targets: Target values at masked positions (batch_size, 9, n_mask_points)
            mask_indices: Mask indices (batch_size, n_mask_points)
            
        Returns:
            MSE loss
        """
        batch_size, n_channels, _ = predictions.shape
        n_mask_points = mask_indices.shape[1]
        
        # Extract predictions at masked positions
        pred_at_mask = torch.zeros(batch_size, n_channels, n_mask_points, device=predictions.device)
        
        for b in range(batch_size):
            for c in range(n_channels):
                pred_at_mask[b, c, :] = predictions[b, c, mask_indices[b]]
        
        # Compute MSE loss
        loss = self.mse_loss(pred_at_mask, targets)
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss
    
    def _compute_amplitude_constraint_loss(self, predictions: torch.Tensor, 
                                         original_signals: torch.Tensor) -> torch.Tensor:
        """
        Compute amplitude constraint loss to prevent over-enhancement
        
        Args:
            predictions: Model predictions (batch_size, 9, patch_length)
            original_signals: Original signals (batch_size, 9, patch_length)
            
        Returns:
            Amplitude constraint loss
        """
        scale_factor = self.loss_config.get('amplitude_scale_factor', 1.2)
        
        # Calculate original signal statistics
        orig_max = torch.max(torch.abs(original_signals), dim=2, keepdim=True)[0]
        orig_std = torch.std(original_signals, dim=2, keepdim=True)
        
        # Define reasonable amplitude bounds
        upper_bound = orig_max * scale_factor
        std_bound = orig_std * scale_factor
        
        # Compute violations
        pred_max = torch.max(torch.abs(predictions), dim=2, keepdim=True)[0]
        pred_std = torch.std(predictions, dim=2, keepdim=True)
        
        # Penalty for exceeding bounds
        max_violation = F.relu(pred_max - upper_bound)
        std_violation = F.relu(pred_std - std_bound)
        
        # Combine violations
        amplitude_loss = max_violation.mean() + std_violation.mean()
        
        return amplitude_loss
    
    def _compute_wavelet_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                            mask_indices: torch.Tensor) -> torch.Tensor:
        """
        Compute wavelet coefficient loss to preserve signal structure
        
        Args:
            predictions: Model predictions (batch_size, 9, patch_length)
            targets: Target values at masked positions (batch_size, 9, n_mask_points)
            mask_indices: Mask indices (batch_size, n_mask_points)
            
        Returns:
            Wavelet coefficient loss
        """
        if not hasattr(self, 'pywt'):
            return torch.tensor(0.0, device=predictions.device)
        
        batch_size, n_channels, patch_length = predictions.shape
        wavelet_loss = torch.tensor(0.0, device=predictions.device)
        
        # Reconstruct full predictions (fill masked positions)
        full_predictions = predictions.clone()
        for b in range(batch_size):
            for c in range(n_channels):
                full_predictions[b, c, mask_indices[b]] = targets[b, c, :]
        
        # Compute wavelet loss for each sample and channel
        for b in range(batch_size):
            for c in range(n_channels):
                # Get signals (detach from computation graph)
                pred_signal = full_predictions[b, c].detach().cpu().numpy()
                
                # Reconstruct original signal
                orig_signal = predictions[b, c].detach().cpu().numpy()
                orig_signal[mask_indices[b].cpu().numpy()] = targets[b, c].detach().cpu().numpy()
                
                try:
                    # Wavelet decomposition
                    coeffs_pred = self.pywt.wavedec(pred_signal, self.wavelet_name, 
                                                   level=self.wavelet_levels)
                    coeffs_orig = self.pywt.wavedec(orig_signal, self.wavelet_name, 
                                                   level=self.wavelet_levels)
                    
                    # Focus on approximation coefficients (low-frequency components)
                    # These represent the main signal structure
                    approx_pred = torch.tensor(coeffs_pred[0], device=predictions.device)
                    approx_orig = torch.tensor(coeffs_orig[0], device=predictions.device)
                    
                    # Compute MSE on approximation coefficients
                    wavelet_loss += F.mse_loss(approx_pred, approx_orig)
                    
                    # Optional: Add penalty for detail coefficients (high-frequency)
                    # This encourages noise removal
                    for i in range(1, min(len(coeffs_pred), 3)):  # First 2 detail levels
                        detail_pred = torch.tensor(coeffs_pred[i], device=predictions.device)
                        detail_loss = torch.mean(detail_pred ** 2)  # Penalty for high-frequency content
                        wavelet_loss += 0.1 * detail_loss
                
                except Exception as e:
                    logger.warning(f"Wavelet decomposition failed: {e}")
                    continue
        
        # Normalize by batch size and channels
        wavelet_loss = wavelet_loss / (batch_size * n_channels)
        
        return wavelet_loss
    
    def _compute_frequency_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                              mask_indices: torch.Tensor) -> torch.Tensor:
        """
        Compute frequency domain loss to preserve spectral characteristics
        
        Args:
            predictions: Model predictions (batch_size, 9, patch_length)
            targets: Target values at masked positions (batch_size, 9, n_mask_points)
            mask_indices: Mask indices (batch_size, n_mask_points)
            
        Returns:
            Frequency domain loss
        """
        batch_size, n_channels, patch_length = predictions.shape
        freq_loss = torch.tensor(0.0, device=predictions.device)
        
        # Reconstruct full predictions
        full_predictions = predictions.clone()
        for b in range(batch_size):
            for c in range(n_channels):
                full_predictions[b, c, mask_indices[b]] = targets[b, c, :]
        
        # Compute FFT
        pred_fft = torch.fft.fft(full_predictions, dim=2)
        
        # Focus on different frequency bands
        freqs = torch.fft.fftfreq(patch_length, 1/self.sampling_rate)
        
        # Heart rate band (0.5-3 Hz) - should be preserved
        heart_rate_mask = (torch.abs(freqs) >= 0.5) & (torch.abs(freqs) <= 3.0)
        
        # High frequency band (>50 Hz) - should be attenuated
        high_freq_mask = torch.abs(freqs) > 50
        
        # Reconstruct original FFT
        orig_predictions = predictions.clone()
        for b in range(batch_size):
            for c in range(n_channels):
                orig_predictions[b, c, mask_indices[b]] = targets[b, c, :]
        
        orig_fft = torch.fft.fft(orig_predictions, dim=2)
        
        # Loss for heart rate preservation
        if heart_rate_mask.any():
            heart_rate_loss = F.mse_loss(
                torch.abs(pred_fft[:, :, heart_rate_mask]),
                torch.abs(orig_fft[:, :, heart_rate_mask])
            )
            freq_loss += heart_rate_loss
        
        # Loss for high frequency attenuation
        if high_freq_mask.any():
            high_freq_power = torch.mean(torch.abs(pred_fft[:, :, high_freq_mask]) ** 2)
            freq_loss += 0.1 * high_freq_power
        
        return freq_loss
    
    def _compute_r_wave_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                           mask_indices: torch.Tensor) -> torch.Tensor:
        """
        Compute R-wave preservation loss
        
        Args:
            predictions: Model predictions (batch_size, 9, patch_length)
            targets: Target values at masked positions (batch_size, 9, n_mask_points)
            mask_indices: Mask indices (batch_size, n_mask_points)
            
        Returns:
            R-wave preservation loss
        """
        batch_size, n_channels, patch_length = predictions.shape
        r_wave_loss = torch.tensor(0.0, device=predictions.device)
        
        # Reconstruct full predictions
        full_predictions = predictions.clone()
        for b in range(batch_size):
            for c in range(n_channels):
                full_predictions[b, c, mask_indices[b]] = targets[b, c, :]
        
        # Simple R-wave detection based on peaks
        # This is a simplified implementation
        for b in range(batch_size):
            for c in range(n_channels):
                pred_signal = full_predictions[b, c]
                
                # Reconstruct original
                orig_signal = predictions[b, c].clone()
                orig_signal[mask_indices[b]] = targets[b, c]
                
                # Find peaks (simplified R-wave detection)
                # Use gradient-based peak detection
                pred_grad = torch.gradient(pred_signal)[0]
                orig_grad = torch.gradient(orig_signal)[0]
                
                # Focus on significant peaks
                pred_peaks = (pred_grad[:-1] > 0) & (pred_grad[1:] < 0) & (pred_signal[:-1] > 0.5 * torch.std(pred_signal))
                orig_peaks = (orig_grad[:-1] > 0) & (orig_grad[1:] < 0) & (orig_signal[:-1] > 0.5 * torch.std(orig_signal))
                
                # Compute peak preservation loss
                if orig_peaks.any():
                    # Penalty for losing peaks
                    peak_positions = torch.where(orig_peaks)[0]
                    peak_preservation = torch.mean(pred_signal[peak_positions] / (orig_signal[peak_positions] + 1e-8))
                    r_wave_loss += F.mse_loss(peak_preservation, torch.ones_like(peak_preservation))
        
        # Normalize by batch size and channels
        r_wave_loss = r_wave_loss / (batch_size * n_channels)
        
        return r_wave_loss

class EnhancedNoise2SimLoss(nn.Module):
    """
    Wrapper class for enhanced Noise2Sim loss with backward compatibility
    """
    
    def __init__(self, loss_config: Dict, sampling_rate: int = 1000):
        super().__init__()
        self.enhanced_loss = Noise2SimLoss(loss_config, sampling_rate)
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor, 
                mask_indices: torch.Tensor, original_signals: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass returning only total loss for backward compatibility
        
        Args:
            predictions: Model predictions
            targets: Target values
            mask_indices: Mask indices
            original_signals: Original signals (optional)
            
        Returns:
            Total loss
        """
        loss_dict = self.enhanced_loss(predictions, targets, mask_indices, original_signals)
        return loss_dict['total']
    
    def compute_detailed_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                            mask_indices: torch.Tensor, original_signals: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Compute detailed loss components
        
        Args:
            predictions: Model predictions
            targets: Target values
            mask_indices: Mask indices
            original_signals: Original signals (optional)
            
        Returns:
            Dictionary of loss components
        """
        return self.enhanced_loss(predictions, targets, mask_indices, original_signals)

def create_enhanced_loss(config) -> EnhancedNoise2SimLoss:
    """
    Create enhanced loss function from config
    
    Args:
        config: Configuration object
        
    Returns:
        Enhanced loss function
    """
    loss_config = config.get_loss_config()
    return EnhancedNoise2SimLoss(loss_config, config.sampling_rate)

if __name__ == "__main__":
    # Test enhanced loss function
    import torch
    
    # Create dummy data
    batch_size = 4
    n_channels = 9
    patch_length = 1024
    n_mask_points = 64
    
    predictions = torch.randn(batch_size, n_channels, patch_length)
    targets = torch.randn(batch_size, n_channels, n_mask_points)
    mask_indices = torch.randint(0, patch_length, (batch_size, n_mask_points))
    original_signals = torch.randn(batch_size, n_channels, patch_length)
    
    # Create loss function
    loss_config = {
        'use_wavelet_loss': False,  # Disable for testing without PyWavelets
        'use_amplitude_constraint': True,
        'amplitude_scale_factor': 1.2,
        'loss_weights': {
            'mse': 1.0,
            'amplitude': 0.1
        }
    }
    
    loss_fn = Noise2SimLoss(loss_config)
    
    # Test loss computation
    loss_dict = loss_fn(predictions, targets, mask_indices, original_signals)
    
    print("Enhanced loss function test:")
    for loss_name, loss_value in loss_dict.items():
        print(f"  {loss_name}: {loss_value.item():.6f}")
    
    print("\\nEnhanced loss function test completed!")