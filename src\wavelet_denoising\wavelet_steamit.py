"""
Author: <PERSON><PERSON> (Enhanced Wavelet Version)
email: <EMAIL>
file: wavelet_analyzer_optimized.py
date: 2025/7/15
desc:
多通道数据小波分解降噪分析工具，支持多种小波类型和阈值处理方法。
"""
import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
import pywt
import os
import pandas as pd
from scipy.fft import fft, fftfreq
from scipy import signal
from sklearn.metrics import mean_squared_error

# --- 初始设置 ---
try:
    plt.rcParams['font.family'] = 'SimHei'
    plt.rcParams['axes.unicode_minus'] = False
except:
    plt.rcParams['font.family'] = 'sans-serif'
    st.warning("未找到'SimHei'字体，已回退至默认字体。中文字符可能无法正常显示。")

st.set_page_config(page_title="高级小波降噪分析平台", layout="wide")

# --- 核心功能函数 ---

@st.cache_data
def load_data(file_path):
    """加载数据文件，支持不同分隔符"""
    try:
        try:
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')
        return data
    except Exception as e:
        st.error(f"读取文件出错: {e}")
        return None

@st.cache_data
def compute_spectrum(signal_data, fs, max_freq=50):
    """计算信号频谱，限制显示频率范围到max_freq Hz"""
    n = len(signal_data)
    frequencies = fftfreq(n, 1/fs)
    spectrum = np.abs(fft(signal_data))

    # 只保留正频率部分且在max_freq范围内
    positive_mask = (frequencies >= 0) & (frequencies <= max_freq)
    return frequencies[positive_mask], spectrum[positive_mask]

@st.cache_data
def cached_wavelet_decompose(signal_data, wavelet, mode, levels):
    """缓存小波分解结果避免重复计算"""
    return pywt.wavedec(signal_data, wavelet, mode=mode, level=levels)

def get_available_wavelets():
    """获取可用的小波类型"""
    wavelets = {}
    # Daubechies wavelets
    wavelets['Daubechies'] = [f'db{i}' for i in range(1, 21)]
    # Biorthogonal wavelets
    wavelets['Biorthogonal'] = ['bior1.1', 'bior1.3', 'bior1.5', 'bior2.2', 'bior2.4', 'bior2.6', 'bior2.8',
                               'bior3.1', 'bior3.3', 'bior3.5', 'bior3.7', 'bior3.9', 'bior4.4', 'bior5.5', 'bior6.8']
    # Coiflets wavelets
    wavelets['Coiflets'] = [f'coif{i}' for i in range(1, 18)]
    # Haar wavelet
    wavelets['Haar'] = ['haar']
    # Symlets wavelets
    wavelets['Symlets'] = [f'sym{i}' for i in range(2, 21)]
    # Other wavelets
    wavelets['Others'] = ['dmey', 'mexican_hat', 'morlet']

    return wavelets

def apply_threshold(coeffs, threshold_method, threshold_value=None, sigma=None):
    """应用阈值处理到小波系数"""
    if threshold_method == 'manual' and threshold_value is not None:
        threshold = threshold_value
    elif threshold_method == 'sure' and sigma is not None:
        threshold = pywt.threshold(coeffs[1], sigma * np.sqrt(2 * np.log(len(coeffs[1]))), 'soft')
        return threshold
    elif threshold_method == 'minimax':
        # 使用minimax估计
        N = len(coeffs[1])
        if N >= 32:
            threshold = 0.3936 + 0.1829 * np.log2(N)
        else:
            threshold = 0
    else:
        threshold = 0

    # 应用阈值到所有细节系数
    coeffs_thresh = coeffs.copy()
    for i in range(1, len(coeffs_thresh)):
        coeffs_thresh[i] = pywt.threshold(coeffs_thresh[i], threshold, 'soft')

    return coeffs_thresh

def estimate_noise_sigma(coeffs):
    """估计噪声的标准差（使用最高频率的细节系数）"""
    return np.median(np.abs(coeffs[-1])) / 0.6745

def create_wavelet_display(coeffs, wavelet, fs, channel_num):
    """创建小波系数的显示组件"""

    st.markdown("**📊 小波系数分析**")

    # 系数概览
    with st.expander("🔍 各层系数概览", expanded=True):
        fig_coeffs, axes = plt.subplots(len(coeffs), 1, figsize=(12, 2*len(coeffs)))
        if len(coeffs) == 1:
            axes = [axes]

        # 显示近似系数
        time_axis = np.arange(len(coeffs[0])) / fs
        axes[0].plot(time_axis, coeffs[0], 'b-', linewidth=1)
        axes[0].set_title('近似系数 (A)')
        axes[0].grid(True, alpha=0.3)

        # 显示细节系数
        for i in range(1, len(coeffs)):
            time_axis = np.arange(len(coeffs[i])) / fs
            axes[i].plot(time_axis, coeffs[i], 'r-', linewidth=1)
            axes[i].set_title(f'细节系数 D{i}')
            axes[i].grid(True, alpha=0.3)

        axes[-1].set_xlabel('时间 (s)')
        plt.tight_layout()
        st.pyplot(fig_coeffs, use_container_width=True)

def calculate_metrics(original, denoised):
    """计算降噪效果指标"""
    mse = mean_squared_error(original, denoised)
    rmse = np.sqrt(mse)

    # 信噪比改善 (SNR improvement)
    signal_power = np.var(original)
    noise_power = np.var(original - denoised)
    snr_improvement = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else float('inf')

    # 相关系数
    correlation = np.corrcoef(original, denoised)[0, 1]

    return {
        'MSE': mse,
        'RMSE': rmse,
        'SNR_improvement': snr_improvement,
        'Correlation': correlation
    }

# --- 初始化 Session State ---
def initialize_session_state():
    """初始化会话状态"""
    if 'run_analysis' not in st.session_state:
        st.session_state.run_analysis = False
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = {}
    if 'processed_signals' not in st.session_state:
        st.session_state.processed_signals = {}

# --- Streamlit 应用主函数 ---
def main():
    initialize_session_state()
    st.title("🌊 高级小波降噪分析平台")

    # 自定义CSS样式
    st.markdown("""
        <style>
        .metric-container {
            padding: 10px;
            border-radius: 8px;
            background-color: #f0f8ff;
            margin: 5px 0;
        }
        .wavelet-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .stExpander > div:first-child {
            background-color: #f0f0f0;
        }
        </style>
    """, unsafe_allow_html=True)

    st.markdown("---")

    # --- 侧边栏 ---
    with st.sidebar:
        st.header("📁 1. 数据选择")
        default_path = r"D:\data\去噪长周期\降噪北京301的标准源2025-7-14\20250711标准源"
        data_path = st.text_input("数据目录路径", value=default_path)

        full_file_path, data = None, None
        if os.path.exists(data_path):
            files = [f for f in os.listdir(data_path) if
                     f.endswith('.txt') and os.path.isfile(os.path.join(data_path, f))]
            if files:
                selected_file = st.selectbox("选择数据文件", options=files)
                full_file_path = os.path.join(data_path, selected_file)
                data = load_data(full_file_path)
            else:
                st.warning(f"在 {data_path} 中未找到.txt文件")
        else:
            st.warning(f"目录 {data_path} 不存在")

        if data is not None:
            st.success(f"✅ 已加载: {os.path.basename(full_file_path)}\n📊 形状: {data.shape}")

            st.header("⚙️ 2. 分析设置")
            all_channels = list(range(1, data.shape[1] + 1))
            selected_channels = st.multiselect("选择分析通道 (可多选)", options=all_channels, default=all_channels[0:1])

            segment_length = st.number_input("分析长度（点数）", min_value=1000, max_value=len(data), value=10000)
            max_start_pos = max(0, len(data) - segment_length)
            segment_start = st.number_input("分析起始位置（点）", min_value=0, max_value=max_start_pos, value=0)

            st.header("🌊 3. 小波参数")
            sampling_rate = st.number_input("采样率 (Hz)", min_value=1, value=1000)

            # 小波类型选择
            wavelets = get_available_wavelets()
            wavelet_family = st.selectbox("小波族", list(wavelets.keys()))
            wavelet_name = st.selectbox("小波类型", wavelets[wavelet_family])

            # 边界模式
            boundary_modes = ['symmetric', 'zero', 'constant', 'periodic']
            boundary_mode = st.selectbox("边界处理模式", boundary_modes)

            # 分解层数
            max_levels = pywt.dwt_max_level(segment_length, wavelet_name)
            decomp_levels = st.number_input("分解层数", min_value=1, max_value=max_levels, value=min(6, max_levels))

            st.header("🎯 4. 阈值处理")
            threshold_methods = ['manual', 'sure', 'minimax']
            threshold_method = st.selectbox("阈值选择方法", threshold_methods)

            if threshold_method == 'manual':
                threshold_value = st.number_input("手动阈值", min_value=0.0, value=0.1, step=0.01, format="%.3f")
            else:
                threshold_value = None

            # 阈值类型
            threshold_types = ['soft', 'hard']
            threshold_type = st.selectbox("阈值类型", threshold_types)

            st.header("🚀 5. 执行分解")
            if st.button("🚀 执行小波分解", disabled=not selected_channels, type="primary"):
                st.session_state.run_analysis = True
                st.session_state.analysis_results = {}
                st.session_state.processed_signals = {}

                progress_bar = st.progress(0, text="小波分解进度...")
                with st.spinner("正在对所有选定通道进行小波分解..."):
                    for i, channel in enumerate(selected_channels):
                        signal_to_process = data[segment_start: segment_start + segment_length, channel - 1]

                        # 小波分解
                        coeffs = cached_wavelet_decompose(signal_to_process, wavelet_name, boundary_mode, decomp_levels)

                        # 估计噪声标准差
                        sigma = estimate_noise_sigma(coeffs)

                        st.session_state.analysis_results[channel] = {
                            'coeffs': coeffs,
                            'original_segment': signal_to_process,
                            'wavelet': wavelet_name,
                            'levels': decomp_levels,
                            'mode': boundary_mode,
                            'sigma': sigma,
                            'fs': sampling_rate
                        }
                        st.session_state.processed_signals[channel] = {}
                        progress_bar.progress((i + 1) / len(selected_channels),
                                            text=f"通道 {channel} 分解完成")
                st.success("✅ 所有选定通道的小波分解完成！")
                st.rerun()

        elif full_file_path:
            st.error("❌ 数据加载失败，请检查文件。")

    # 主界面
    if not st.session_state.run_analysis or not st.session_state.analysis_results:
        st.info("👈 请在左侧侧边栏配置参数并开始分析。")
        return

    analyzed_channels = list(st.session_state.analysis_results.keys())
    tabs = st.tabs([f"📈 通道 {ch}" for ch in analyzed_channels])

    for i, tab in enumerate(tabs):
        with tab:
            channel_num = analyzed_channels[i]
            channel_results = st.session_state.analysis_results[channel_num]
            coeffs = channel_results['coeffs']
            original_segment = channel_results['original_segment']
            wavelet = channel_results['wavelet']
            levels = channel_results['levels']
            mode = channel_results['mode']
            sigma = channel_results['sigma']
            fs = channel_results['fs']

            time_axis = np.arange(len(original_segment)) / fs

            st.header(f"📊 通道 {channel_num} 小波分析结果")

            # 1. 原始信号显示
            with st.container():
                st.subheader("📈 原始信号片段")
                col1, col2 = st.columns([1, 1])

                with col1:
                    st.markdown("**时域信号**")
                    fig_orig, ax_orig = plt.subplots(figsize=(8, 3))
                    ax_orig.plot(time_axis, original_segment, 'k-', linewidth=1)
                    ax_orig.grid(True, alpha=0.3)
                    ax_orig.set_xlabel("时间 (s)")
                    ax_orig.set_ylabel("幅值")
                    ax_orig.set_title(f"通道 {channel_num} 原始信号")
                    plt.tight_layout()
                    st.pyplot(fig_orig, use_container_width=True)

                with col2:
                    st.markdown("**频域分析 (≤50Hz)**")
                    freq, spectrum = compute_spectrum(original_segment, fs)
                    fig_freq, ax_freq = plt.subplots(figsize=(8, 3))
                    ax_freq.plot(freq, spectrum, 'k-', linewidth=1)
                    ax_freq.grid(True, alpha=0.3)
                    ax_freq.set_xlabel("频率 (Hz)")
                    ax_freq.set_ylabel("幅值")
                    ax_freq.set_title("原始信号频谱")
                    plt.tight_layout()
                    st.pyplot(fig_freq, use_container_width=True)

            st.markdown("---")

            # 2. 小波分解结果
            st.subheader("🌊 小波分解结果")

            # 显示小波信息
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("小波类型", wavelet)
            with col2:
                st.metric("分解层数", levels)
            with col3:
                st.metric("噪声估计σ", f"{sigma:.4f}")
            with col4:
                st.metric("边界模式", mode)

            # 小波系数显示
            create_wavelet_display(coeffs, wavelet, fs, channel_num)

            st.markdown("---")

            # 3. 降噪处理控制
            st.subheader("🎯 小波降噪处理")

            with st.form(f"denoising_form_{channel_num}"):
                st.markdown("**配置降噪参数：**")

                col1, col2, col3 = st.columns([2, 2, 2])

                with col1:
                    denoise_method = st.selectbox("阈值方法", ['manual', 'sure', 'minimax'],
                                                key=f"denoise_method_{channel_num}")

                with col2:
                    if denoise_method == 'manual':
                        manual_threshold = st.number_input("阈值大小", value=sigma*2, step=0.001, format="%.4f",
                                                         key=f"manual_thresh_{channel_num}")
                    else:
                        manual_threshold = None
                        st.info("自动阈值选择")

                with col3:
                    denoise_type = st.selectbox("阈值类型", ['soft', 'hard'],
                                              key=f"denoise_type_{channel_num}")

                # 高级选项
                with st.expander("🔧 高级选项"):
                    preserve_approximation = st.checkbox("保留近似系数", value=True,
                                                       key=f"preserve_app_{channel_num}")

                    # 层级选择性处理
                    st.markdown("**选择处理的分解层级：**")
                    level_selections = []
                    cols = st.columns(min(levels, 6))
                    for level in range(1, levels + 1):
                        with cols[(level-1) % 6]:
                            selected = st.checkbox(f"D{level}", value=True,
                                                 key=f"level_{level}_{channel_num}")
                            level_selections.append(selected)

                if st.form_submit_button("🎯 执行小波降噪", type="primary"):
                    with st.spinner(f"通道 {channel_num} 降噪处理中..."):
                        # 复制系数
                        coeffs_denoised = [c.copy() for c in coeffs]

                        # 计算阈值
                        if denoise_method == 'manual':
                            threshold = manual_threshold
                        elif denoise_method == 'sure':
                            threshold = sigma * np.sqrt(2 * np.log(len(original_segment)))
                        elif denoise_method == 'minimax':
                            N = len(original_segment)
                            threshold = sigma * (0.3936 + 0.1829 * np.log2(N)) if N >= 32 else sigma * 0.1

                        # 应用阈值到选定层级
                        for level_idx, apply_threshold_flag in enumerate(level_selections):
                            if apply_threshold_flag and level_idx + 1 < len(coeffs_denoised):
                                coeffs_denoised[level_idx + 1] = pywt.threshold(
                                    coeffs_denoised[level_idx + 1],
                                    threshold,
                                    denoise_type
                                )

                        # 重构信号
                        denoised_signal = pywt.waverec(coeffs_denoised, wavelet, mode=mode)

                        # 确保长度一致
                        if len(denoised_signal) != len(original_segment):
                            denoised_signal = denoised_signal[:len(original_segment)]

                        # 计算评估指标
                        metrics = calculate_metrics(original_segment, denoised_signal)

                        st.session_state.processed_signals[channel_num] = {
                            'denoised': denoised_signal,
                            'coeffs_denoised': coeffs_denoised,
                            'threshold': threshold,
                            'method': denoise_method,
                            'type': denoise_type,
                            'metrics': metrics
                        }

                    st.success("✅ 小波降噪完成！")
                    st.rerun()

            # 4. 降噪结果显示
            processed_info = st.session_state.processed_signals[channel_num]
            if 'denoised' in processed_info:
                st.markdown("---")
                st.subheader("📊 降噪效果分析")

                denoised_signal = processed_info['denoised']
                metrics = processed_info['metrics']

                # 显示评估指标
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("均方误差 (MSE)", f"{metrics['MSE']:.6f}")
                with col2:
                    st.metric("均方根误差 (RMSE)", f"{metrics['RMSE']:.4f}")
                with col3:
                    st.metric("SNR 改善 (dB)", f"{metrics['SNR_improvement']:.2f}")
                with col4:
                    st.metric("相关系数", f"{metrics['Correlation']:.4f}")

                # 时域对比
                col1, col2 = st.columns([1, 1])

                with col1:
                    st.markdown("**时域信号对比**")
                    fig_comp, ax_comp = plt.subplots(figsize=(8, 4))
                    ax_comp.plot(time_axis, original_segment, 'k-', alpha=0.7,
                               linewidth=1, label='原始信号')
                    ax_comp.plot(time_axis, denoised_signal, 'r-',
                               linewidth=1.5, label='降噪信号')
                    ax_comp.legend()
                    ax_comp.grid(True, alpha=0.3)
                    ax_comp.set_xlabel("时间 (s)")
                    ax_comp.set_ylabel("幅值")
                    ax_comp.set_title("时域对比")
                    plt.tight_layout()
                    st.pyplot(fig_comp, use_container_width=True)

                with col2:
                    st.markdown("**频域对比 (≤50Hz)**")
                    fig_freq_comp, ax_freq_comp = plt.subplots(figsize=(8, 4))

                    freq_orig, spectrum_orig = compute_spectrum(original_segment, fs)
                    freq_denoised, spectrum_denoised = compute_spectrum(denoised_signal, fs)

                    ax_freq_comp.plot(freq_orig, spectrum_orig, 'k-', alpha=0.7,
                                    linewidth=1, label='原始信号')
                    ax_freq_comp.plot(freq_denoised, spectrum_denoised, 'r-',
                                    linewidth=1.5, label='降噪信号')

                    ax_freq_comp.legend()
                    ax_freq_comp.grid(True, alpha=0.3)
                    ax_freq_comp.set_xlabel("频率 (Hz)")
                    ax_freq_comp.set_ylabel("幅值")
                    ax_freq_comp.set_title("频域对比")
                    plt.tight_layout()
                    st.pyplot(fig_freq_comp, use_container_width=True)

                # 误差分析
                st.markdown("**📉 误差分析**")
                error_signal = original_segment - denoised_signal

                col1, col2 = st.columns([1, 1])
                with col1:
                    fig_error, ax_error = plt.subplots(figsize=(8, 3))
                    ax_error.plot(time_axis, error_signal, 'g-', linewidth=1)
                    ax_error.grid(True, alpha=0.3)
                    ax_error.set_xlabel("时间 (s)")
                    ax_error.set_ylabel("误差")
                    ax_error.set_title("降噪误差 (原始 - 降噪)")
                    plt.tight_layout()
                    st.pyplot(fig_error, use_container_width=True)

                with col2:
                    # 误差统计
                    st.markdown("**误差统计**")
                    error_stats = {
                        "最大误差": np.max(np.abs(error_signal)),
                        "误差均值": np.mean(error_signal),
                        "误差标准差": np.std(error_signal),
                        "误差方差": np.var(error_signal)
                    }

                    for key, value in error_stats.items():
                        st.metric(key, f"{value:.6f}")

                # 下载结果
                st.markdown("---")
                df_to_download = pd.DataFrame({
                    'Time (s)': time_axis,
                    'Original': original_segment,
                    'Denoised': denoised_signal,
                    'Error': error_signal
                })

                csv = df_to_download.to_csv(index=False).encode('utf-8-sig')
                st.download_button(
                    label=f"📥 下载通道 {channel_num} 降噪结果 (.csv)",
                    data=csv,
                    file_name=f"wavelet_denoised_ch{channel_num}_{os.path.basename(full_file_path) if full_file_path else 'data'}.csv",
                    mime="text/csv",
                    key=f"download_btn_{channel_num}",
                    type="primary"
                )
            else:
                st.info("💡 请执行小波降噪以查看处理结果。")

if __name__ == "__main__":
    main()