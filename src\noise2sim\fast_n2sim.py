"""
Fast N2SIM implementation optimized for training speed
"""

import numpy as np
import torch
from typing import <PERSON>ple, Dict, Optional
import time
import logging
from .config import Config

logger = logging.getLogger(__name__)

class FastN2SIM:
    """
    Fast N2SIM implementation with aggressive optimizations
    """
    
    def __init__(self, config: Config):
        self.config = config
        
        # Fast configuration overrides
        self.window_size = min(config.n2sim_window_size, 64)  # Max 64 samples
        self.k_similar = min(config.n2sim_k_similar, 2)      # Max 2 similar
        self.max_candidates = 30                              # Max 30 candidates
        self.exclude_radius = min(config.n2sim_exclude_radius, 64)
        self.similarity_threshold = 0.1  # Very low threshold
        
        # Use only euclidean distance for speed
        self.distance_metric = "euclidean"
        
        # Precomputed values
        self.half_window = self.window_size // 2
        
        logger.info(f"FastN2SIM initialized: window={self.window_size}, k={self.k_similar}, candidates={self.max_candidates}")
    
    def find_similar_fast(self, signal: np.ndarray, target_idx: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Fast similarity search with aggressive optimizations
        
        Args:
            signal: Input signal (patch_length,)
            target_idx: Target index
            
        Returns:
            similar_values: Values at similar positions
            similarities: Similarity scores
        """
        signal_length = len(signal)
        
        # Early exit for edge cases
        if target_idx < self.half_window or target_idx >= signal_length - self.half_window:
            return np.array([signal[target_idx]]), np.array([1.0])
        
        # Extract target patch
        target_start = target_idx - self.half_window
        target_patch = signal[target_start:target_start + self.window_size]
        
        # Generate candidate positions with subsampling for speed
        candidates = []
        step = max(1, (signal_length - self.window_size) // self.max_candidates)
        
        for start in range(0, signal_length - self.window_size + 1, step):
            center_idx = start + self.half_window
            
            # Skip if too close to target
            if abs(center_idx - target_idx) <= self.exclude_radius:
                continue
                
            candidates.append((start, center_idx))
            
            if len(candidates) >= self.max_candidates:
                break
        
        if not candidates:
            return np.array([signal[target_idx]]), np.array([1.0])
        
        # Vectorized similarity computation
        candidate_patches = np.array([signal[start:start + self.window_size] for start, _ in candidates])
        
        # Fast euclidean distance computation
        diffs = candidate_patches - target_patch[np.newaxis, :]
        distances = np.sqrt(np.sum(diffs * diffs, axis=1))
        
        # Convert to similarities
        max_dist = np.max(distances) + 1e-8
        similarities = 1.0 - (distances / max_dist)
        
        # Filter by threshold
        valid_mask = similarities >= self.similarity_threshold
        if not np.any(valid_mask):
            return np.array([signal[target_idx]]), np.array([1.0])
        
        valid_similarities = similarities[valid_mask]
        valid_centers = np.array([center for i, (_, center) in enumerate(candidates) if valid_mask[i]])
        
        # Take top k
        k_actual = min(self.k_similar, len(valid_similarities))
        if k_actual == 0:
            return np.array([signal[target_idx]]), np.array([1.0])
        
        top_k_idx = np.argsort(valid_similarities)[-k_actual:][::-1]
        
        similar_centers = valid_centers[top_k_idx]
        similar_values = signal[similar_centers]
        similar_scores = valid_similarities[top_k_idx]
        
        return similar_values, similar_scores
    
    def create_targets_fast(self, patch: np.ndarray, mask_indices: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        Fast N2SIM target creation
        
        Args:
            patch: Input patch (9, patch_length)
            mask_indices: Mask indices
            
        Returns:
            targets: Fast N2SIM targets
            metadata: Minimal metadata
        """
        n_channels, patch_length = patch.shape
        n_mask_points = len(mask_indices)
        targets = np.zeros((n_channels, n_mask_points))
        
        start_time = time.time()
        total_similarities = 0
        
        # Process each channel
        for ch in range(n_channels):
            channel_signal = patch[ch]
            
            # Process each masked point
            for i, mask_idx in enumerate(mask_indices):
                similar_values, similarities = self.find_similar_fast(channel_signal, mask_idx)
                
                # Simple weighted average
                if len(similarities) > 1 and np.sum(similarities) > 0:
                    weights = similarities / np.sum(similarities)
                    target_value = np.sum(similar_values * weights)
                else:
                    target_value = similar_values[0] if len(similar_values) > 0 else channel_signal[mask_idx]
                
                targets[ch, i] = target_value
                total_similarities += np.sum(similarities)
        
        metadata = {
            'computation_time': time.time() - start_time,
            'avg_similarity': total_similarities / (n_channels * n_mask_points) if n_channels * n_mask_points > 0 else 0.0
        }
        
        return targets, metadata

class FastN2SIMDatasetMixin:
    """
    Fast N2SIM mixin for dataset classes
    """
    
    def __init__(self):
        if hasattr(self, 'config') and self.config.use_n2sim:
            self.fast_n2sim = FastN2SIM(self.config)
            self.fast_n2sim_stats = {
                'total_calls': 0,
                'total_time': 0.0,
                'avg_similarity': 0.0
            }
        else:
            self.fast_n2sim = None
    
    def get_fast_n2sim_targets(self, patch: np.ndarray, mask_indices: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        Get fast N2SIM targets
        
        Args:
            patch: Input patch (9, patch_length)
            mask_indices: Mask indices
            
        Returns:
            targets: Fast N2SIM targets
            metadata: Metadata
        """
        if self.fast_n2sim is None:
            # Fallback to N2V
            return patch[:, mask_indices], {}
        
        targets, metadata = self.fast_n2sim.create_targets_fast(patch, mask_indices)
        
        # Update stats
        self.fast_n2sim_stats['total_calls'] += 1
        self.fast_n2sim_stats['total_time'] += metadata['computation_time']
        
        # Rolling average of similarity
        alpha = 0.1  # Exponential moving average factor
        self.fast_n2sim_stats['avg_similarity'] = (
            (1 - alpha) * self.fast_n2sim_stats['avg_similarity'] + 
            alpha * metadata['avg_similarity']
        )
        
        return targets, metadata
    
    def get_fast_n2sim_stats(self) -> Dict:
        """Get fast N2SIM statistics"""
        if self.fast_n2sim_stats['total_calls'] > 0:
            avg_time = self.fast_n2sim_stats['total_time'] / self.fast_n2sim_stats['total_calls']
        else:
            avg_time = 0.0
            
        return {
            'total_calls': self.fast_n2sim_stats['total_calls'],
            'avg_computation_time': avg_time,
            'avg_similarity': self.fast_n2sim_stats['avg_similarity']
        }

def test_fast_n2sim():
    """Test fast N2SIM implementation"""
    print("Testing Fast N2SIM...")
    
    # Create test data
    patch_length = 2048
    n_channels = 9
    patch = np.random.randn(n_channels, patch_length)
    
    # Add some structure
    for ch in range(n_channels):
        for i in range(0, patch_length, 200):
            if i + 100 < patch_length:
                patch[ch, i:i+100] += np.sin(np.linspace(0, 4*np.pi, 100)) * (0.5 + ch*0.1)
    
    mask_indices = np.random.choice(range(100, patch_length-100), 64, replace=False)
    
    # Create config
    config = Config()
    config.use_n2sim = True
    
    # Test fast implementation
    fast_n2sim = FastN2SIM(config)
    
    # Timing test
    times = []
    for _ in range(5):
        start_time = time.time()
        targets, metadata = fast_n2sim.create_targets_fast(patch, mask_indices)
        end_time = time.time()
        times.append(end_time - start_time)
    
    avg_time = np.mean(times)
    std_time = np.std(times)
    
    print(f"Fast N2SIM performance:")
    print(f"  Average time: {avg_time*1000:.2f}±{std_time*1000:.2f}ms")
    print(f"  Target shape: {targets.shape}")
    print(f"  Average similarity: {metadata['avg_similarity']:.4f}")
    
    # Compare with original
    if avg_time < 0.05:  # Less than 50ms
        print("  ✓ Fast enough for training!")
    else:
        print("  ⚠ Still too slow for real-time training")
    
    return avg_time

if __name__ == "__main__":
    test_fast_n2sim()