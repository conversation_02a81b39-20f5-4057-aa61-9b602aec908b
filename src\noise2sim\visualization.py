"""
Enhanced visualization system for Noise2Sim training and evaluation
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
import seaborn as sns
from typing import List, Tuple, Optional, Dict, Any
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class Noise2SimVisualizer:
    """
    Enhanced visualization system for Noise2Sim
    """
    
    def __init__(self, sampling_rate: int = 1000):
        """
        Initialize visualizer
        
        Args:
            sampling_rate: Sampling rate in Hz
        """
        self.sampling_rate = sampling_rate
        plt.style.use('seaborn-v0_8')
        
    def plot_signal_comparison_all_channels(self, 
                                          original: np.ndarray, 
                                          denoised: np.ndarray,
                                          mask_indices: Optional[np.ndarray] = None,
                                          duration: int = 10,
                                          title: str = "Signal Comparison",
                                          save_path: Optional[str] = None) -> None:
        """
        Plot comparison of original vs denoised signals for all 9 channels
        
        Args:
            original: Original signal (9, patch_length)
            denoised: Denoised signal (9, patch_length)
            mask_indices: Mask indices (optional)
            duration: Duration to display in seconds
            title: Plot title
            save_path: Path to save the plot
        """
        # Calculate time axis
        n_samples = min(duration * self.sampling_rate, original.shape[1])
        time_axis = np.arange(n_samples) / self.sampling_rate
        
        # Trim signals to duration
        original = original[:, :n_samples]
        denoised = denoised[:, :n_samples]
        
        # Create figure with 3x3 grid
        fig = plt.figure(figsize=(15, 12))
        gs = GridSpec(3, 3, figure=fig, hspace=0.3, wspace=0.3)
        
        for i in range(9):
            row = i // 3
            col = i % 3
            ax = fig.add_subplot(gs[row, col])
            
            # Plot original and denoised
            ax.plot(time_axis, original[i], 'b-', alpha=0.7, linewidth=1, label='Original')
            ax.plot(time_axis, denoised[i], 'r-', alpha=0.7, linewidth=1, label='Denoised')
            
            # Plot mask points if provided
            if mask_indices is not None:
                mask_in_range = mask_indices[mask_indices < n_samples]
                if len(mask_in_range) > 0:
                    ax.scatter(time_axis[mask_in_range], 
                             original[i, mask_in_range], 
                             c='green', s=15, alpha=0.8, label='Masked points')
            
            # Styling
            ax.set_title(f'Channel {i+1}', fontsize=10)
            ax.set_xlabel('Time (s)', fontsize=8)
            ax.set_ylabel('Amplitude', fontsize=8)
            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=8)
            
            # Add legend only to first subplot
            if i == 0:
                ax.legend(fontsize=8)
        
        plt.suptitle(title, fontsize=14, fontweight='bold')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"9-channel comparison saved to {save_path}")
            plt.close(fig)  # Close figure to free memory
        else:
            plt.show()
            plt.close(fig)
    
    def plot_training_vs_test_comparison(self,
                                       train_original: np.ndarray,
                                       train_denoised: np.ndarray,
                                       test_original: np.ndarray,
                                       test_denoised: np.ndarray,
                                       channel_idx: int = 0,
                                       duration: int = 10,
                                       save_path: Optional[str] = None) -> None:
        """
        Plot training vs test set comparison
        
        Args:
            train_original: Training set original signal
            train_denoised: Training set denoised signal
            test_original: Test set original signal  
            test_denoised: Test set denoised signal
            channel_idx: Channel index to display
            duration: Duration to display in seconds
            save_path: Path to save the plot
        """
        # Calculate time axis
        n_samples = min(duration * self.sampling_rate, train_original.shape[1])
        time_axis = np.arange(n_samples) / self.sampling_rate
        
        # Trim signals
        train_original = train_original[channel_idx, :n_samples]
        train_denoised = train_denoised[channel_idx, :n_samples]
        test_original = test_original[channel_idx, :n_samples]
        test_denoised = test_denoised[channel_idx, :n_samples]
        
        # Create figure with 2x2 grid
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Training set original
        axes[0, 0].plot(time_axis, train_original, 'b-', alpha=0.7, linewidth=1)
        axes[0, 0].set_title('Training Set - Original', fontsize=12)
        axes[0, 0].set_xlabel('Time (s)')
        axes[0, 0].set_ylabel('Amplitude')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Training set denoised
        axes[0, 1].plot(time_axis, train_denoised, 'r-', alpha=0.7, linewidth=1)
        axes[0, 1].set_title('Training Set - Denoised', fontsize=12)
        axes[0, 1].set_xlabel('Time (s)')
        axes[0, 1].set_ylabel('Amplitude')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Test set original
        axes[1, 0].plot(time_axis, test_original, 'b-', alpha=0.7, linewidth=1)
        axes[1, 0].set_title('Test Set - Original', fontsize=12)
        axes[1, 0].set_xlabel('Time (s)')
        axes[1, 0].set_ylabel('Amplitude')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Test set denoised
        axes[1, 1].plot(time_axis, test_denoised, 'r-', alpha=0.7, linewidth=1)
        axes[1, 1].set_title('Test Set - Denoised', fontsize=12)
        axes[1, 1].set_xlabel('Time (s)')
        axes[1, 1].set_ylabel('Amplitude')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.suptitle(f'Training vs Test Comparison - Channel {channel_idx + 1}', 
                    fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Training vs test comparison saved to {save_path}")
            plt.close(fig)  # Close figure to free memory
        else:
            plt.show()
            plt.close(fig)
    
    def plot_mask_visualization(self,
                              original: np.ndarray,
                              masked_input: np.ndarray,
                              denoised: np.ndarray,
                              mask_indices: np.ndarray,
                              channel_idx: int = 0,
                              duration: int = 10,
                              save_path: Optional[str] = None) -> None:
        """
        Visualize the masking process and results
        
        Args:
            original: Original signal (9, patch_length)
            masked_input: Masked input signal (9, patch_length)
            denoised: Denoised signal (9, patch_length)
            mask_indices: Mask indices
            channel_idx: Channel index to display
            duration: Duration to display in seconds
            save_path: Path to save the plot
        """
        # Calculate time axis
        n_samples = min(duration * self.sampling_rate, original.shape[1])
        time_axis = np.arange(n_samples) / self.sampling_rate
        
        # Trim signals
        original = original[channel_idx, :n_samples]
        masked_input = masked_input[channel_idx, :n_samples]
        denoised = denoised[channel_idx, :n_samples]
        
        # Filter mask indices to display range
        mask_in_range = mask_indices[mask_indices < n_samples]
        
        # Create figure with 3 subplots
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        # Original signal
        axes[0].plot(time_axis, original, 'b-', alpha=0.7, linewidth=1, label='Original')
        axes[0].scatter(time_axis[mask_in_range], original[mask_in_range], 
                       c='red', s=20, alpha=0.8, label='Points to predict')
        axes[0].set_title('Original Signal with Mask Points', fontsize=12)
        axes[0].set_ylabel('Amplitude')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Masked input
        axes[1].plot(time_axis, masked_input, 'g-', alpha=0.7, linewidth=1, label='Masked Input')
        axes[1].scatter(time_axis[mask_in_range], masked_input[mask_in_range], 
                       c='red', s=20, alpha=0.8, label='Masked points (zeros)')
        axes[1].set_title('Masked Input Signal', fontsize=12)
        axes[1].set_ylabel('Amplitude')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # Denoised output
        axes[2].plot(time_axis, original, 'b-', alpha=0.5, linewidth=1, label='Original')
        axes[2].plot(time_axis, denoised, 'r-', alpha=0.7, linewidth=1, label='Denoised')
        axes[2].scatter(time_axis[mask_in_range], denoised[mask_in_range], 
                       c='orange', s=20, alpha=0.8, label='Predicted points')
        axes[2].set_title('Denoised Output vs Original', fontsize=12)
        axes[2].set_xlabel('Time (s)')
        axes[2].set_ylabel('Amplitude')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        plt.suptitle(f'Masking Process Visualization - Channel {channel_idx + 1}', 
                    fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Mask visualization saved to {save_path}")
            plt.close(fig)  # Close figure to free memory
        else:
            plt.show()
            plt.close(fig)
    
    def plot_training_progress(self,
                             train_losses: Dict[int, List[float]],
                             val_losses: Dict[int, List[float]],
                             save_path: Optional[str] = None) -> None:
        """
        Plot training progress for all 4 groups
        
        Args:
            train_losses: Training losses for each group
            val_losses: Validation losses for each group
            save_path: Path to save the plot
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        for group_idx in range(4):
            ax = axes[group_idx]
            
            if group_idx in train_losses and group_idx in val_losses:
                epochs = range(1, len(train_losses[group_idx]) + 1)
                
                ax.plot(epochs, train_losses[group_idx], 'b-', 
                       label='Training Loss', linewidth=2)
                ax.plot(epochs, val_losses[group_idx], 'r-', 
                       label='Validation Loss', linewidth=2)
                
                ax.set_title(f'Group {group_idx + 1} Training Progress', fontsize=12)
                ax.set_xlabel('Epoch')
                ax.set_ylabel('Loss')
                ax.legend()
                ax.grid(True, alpha=0.3)
                
                # Add best loss annotation
                if val_losses[group_idx]:
                    best_epoch = np.argmin(val_losses[group_idx]) + 1
                    best_loss = min(val_losses[group_idx])
                    ax.annotate(f'Best: {best_loss:.4f}', 
                               xy=(best_epoch, best_loss),
                               xytext=(10, 10), textcoords='offset points',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                               arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        plt.suptitle('Training Progress for All Groups', fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Training progress plot saved to {save_path}")
            plt.close(fig)  # Close figure to free memory
        else:
            plt.show()
            plt.close(fig)
    
    def plot_spectral_analysis(self,
                             original: np.ndarray,
                             denoised: np.ndarray,
                             channel_idx: int = 0,
                             save_path: Optional[str] = None) -> None:
        """
        Plot spectral analysis comparison
        
        Args:
            original: Original signal (9, patch_length)
            denoised: Denoised signal (9, patch_length)
            channel_idx: Channel index to analyze
            save_path: Path to save the plot
        """
        from scipy.signal import welch
        
        # Compute PSDs
        freqs_orig, psd_orig = welch(original[channel_idx], self.sampling_rate, nperseg=1024)
        freqs_denoised, psd_denoised = welch(denoised[channel_idx], self.sampling_rate, nperseg=1024)
        
        # Create figure with 3 subplots
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        
        # Original PSD
        axes[0].semilogy(freqs_orig, psd_orig, 'b-', alpha=0.7, linewidth=1)
        axes[0].set_title('Original Signal PSD', fontsize=12)
        axes[0].set_ylabel('Power')
        axes[0].grid(True, alpha=0.3)
        axes[0].set_xlim(0, 100)
        
        # Denoised PSD
        axes[1].semilogy(freqs_denoised, psd_denoised, 'r-', alpha=0.7, linewidth=1)
        axes[1].set_title('Denoised Signal PSD', fontsize=12)
        axes[1].set_ylabel('Power')
        axes[1].grid(True, alpha=0.3)
        axes[1].set_xlim(0, 100)
        
        # Comparison
        axes[2].semilogy(freqs_orig, psd_orig, 'b-', alpha=0.7, linewidth=1, label='Original')
        axes[2].semilogy(freqs_denoised, psd_denoised, 'r-', alpha=0.7, linewidth=1, label='Denoised')
        axes[2].set_title('PSD Comparison', fontsize=12)
        axes[2].set_xlabel('Frequency (Hz)')
        axes[2].set_ylabel('Power')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        axes[2].set_xlim(0, 100)
        
        # Add frequency band annotations
        axes[2].axvspan(0, 10, alpha=0.2, color='green', label='Heart Rate')
        axes[2].axvspan(10, 50, alpha=0.2, color='blue', label='MCG Signal')
        axes[2].axvspan(50, 100, alpha=0.2, color='red', label='Noise')
        
        plt.suptitle(f'Spectral Analysis - Channel {channel_idx + 1}', 
                    fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Spectral analysis plot saved to {save_path}")
            plt.close(fig)  # Close figure to free memory
        else:
            plt.show()
            plt.close(fig)
    
    def plot_residual_analysis(self,
                             original: np.ndarray,
                             denoised: np.ndarray,
                             save_path: Optional[str] = None) -> None:
        """
        Plot residual analysis
        
        Args:
            original: Original signal (9, patch_length)
            denoised: Denoised signal (9, patch_length)
            save_path: Path to save the plot
        """
        residual = original - denoised
        
        # Create figure with 2x2 grid
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Residual time series (channel 0)
        time_axis = np.arange(residual.shape[1]) / self.sampling_rate
        axes[0, 0].plot(time_axis, residual[0], 'g-', alpha=0.7, linewidth=1)
        axes[0, 0].set_title('Residual Time Series (Channel 1)', fontsize=12)
        axes[0, 0].set_xlabel('Time (s)')
        axes[0, 0].set_ylabel('Amplitude')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Residual histogram
        axes[0, 1].hist(residual.flatten(), bins=50, alpha=0.7, density=True, color='orange')
        axes[0, 1].set_title('Residual Distribution', fontsize=12)
        axes[0, 1].set_xlabel('Residual Value')
        axes[0, 1].set_ylabel('Density')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Channel-wise residual statistics
        channel_rmse = np.sqrt(np.mean(residual ** 2, axis=1))
        axes[1, 0].bar(range(1, 10), channel_rmse, alpha=0.7, color='purple')
        axes[1, 0].set_title('Channel-wise RMSE', fontsize=12)
        axes[1, 0].set_xlabel('Channel')
        axes[1, 0].set_ylabel('RMSE')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Residual correlation matrix
        residual_corr = np.corrcoef(residual)
        im = axes[1, 1].imshow(residual_corr, cmap='coolwarm', vmin=-1, vmax=1)
        axes[1, 1].set_title('Residual Correlation Matrix', fontsize=12)
        axes[1, 1].set_xlabel('Channel')
        axes[1, 1].set_ylabel('Channel')
        plt.colorbar(im, ax=axes[1, 1])
        
        plt.suptitle('Residual Analysis', fontsize=14, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Residual analysis plot saved to {save_path}")
            plt.close(fig)  # Close figure to free memory
        else:
            plt.show()
            plt.close(fig)

def create_enhanced_evaluation_plots(original: np.ndarray,
                                   denoised: np.ndarray,
                                   mask_indices: Optional[np.ndarray] = None,
                                   train_original: Optional[np.ndarray] = None,
                                   train_denoised: Optional[np.ndarray] = None,
                                   save_dir: str = "enhanced_visualization",
                                   sampling_rate: int = 1000) -> None:
    """
    Create comprehensive evaluation plots
    
    Args:
        original: Original signal (9, patch_length)
        denoised: Denoised signal (9, patch_length)
        mask_indices: Mask indices (optional)
        train_original: Training set original signal (optional)
        train_denoised: Training set denoised signal (optional)
        save_dir: Directory to save plots
        sampling_rate: Sampling rate
    """
    os.makedirs(save_dir, exist_ok=True)
    
    visualizer = Noise2SimVisualizer(sampling_rate)
    
    # 1. All channels comparison
    visualizer.plot_signal_comparison_all_channels(
        original, denoised, mask_indices,
        save_path=os.path.join(save_dir, "all_channels_comparison.png")
    )
    
    # 2. Mask visualization
    if mask_indices is not None:
        # Reconstruct masked input
        masked_input = original.copy()
        masked_input[:, mask_indices] = 0
        
        visualizer.plot_mask_visualization(
            original, masked_input, denoised, mask_indices,
            save_path=os.path.join(save_dir, "mask_visualization.png")
        )
    
    # 3. Training vs test comparison
    if train_original is not None and train_denoised is not None:
        visualizer.plot_training_vs_test_comparison(
            train_original, train_denoised, original, denoised,
            save_path=os.path.join(save_dir, "train_vs_test_comparison.png")
        )
    
    # 4. Spectral analysis
    visualizer.plot_spectral_analysis(
        original, denoised,
        save_path=os.path.join(save_dir, "spectral_analysis.png")
    )
    
    # 5. Residual analysis
    visualizer.plot_residual_analysis(
        original, denoised,
        save_path=os.path.join(save_dir, "residual_analysis.png")
    )
    
    logger.info(f"Enhanced evaluation plots saved to {save_dir}")

def plot_long_signal_denoising(original_signal: np.ndarray,
                               denoised_signal: np.ndarray,
                               duration: Optional[int] = None,
                               sampling_rate: int = 1000,
                               start_offset: int = 0,
                               channel_subset: Optional[List[int]] = None,
                               title: str = "Long Signal Denoising Comparison",
                               save_path: Optional[str] = None) -> None:
    """
    Plot denoising comparison for long signals (vis_signal_duration length)
    
    Args:
        original_signal: Original signal (samples, 36)
        denoised_signal: Denoised signal (samples, 36)
        duration: Duration to display in seconds (if None, uses vis_signal_duration from config)
        sampling_rate: Sampling rate in Hz
        start_offset: Start offset in samples
        channel_subset: List of channel indices to display (if None, shows first 9 channels)
        title: Plot title
        save_path: Path to save the plot
    """
    # Default parameters
    if duration is None:
        duration = 10  # Default 10 seconds
    if channel_subset is None:
        channel_subset = list(range(9))  # First 9 channels
    
    # Calculate time range
    n_samples = min(duration * sampling_rate, original_signal.shape[0] - start_offset)
    end_idx = start_offset + n_samples
    
    # Extract signal segment
    original_segment = original_signal[start_offset:end_idx, channel_subset]
    denoised_segment = denoised_signal[start_offset:end_idx, channel_subset]
    
    # Create time axis
    time_axis = np.arange(n_samples) / sampling_rate
    
    # Create figure with 3x3 grid for 9 channels
    fig = plt.figure(figsize=(15, 12))
    gs = GridSpec(3, 3, figure=fig, hspace=0.3, wspace=0.3)
    
    n_channels = len(channel_subset)
    for i in range(min(9, n_channels)):
        row = i // 3
        col = i % 3
        ax = fig.add_subplot(gs[row, col])
        
        # Plot original and denoised
        ax.plot(time_axis, original_segment[:, i], 'b-', alpha=0.7, linewidth=1, label='Original')
        ax.plot(time_axis, denoised_segment[:, i], 'r-', alpha=0.7, linewidth=1, label='Denoised')
        
        # Styling
        channel_idx = channel_subset[i]
        ax.set_title(f'Channel {channel_idx + 1}', fontsize=10)
        ax.set_xlabel('Time (s)', fontsize=8)
        ax.set_ylabel('Amplitude', fontsize=8)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=8)
        
        # Add legend only to first subplot
        if i == 0:
            ax.legend(fontsize=8)
    
    plt.suptitle(f'{title} ({duration}s segment)', fontsize=14, fontweight='bold')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Long signal visualization saved to {save_path}")
        plt.close(fig)
    else:
        plt.show()
        plt.close(fig)

def plot_long_signal_with_inference(file_path: str,
                                   model_paths: Dict[int, str],
                                   config,
                                   duration: Optional[int] = None,
                                   start_offset: int = 0,
                                   channel_subset: Optional[List[int]] = None,
                                   save_path: Optional[str] = None) -> None:
    """
    Load a signal file, perform inference, and visualize the long signal denoising
    
    Args:
        file_path: Path to input signal file
        model_paths: Dictionary mapping group_idx to model checkpoint path
        config: Configuration object
        duration: Duration to display in seconds (if None, uses vis_signal_duration from config)
        start_offset: Start offset in samples
        channel_subset: List of channel indices to display (if None, shows first 9 channels)
        save_path: Path to save the plot
    """
    from .inference import Noise2SimInference
    from .utils import load_mcg_data
    
    # Use config duration if not specified
    if duration is None:
        duration = config.vis_signal_duration
    
    # Load original signal
    signal_channels, reference_channels = load_mcg_data(file_path)
    
    # Create inference pipeline
    inference = Noise2SimInference(config, model_paths)
    
    # Perform denoising
    denoised_signal = inference.denoise_signal(signal_channels, reference_channels)
    
    # Create visualization
    plot_long_signal_denoising(
        signal_channels,
        denoised_signal,
        duration=duration,
        sampling_rate=config.sampling_rate,
        start_offset=start_offset,
        channel_subset=channel_subset,
        title=f"Long Signal Denoising - {os.path.basename(file_path)}",
        save_path=save_path
    )

def create_long_signal_evaluation_plots(file_path: str,
                                       model_paths: Dict[int, str],
                                       config,
                                       save_dir: str = "long_signal_visualization",
                                       n_segments: int = 3) -> None:
    """
    Create comprehensive long signal evaluation plots with multiple segments
    
    Args:
        file_path: Path to input signal file
        model_paths: Dictionary mapping group_idx to model checkpoint path
        config: Configuration object
        save_dir: Directory to save plots
        n_segments: Number of different segments to visualize
    """
    from .inference import Noise2SimInference
    from .utils import load_mcg_data
    
    os.makedirs(save_dir, exist_ok=True)
    
    # Load original signal
    signal_channels, reference_channels = load_mcg_data(file_path)
    
    # Create inference pipeline
    inference = Noise2SimInference(config, model_paths)
    
    # Perform denoising
    denoised_signal = inference.denoise_signal(signal_channels, reference_channels)
    
    # Calculate segment parameters
    duration = config.vis_signal_duration
    signal_length = signal_channels.shape[0]
    segment_samples = duration * config.sampling_rate
    
    # Create visualizations for different segments
    for i in range(n_segments):
        if i == 0:
            # First segment - beginning of signal
            start_offset = 0
            segment_name = "beginning"
        elif i == n_segments - 1:
            # Last segment - end of signal
            start_offset = max(0, signal_length - segment_samples)
            segment_name = "end"
        else:
            # Middle segments
            start_offset = int((signal_length - segment_samples) * i / (n_segments - 1))
            segment_name = f"middle_{i}"
        
        # Create visualization for this segment
        save_path = os.path.join(save_dir, f"long_signal_denoising_{segment_name}.png")
        
        plot_long_signal_denoising(
            signal_channels,
            denoised_signal,
            duration=duration,
            sampling_rate=config.sampling_rate,
            start_offset=start_offset,
            channel_subset=list(range(9)),  # First 9 channels
            title=f"Long Signal Denoising - {segment_name.capitalize()} Segment",
            save_path=save_path
        )
    
    # Create overview plot showing the entire signal with denoising effect
    create_signal_overview_plot(
        signal_channels,
        denoised_signal,
        config.sampling_rate,
        os.path.join(save_dir, "signal_overview.png")
    )
    
    logger.info(f"Long signal evaluation plots saved to {save_dir}")

def create_signal_overview_plot(original_signal: np.ndarray,
                               denoised_signal: np.ndarray,
                               sampling_rate: int,
                               save_path: str) -> None:
    """
    Create overview plot showing the entire signal with denoising effect
    
    Args:
        original_signal: Original signal (samples, 36)
        denoised_signal: Denoised signal (samples, 36)
        sampling_rate: Sampling rate in Hz
        save_path: Path to save the plot
    """
    # Downsample for overview (show every 10th sample to reduce plot complexity)
    downsample_factor = 10
    original_downsampled = original_signal[::downsample_factor, :9]  # First 9 channels
    denoised_downsampled = denoised_signal[::downsample_factor, :9]
    
    # Create time axis
    time_axis = np.arange(original_downsampled.shape[0]) * downsample_factor / sampling_rate
    
    # Create figure with 3x3 grid
    fig = plt.figure(figsize=(15, 12))
    gs = GridSpec(3, 3, figure=fig, hspace=0.3, wspace=0.3)
    
    for i in range(9):
        row = i // 3
        col = i % 3
        ax = fig.add_subplot(gs[row, col])
        
        # Plot original and denoised
        ax.plot(time_axis, original_downsampled[:, i], 'b-', alpha=0.5, linewidth=0.5, label='Original')
        ax.plot(time_axis, denoised_downsampled[:, i], 'r-', alpha=0.7, linewidth=0.5, label='Denoised')
        
        # Styling
        ax.set_title(f'Channel {i + 1}', fontsize=10)
        ax.set_xlabel('Time (s)', fontsize=8)
        ax.set_ylabel('Amplitude', fontsize=8)
        ax.grid(True, alpha=0.3)
        ax.tick_params(labelsize=8)
        
        # Add legend only to first subplot
        if i == 0:
            ax.legend(fontsize=8)
    
    plt.suptitle('Signal Overview - Entire Recording', fontsize=14, fontweight='bold')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close(fig)
    
    logger.info(f"Signal overview plot saved to {save_path}")

if __name__ == "__main__":
    # Test visualization with synthetic data
    np.random.seed(42)
    
    # Generate synthetic 9-channel signal
    patch_length = 10000  # 10 seconds at 1kHz
    original = np.random.randn(9, patch_length) * 0.1
    
    # Add some structure (simulated heart beats)
    for i in range(9):
        for beat in range(0, patch_length, 800):  # ~1.25 Hz heart rate
            if beat < patch_length - 100:
                original[i, beat:beat+100] += np.sin(np.linspace(0, 2*np.pi, 100)) * (0.5 + i*0.1)
    
    # Create "denoised" version (simple smoothing)
    from scipy.signal import savgol_filter
    denoised = np.array([savgol_filter(original[i], 51, 3) for i in range(9)])
    
    # Create mask indices
    mask_indices = np.random.choice(patch_length, 128, replace=False)
    
    # Test visualization
    create_enhanced_evaluation_plots(
        original, denoised, mask_indices,
        save_dir="test_visualization"
    )
    
    print("Enhanced visualization test completed!")