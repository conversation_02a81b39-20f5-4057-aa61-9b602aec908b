# -*- coding: utf-8 -*-
"""
Created on Wed Feb 20 19:24:58 2019
"""
import numpy as np

def  VMD(f, alpha, tau, K, DC, init, tol):
    """
    u,u_hat,omega = VMD(f, alpha, tau, K, DC, init, tol)
    Variational mode decomposition
    Python implementation by <PERSON><PERSON><PERSON><EMAIL>
    code based on <PERSON>'s MATLAB code, available at:
    https://www.mathworks.com/matlabcentral/fileexchange/44765-variational-mode-decomposition
    Original paper:
    Dragomiretskiy, K. and <PERSON>o, D. (2014) 'Variational Mode Decomposition', 
    IEEE Transactions on Signal Processing, 62(3), pp. 531–544. doi: 10.1109/TSP.2013.2288675.
    
    
    Input and Parameters:
    ---------------------
    f       - the time domain signal (1D) to be decomposed
    alpha   - the balancing parameter of the data-fidelity constraint
    tau     - time-step of the dual ascent ( pick 0 for noise-slack )
    K       - the number of modes to be recovered
    DC      - true if the first mode is put and kept at DC (0-freq)
    init    - 0 = all omegas start at 0
                       1 = all omegas start uniformly distributed
                      2 = all omegas initialized randomly
    tol     - tolerance of convergence criterion; typically around 1e-6

    Output:
    -------
    u       - the collection of decomposed modes
    u_hat   - spectra of the modes
    omega   - estimated mode center-frequencies
    """
    
    if len(f)%2:
       f = f[:-1]

    # Period and sampling frequency of input signal
    fs = 1./len(f)
    
    ltemp = len(f)//2 
    fMirr =  np.append(np.flip(f[:ltemp],axis = 0),f)  
    fMirr = np.append(fMirr,np.flip(f[-ltemp:],axis = 0))

    # Time Domain 0 to T (of mirrored signal)
    T = len(fMirr)
    t = np.arange(1,T+1)/T  
    
    # Spectral Domain discretization
    freqs = t-0.5-(1/T)

    # Maximum number of iterations (if not converged yet, then it won't anyway)
    Niter = 500
    # For future generalizations: individual alpha for each mode
    Alpha = alpha*np.ones(K)
    
    # Construct and center f_hat
    f_hat = np.fft.fftshift((np.fft.fft(fMirr)))
    f_hat_plus = np.copy(f_hat) #copy f_hat
    f_hat_plus[:T//2] = 0

    # Initialization of omega_k
    omega_plus = np.zeros([Niter, K])


    if init == 1:
        for i in range(K):
            omega_plus[0,i] = (0.5/K)*(i)
    elif init == 2:
        omega_plus[0,:] = np.sort(np.exp(np.log(fs) + (np.log(0.5)-np.log(fs))*np.random.rand(1,K)))
    else:
        omega_plus[0,:] = 0
            
    # if DC mode imposed, set its omega to 0
    if DC:
        omega_plus[0,0] = 0
    
    # start with empty dual variables
    lambda_hat = np.zeros([Niter, len(freqs)], dtype = complex)
    
    # other inits
    uDiff = tol+np.spacing(1) # update step
    n = 0 # loop counter
    sum_uk = 0 # accumulator
    # matrix keeping track of every iterant // could be discarded for mem
    u_hat_plus = np.zeros([Niter, len(freqs), K],dtype=complex)    

    #*** Main loop for iterative updates***

    while ( uDiff > tol and  n < Niter-1 ): # not converged and below iterations limit
        # update first mode accumulator
        k = 0
        sum_uk = u_hat_plus[n,:,K-1] + sum_uk - u_hat_plus[n,:,0]
        
        # update spectrum of first mode through Wiener filter of residuals
        u_hat_plus[n+1,:,k] = (f_hat_plus - sum_uk - lambda_hat[n,:]/2)/(1.+Alpha[k]*(freqs - omega_plus[n,k])**2)
        
        # update first omega if not held at 0
        if not(DC):
            omega_plus[n+1,k] = np.dot(freqs[T//2:T],(abs(u_hat_plus[n+1, T//2:T, k])**2))/np.sum(abs(u_hat_plus[n+1,T//2:T,k])**2)

        # update of any other mode
        for k in np.arange(1,K):
            #accumulator
            sum_uk = u_hat_plus[n+1,:,k-1] + sum_uk - u_hat_plus[n,:,k]
            # mode spectrum
            u_hat_plus[n+1,:,k] = (f_hat_plus - sum_uk - lambda_hat[n,:]/2)/(1+Alpha[k]*(freqs - omega_plus[n,k])**2)
            # center frequencies
            omega_plus[n+1,k] = np.dot(freqs[T//2:T],(abs(u_hat_plus[n+1, T//2:T, k])**2))/np.sum(abs(u_hat_plus[n+1,T//2:T,k])**2)
            
        # Dual ascent
        lambda_hat[n+1,:] = lambda_hat[n,:] + tau*(np.sum(u_hat_plus[n+1,:,:],axis = 1) - f_hat_plus)
        
        # loop counter
        n = n+1
        
        # converged yet?
        uDiff = np.spacing(1)
        for i in range(K):
            uDiff = uDiff + (1/T)*np.dot((u_hat_plus[n,:,i]-u_hat_plus[n-1,:,i]),np.conj((u_hat_plus[n,:,i]-u_hat_plus[n-1,:,i])))

        uDiff = np.abs(uDiff)        
            
    #Postprocessing and cleanup
    
    #discard empty space if converged early
    Niter = np.min([Niter,n])
    omega = omega_plus[:Niter,:]
    
    idxs = np.flip(np.arange(1,T//2+1),axis = 0)
    # Signal reconstruction
    u_hat = np.zeros([T, K],dtype = complex)
    u_hat[T//2:T,:] = u_hat_plus[Niter-1,T//2:T,:]
    u_hat[idxs,:] = np.conj(u_hat_plus[Niter-1,T//2:T,:])
    u_hat[0,:] = np.conj(u_hat[-1,:])    
    
    u = np.zeros([K,len(t)])
    for k in range(K):
        u[k,:] = np.real(np.fft.ifft(np.fft.ifftshift(u_hat[:,k])))
        
    # remove mirror part
    u = u[:,T//4:3*T//4]

    # recompute spectrum
    u_hat = np.zeros([u.shape[1],K],dtype = complex)
    for k in range(K):
        u_hat[:,k]=np.fft.fftshift(np.fft.fft(u[k,:]))

    return u, u_hat, omega


import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
sns.set_style("whitegrid")

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def create_performance_comparison_bars():
    """创建三个类别性能指标对比柱状图"""

    # 性能数据
    categories = ['INOCA', 'IOCA', 'PCI术后CMVD']
    precision = [70.1, 83.9, 97.6]
    recall = [83.7, 69.0, 100.0]
    f1_score = [76.3, 75.7, 98.8]

    # 设置图形和子图
    fig, axes = plt.subplots(1, 3, figsize=(16, 6))
    fig.suptitle('类别性能指标对比', fontsize=18, fontweight='bold', y=0.98)

    # 定义颜色
    colors = ['#3498db', '#e67e22', '#27ae60']

    # 1. 精确率柱状图
    bars1 = axes[0].bar(categories, precision, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)
    axes[0].set_title('精确率 (Precision)', fontsize=14, fontweight='bold', pad=15)
    axes[0].set_ylabel('百分比 (%)', fontsize=12)
    axes[0].set_ylim(0, 105)

    # 添加数值标签
    for bar, value in zip(bars1, precision):
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width() / 2., height + 1,
                     f'{value:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')

    # 2. 召回率柱状图
    bars2 = axes[1].bar(categories, recall, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)
    axes[1].set_title('召回率 (Recall)', fontsize=14, fontweight='bold', pad=15)
    axes[1].set_ylabel('百分比 (%)', fontsize=12)
    axes[1].set_ylim(0, 105)

    for bar, value in zip(bars2, recall):
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width() / 2., height + 1,
                     f'{value:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')

    # 3. F1-Score柱状图
    bars3 = axes[2].bar(categories, f1_score, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)
    axes[2].set_title('F1-Score', fontsize=14, fontweight='bold', pad=15)
    axes[2].set_ylabel('百分比 (%)', fontsize=12)
    axes[2].set_ylim(0, 105)

    for bar, value in zip(bars3, f1_score):
        height = bar.get_height()
        axes[2].text(bar.get_x() + bar.get_width() / 2., height + 1,
                     f'{value:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')

    # 美化图表
    for ax in axes:
        ax.grid(axis='y', alpha=0.3)
        ax.set_axisbelow(True)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.5)
        ax.spines['bottom'].set_linewidth(0.5)

    plt.tight_layout()
    return fig


def create_grouped_performance_bars():
    """创建分组柱状图显示所有性能指标"""

    # 性能数据
    categories = ['INOCA', 'IOCA', 'PCI术后CMVD']
    precision = [70.1, 83.9, 97.6]
    recall = [83.7, 69.0, 100.0]
    f1_score = [76.3, 75.7, 98.8]

    # 设置柱状图位置
    x = np.arange(len(categories))
    width = 0.25

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 创建分组柱状图
    bars1 = ax.bar(x - width, precision, width, label='精确率 (Precision)',
                   color='#3498db', alpha=0.8, edgecolor='black', linewidth=1)
    bars2 = ax.bar(x, recall, width, label='召回率 (Recall)',
                   color='#e67e22', alpha=0.8, edgecolor='black', linewidth=1)
    bars3 = ax.bar(x + width, f1_score, width, label='F1-Score',
                   color='#27ae60', alpha=0.8, edgecolor='black', linewidth=1)

    # 添加数值标签
    def add_value_labels(bars, values):
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width() / 2., height + 1,
                    f'{value:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')

    add_value_labels(bars1, precision)
    add_value_labels(bars2, recall)
    add_value_labels(bars3, f1_score)

    # 设置标题和标签
    ax.set_title('类别性能指标综合对比', fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel('百分比 (%)', fontsize=12, fontweight='bold')
    ax.set_xlabel('类别', fontsize=12, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 105)

    # 添加图例
    ax.legend(loc='upper left', fontsize=11, frameon=True, fancybox=True, shadow=True)

    # 美化图表
    ax.grid(axis='y', alpha=0.3)
    ax.set_axisbelow(True)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)

    plt.tight_layout()
    return fig


def create_horizontal_performance_bars():
    """创建水平柱状图显示性能指标"""

    # 性能数据
    metrics = ['精确率', '召回率', 'F1-Score']
    class_0 = [70.1, 83.7, 76.3]
    class_1 = [83.9, 69.0, 75.7]
    class_2 = [97.6, 100.0, 98.8]

    # 设置柱状图位置
    y = np.arange(len(metrics))
    height = 0.25

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 创建水平分组柱状图
    bars1 = ax.barh(y - height, class_0, height, label='INOCA',
                    color='#3498db', alpha=0.8, edgecolor='black', linewidth=1)
    bars2 = ax.barh(y, class_1, height, label='IOCA',
                    color='#e67e22', alpha=0.8, edgecolor='black', linewidth=1)
    bars3 = ax.barh(y + height, class_2, height, label='PCI术后CMVD',
                    color='#27ae60', alpha=0.8, edgecolor='black', linewidth=1)

    # 添加数值标签
    def add_horizontal_labels(bars, values):
        for bar, value in zip(bars, values):
            width = bar.get_width()
            ax.text(width + 1, bar.get_y() + bar.get_height() / 2.,
                    f'{value:.1f}%', ha='left', va='center', fontsize=10, fontweight='bold')

    add_horizontal_labels(bars1, class_0)
    add_horizontal_labels(bars2, class_1)
    add_horizontal_labels(bars3, class_2)

    # 设置标题和标签
    ax.set_title('性能指标水平对比图', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('百分比 (%)', fontsize=12, fontweight='bold')
    ax.set_ylabel('性能指标', fontsize=12, fontweight='bold')
    ax.set_yticks(y)
    ax.set_yticklabels(metrics)
    ax.set_xlim(0, 105)

    # 添加图例
    ax.legend(loc='lower right', fontsize=11, frameon=True, fancybox=True, shadow=True)

    # 美化图表
    ax.grid(axis='x', alpha=0.3)
    ax.set_axisbelow(True)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)

    plt.tight_layout()
    return fig


def create_overall_performance_bars():
    """创建整体性能指标柱状图"""

    # 整体性能数据
    metrics = ['总体准确率', '宏平均F1-Score', 'INOCA F1', 'IOCA F1', 'PCI术后CMVD F1']
    values = [84.7, 83.6, 76.3, 75.7, 98.8]
    colors = ['#2c3e50', '#34495e', '#3498db', '#e67e22', '#27ae60']

    # 创建图形
    fig, ax = plt.subplots(figsize=(14, 8))

    # 创建柱状图
    bars = ax.bar(metrics, values, color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)

    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width() / 2., height + 1,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=12, fontweight='bold')

    # 设置标题和标签
    ax.set_title('模型整体性能指标总览', fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel('百分比 (%)', fontsize=12, fontweight='bold')
    ax.set_xlabel('性能指标', fontsize=12, fontweight='bold')
    ax.set_ylim(0, 105)

    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45, ha='right')

    # 添加平均线
    avg_line = np.mean(values)
    ax.axhline(y=avg_line, color='red', linestyle='--', alpha=0.7, linewidth=2)
    ax.text(len(metrics) - 1, avg_line + 2, f'平均值: {avg_line:.1f}%',
            ha='right', va='bottom', fontsize=10, color='red', fontweight='bold')

    # 美化图表
    ax.grid(axis='y', alpha=0.3)
    ax.set_axisbelow(True)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)

    plt.tight_layout()
    return fig


def create_confusion_matrix_heatmap():
    """创建混淆矩阵热力图"""

    # 混淆矩阵数据
    confusion_matrix = np.array([
        [82, 13, 3],  # INOCA
        [35, 78, 0],  # IOCA
        [0, 0, 122]  # PCI术后CMVD
    ])

    # 创建热力图
    fig, ax = plt.subplots(figsize=(10, 8))

    # 使用seaborn创建更美观的热力图
    sns.heatmap(confusion_matrix, annot=True, fmt='d', cmap='Blues',
                xticklabels=['INOCA', 'IOCA', 'PCI术后CMVD'],
                yticklabels=['INOCA', 'IOCA', 'PCI术后CMVD'],
                ax=ax, cbar_kws={'label': '样本数量'})

    # 设置标题和标签
    ax.set_title('混淆矩阵热力图', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('预测类别', fontsize=12, fontweight='bold')
    ax.set_ylabel('真实类别', fontsize=12, fontweight='bold')

    plt.tight_layout()
    return fig


def create_class_distribution_bars():
    """创建美观的类别分布柱状图"""

    # 类别分布数据
    class_names = ['INOCA', 'IOCA', 'PCI术后CMVD']
    sample_counts = [98, 113, 122]
    colors = ['#3498db', '#e67e22', '#27ae60']

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 创建渐变效果的柱状图
    bars = ax.bar(class_names, sample_counts, color=colors, alpha=0.8,
                  edgecolor='black', linewidth=2, capsize=5)

    # 添加数值标签
    for bar, count in zip(bars, sample_counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width() / 2., height + 1,
                f'{count}', ha='center', va='bottom', fontsize=14, fontweight='bold')

    # 添加百分比标签
    total_samples = sum(sample_counts)
    for bar, count in zip(bars, sample_counts):
        height = bar.get_height()
        percentage = count / total_samples * 100
        ax.text(bar.get_x() + bar.get_width() / 2., height / 2,
                f'{percentage:.1f}%', ha='center', va='center',
                fontsize=12, fontweight='bold', color='white')

    # 设置标题和标签
    ax.set_title('类别分布统计', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('类别', fontsize=12, fontweight='bold')
    ax.set_ylabel('样本数量', fontsize=12, fontweight='bold')

    # 设置y轴范围
    ax.set_ylim(0, max(sample_counts) * 1.15)

    # 添加总样本数信息
    ax.text(0.02, 0.98, f'总样本数: {total_samples}', transform=ax.transAxes,
            fontsize=12, fontweight='bold', verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 美化图表
    ax.grid(axis='y', alpha=0.3)
    ax.set_axisbelow(True)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)

    plt.tight_layout()
    return fig


# 使用示例
if __name__ == "__main__":
    # 1. 创建三个独立的性能指标柱状图
    fig1 = create_performance_comparison_bars()
    plt.show()

    # 2. 创建分组柱状图显示所有性能指标
    fig2 = create_grouped_performance_bars()
    plt.show()

    # 3. 创建水平柱状图
    fig3 = create_horizontal_performance_bars()
    plt.show()

    # 4. 创建整体性能指标柱状图
    fig4 = create_overall_performance_bars()
    plt.show()

    # 5. 创建混淆矩阵热力图
    fig5 = create_confusion_matrix_heatmap()
    plt.show()

    # 6. 创建类别分布柱状图
    fig6 = create_class_distribution_bars()
    plt.show()

    # 保存图片（可选）
    fig1.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
    fig2.savefig('grouped_performance.png', dpi=300, bbox_inches='tight')
    fig3.savefig('horizontal_performance.png', dpi=300, bbox_inches='tight')
    fig4.savefig('overall_performance.png', dpi=300, bbox_inches='tight')
    fig5.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
    fig6.savefig('class_distribution.png', dpi=300, bbox_inches='tight')



