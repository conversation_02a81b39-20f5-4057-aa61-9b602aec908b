"""
Evaluation metrics for Noise2Sim MCG denoising
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.signal import welch
from scipy.stats import pearsonr
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
import os
from datetime import datetime

from .utils import compute_psd, plot_signal_comparison, plot_psd_comparison

logger = logging.getLogger(__name__)

class Noise2SimEvaluator:
    """
    Comprehensive evaluation for Noise2Sim denoising
    """
    
    def __init__(self, sampling_rate: int = 1000):
        """
        Initialize evaluator
        
        Args:
            sampling_rate: Sampling rate in Hz
        """
        self.sampling_rate = sampling_rate
        self.results = {}
    
    def evaluate_signal(self, original: np.ndarray, denoised: np.ndarray,
                       reference: Optional[np.ndarray] = None) -> Dict:
        """
        Comprehensive evaluation of denoising performance
        
        Args:
            original: Original signal (samples, channels)
            denoised: Denoised signal (samples, channels)
            reference: Reference channels (samples, 4) - optional
            
        Returns:
            evaluation_results: Dictionary containing all metrics
        """
        if original.shape != denoised.shape:
            raise ValueError(f"Shape mismatch: original {original.shape}, denoised {denoised.shape}")
        
        logger.info(f"Evaluating denoising performance for signal shape: {original.shape}")
        
        results = {
            'signal_quality': self._evaluate_signal_quality(original, denoised),
            'spectral_analysis': self._evaluate_spectral_characteristics(original, denoised),
            'residual_analysis': self._evaluate_residual_characteristics(original, denoised),
            'channel_analysis': self._evaluate_channel_wise_performance(original, denoised),
            'temporal_analysis': self._evaluate_temporal_characteristics(original, denoised)
        }
        
        # Add reference channel analysis if available
        if reference is not None:
            results['reference_analysis'] = self._evaluate_reference_correlation(
                original, denoised, reference
            )
        
        return results
    
    def _evaluate_signal_quality(self, original: np.ndarray, denoised: np.ndarray) -> Dict:
        """
        Evaluate overall signal quality metrics
        
        Args:
            original: Original signal
            denoised: Denoised signal
            
        Returns:
            quality_metrics: Dictionary of quality metrics
        """
        residual = original - denoised
        
        # Signal-to-Noise Ratio improvement
        original_power = np.mean(original ** 2)
        residual_power = np.mean(residual ** 2)
        snr_improvement = 10 * np.log10(original_power / residual_power)
        
        # Root Mean Square Error
        rmse = np.sqrt(np.mean(residual ** 2))
        
        # Normalized RMSE
        nrmse = rmse / np.std(original)
        
        # Correlation coefficient
        correlation = np.corrcoef(original.flatten(), denoised.flatten())[0, 1]
        
        # Relative error
        relative_error = np.mean(np.abs(residual)) / np.mean(np.abs(original))
        
        return {
            'snr_improvement_db': snr_improvement,
            'rmse': rmse,
            'nrmse': nrmse,
            'correlation': correlation,
            'relative_error': relative_error
        }
    
    def _evaluate_spectral_characteristics(self, original: np.ndarray, denoised: np.ndarray) -> Dict:
        """
        Evaluate spectral characteristics
        
        Args:
            original: Original signal
            denoised: Denoised signal
            
        Returns:
            spectral_metrics: Dictionary of spectral metrics
        """
        # Compute PSDs for first channel
        freqs_orig, psd_orig = compute_psd(original[:, 0], self.sampling_rate)
        freqs_denoised, psd_denoised = compute_psd(denoised[:, 0], self.sampling_rate)
        
        # Spectral correlation
        spectral_corr = np.corrcoef(psd_orig, psd_denoised)[0, 1]
        
        # Power reduction in different frequency bands
        def power_in_band(psd, freqs, low_freq, high_freq):
            mask = (freqs >= low_freq) & (freqs <= high_freq)
            return np.trapz(psd[mask], freqs[mask])
        
        # Define frequency bands
        bands = {
            'low': (0, 10),      # 0-10 Hz (heart rate)
            'medium': (10, 50),  # 10-50 Hz (MCG signal)
            'high': (50, 100),   # 50-100 Hz (noise)
            'very_high': (100, 500)  # 100-500 Hz (high-frequency noise)
        }
        
        power_reduction = {}
        for band_name, (low, high) in bands.items():
            power_orig = power_in_band(psd_orig, freqs_orig, low, high)
            power_denoised = power_in_band(psd_denoised, freqs_denoised, low, high)
            power_reduction[f'{band_name}_band_reduction_db'] = \
                10 * np.log10(power_orig / power_denoised) if power_denoised > 0 else 0
        
        # Spectral distortion
        spectral_distortion = np.mean(np.abs(psd_orig - psd_denoised))
        
        return {
            'spectral_correlation': spectral_corr,
            'spectral_distortion': spectral_distortion,
            **power_reduction
        }
    
    def _evaluate_residual_characteristics(self, original: np.ndarray, denoised: np.ndarray) -> Dict:
        """
        Evaluate residual (noise) characteristics
        
        Args:
            original: Original signal
            denoised: Denoised signal
            
        Returns:
            residual_metrics: Dictionary of residual metrics
        """
        residual = original - denoised
        
        # Statistical properties
        residual_mean = np.mean(residual)
        residual_std = np.std(residual)
        residual_var = np.var(residual)
        
        # Skewness and kurtosis
        from scipy.stats import skew, kurtosis
        residual_skew = skew(residual.flatten())
        residual_kurtosis = kurtosis(residual.flatten())
        
        # Normality test (Shapiro-Wilk on sample)
        from scipy.stats import normaltest
        sample_size = min(5000, len(residual.flatten()))
        residual_sample = np.random.choice(residual.flatten(), sample_size, replace=False)
        normality_stat, normality_p = normaltest(residual_sample)
        
        # Autocorrelation of residual
        def autocorr(x, max_lag=100):
            n = len(x)
            x = x - np.mean(x)
            result = np.correlate(x, x, mode='full')
            result = result[n-1:]
            result = result / result[0]
            return result[:max_lag]
        
        residual_autocorr = autocorr(residual[:, 0])
        
        return {
            'residual_mean': residual_mean,
            'residual_std': residual_std,
            'residual_var': residual_var,
            'residual_skewness': residual_skew,
            'residual_kurtosis': residual_kurtosis,
            'normality_stat': normality_stat,
            'normality_p_value': normality_p,
            'residual_autocorr_lag1': residual_autocorr[1] if len(residual_autocorr) > 1 else 0
        }
    
    def _evaluate_channel_wise_performance(self, original: np.ndarray, denoised: np.ndarray) -> Dict:
        """
        Evaluate performance for each channel
        
        Args:
            original: Original signal
            denoised: Denoised signal
            
        Returns:
            channel_metrics: Dictionary of channel-wise metrics
        """
        n_channels = original.shape[1]
        
        channel_correlations = []
        channel_snr_improvements = []
        channel_rmse = []
        
        for ch in range(n_channels):
            # Correlation
            corr = np.corrcoef(original[:, ch], denoised[:, ch])[0, 1]
            channel_correlations.append(corr)
            
            # SNR improvement
            residual = original[:, ch] - denoised[:, ch]
            signal_power = np.mean(original[:, ch] ** 2)
            residual_power = np.mean(residual ** 2)
            snr_improvement = 10 * np.log10(signal_power / residual_power)
            channel_snr_improvements.append(snr_improvement)
            
            # RMSE
            rmse = np.sqrt(np.mean(residual ** 2))
            channel_rmse.append(rmse)
        
        return {
            'channel_correlations': channel_correlations,
            'channel_snr_improvements': channel_snr_improvements,
            'channel_rmse': channel_rmse,
            'avg_channel_correlation': np.mean(channel_correlations),
            'avg_snr_improvement': np.mean(channel_snr_improvements),
            'avg_rmse': np.mean(channel_rmse)
        }
    
    def _evaluate_temporal_characteristics(self, original: np.ndarray, denoised: np.ndarray) -> Dict:
        """
        Evaluate temporal characteristics preservation
        
        Args:
            original: Original signal
            denoised: Denoised signal
            
        Returns:
            temporal_metrics: Dictionary of temporal metrics
        """
        # Evaluate on first channel
        orig_signal = original[:, 0]
        denoised_signal = denoised[:, 0]
        
        # Peak detection and preservation
        from scipy.signal import find_peaks
        
        # Find peaks in original signal
        orig_peaks, _ = find_peaks(orig_signal, height=np.std(orig_signal))
        denoised_peaks, _ = find_peaks(denoised_signal, height=np.std(denoised_signal))
        
        # Peak preservation ratio
        peak_preservation = len(denoised_peaks) / len(orig_peaks) if len(orig_peaks) > 0 else 0
        
        # Temporal smoothness (using gradient)
        orig_gradient = np.gradient(orig_signal)
        denoised_gradient = np.gradient(denoised_signal)
        
        gradient_correlation = np.corrcoef(orig_gradient, denoised_gradient)[0, 1]
        
        # Signal envelope preservation
        from scipy.signal import hilbert
        orig_envelope = np.abs(hilbert(orig_signal))
        denoised_envelope = np.abs(hilbert(denoised_signal))
        
        envelope_correlation = np.corrcoef(orig_envelope, denoised_envelope)[0, 1]
        
        return {
            'peak_preservation_ratio': peak_preservation,
            'gradient_correlation': gradient_correlation,
            'envelope_correlation': envelope_correlation,
            'num_peaks_original': len(orig_peaks),
            'num_peaks_denoised': len(denoised_peaks)
        }
    
    def _evaluate_reference_correlation(self, original: np.ndarray, denoised: np.ndarray,
                                      reference: np.ndarray) -> Dict:
        """
        Evaluate correlation with reference channels
        
        Args:
            original: Original signal
            denoised: Denoised signal  
            reference: Reference channels
            
        Returns:
            reference_metrics: Dictionary of reference correlation metrics
        """
        # Compute correlation between signal channels and reference channels
        orig_ref_corr = []
        denoised_ref_corr = []
        
        for sig_ch in range(original.shape[1]):
            for ref_ch in range(reference.shape[1]):
                # Original correlation
                orig_corr = np.corrcoef(original[:, sig_ch], reference[:, ref_ch])[0, 1]
                orig_ref_corr.append(np.abs(orig_corr))
                
                # Denoised correlation
                denoised_corr = np.corrcoef(denoised[:, sig_ch], reference[:, ref_ch])[0, 1]
                denoised_ref_corr.append(np.abs(denoised_corr))
        
        # Average correlations
        avg_orig_ref_corr = np.mean(orig_ref_corr)
        avg_denoised_ref_corr = np.mean(denoised_ref_corr)
        
        # Reference correlation improvement
        ref_corr_improvement = avg_orig_ref_corr - avg_denoised_ref_corr
        
        return {
            'avg_original_ref_correlation': avg_orig_ref_corr,
            'avg_denoised_ref_correlation': avg_denoised_ref_corr,
            'reference_correlation_improvement': ref_corr_improvement
        }
    
    def generate_evaluation_report(self, results: Dict, save_dir: str,
                                 filename: str = "evaluation_report.md") -> str:
        """
        Generate comprehensive evaluation report
        
        Args:
            results: Evaluation results dictionary
            save_dir: Directory to save report
            filename: Report filename
            
        Returns:
            report_path: Path to generated report
        """
        os.makedirs(save_dir, exist_ok=True)
        report_path = os.path.join(save_dir, filename)
        
        with open(report_path, 'w') as f:
            f.write("# Noise2Sim MCG Denoising Evaluation Report\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Signal Quality Metrics
            f.write("## Signal Quality Metrics\n\n")
            quality = results['signal_quality']
            f.write(f"- **SNR Improvement**: {quality['snr_improvement_db']:.2f} dB\n")
            f.write(f"- **RMSE**: {quality['rmse']:.6f}\n")
            f.write(f"- **Normalized RMSE**: {quality['nrmse']:.6f}\n")
            f.write(f"- **Correlation**: {quality['correlation']:.6f}\n")
            f.write(f"- **Relative Error**: {quality['relative_error']:.6f}\n\n")
            
            # Spectral Analysis
            f.write("## Spectral Analysis\n\n")
            spectral = results['spectral_analysis']
            f.write(f"- **Spectral Correlation**: {spectral['spectral_correlation']:.6f}\n")
            f.write(f"- **Spectral Distortion**: {spectral['spectral_distortion']:.6f}\n")
            f.write("- **Power Reduction by Frequency Band**:\n")
            for key, value in spectral.items():
                if 'band_reduction_db' in key:
                    band_name = key.replace('_band_reduction_db', '').replace('_', ' ').title()
                    f.write(f"  - {band_name}: {value:.2f} dB\n")
            f.write("\n")
            
            # Residual Analysis
            f.write("## Residual Analysis\n\n")
            residual = results['residual_analysis']
            f.write(f"- **Residual Mean**: {residual['residual_mean']:.6f}\n")
            f.write(f"- **Residual Std**: {residual['residual_std']:.6f}\n")
            f.write(f"- **Residual Skewness**: {residual['residual_skewness']:.6f}\n")
            f.write(f"- **Residual Kurtosis**: {residual['residual_kurtosis']:.6f}\n")
            f.write(f"- **Normality Test p-value**: {residual['normality_p_value']:.6f}\n")
            f.write(f"- **Autocorrelation (lag 1)**: {residual['residual_autocorr_lag1']:.6f}\n\n")
            
            # Channel-wise Performance
            f.write("## Channel-wise Performance\n\n")
            channel = results['channel_analysis']
            f.write(f"- **Average Channel Correlation**: {channel['avg_channel_correlation']:.6f}\n")
            f.write(f"- **Average SNR Improvement**: {channel['avg_snr_improvement']:.2f} dB\n")
            f.write(f"- **Average RMSE**: {channel['avg_rmse']:.6f}\n\n")
            
            # Temporal Characteristics
            f.write("## Temporal Characteristics\n\n")
            temporal = results['temporal_analysis']
            f.write(f"- **Peak Preservation Ratio**: {temporal['peak_preservation_ratio']:.6f}\n")
            f.write(f"- **Gradient Correlation**: {temporal['gradient_correlation']:.6f}\n")
            f.write(f"- **Envelope Correlation**: {temporal['envelope_correlation']:.6f}\n")
            f.write(f"- **Original Peaks**: {temporal['num_peaks_original']}\n")
            f.write(f"- **Denoised Peaks**: {temporal['num_peaks_denoised']}\n\n")
            
            # Reference Analysis (if available)
            if 'reference_analysis' in results:
                f.write("## Reference Channel Analysis\n\n")
                ref = results['reference_analysis']
                f.write(f"- **Original-Reference Correlation**: {ref['avg_original_ref_correlation']:.6f}\n")
                f.write(f"- **Denoised-Reference Correlation**: {ref['avg_denoised_ref_correlation']:.6f}\n")
                f.write(f"- **Reference Correlation Improvement**: {ref['reference_correlation_improvement']:.6f}\n\n")
            
            f.write("## Summary\n\n")
            f.write("The Noise2Sim denoising algorithm shows:\n")
            f.write(f"- {quality['snr_improvement_db']:.1f} dB improvement in signal-to-noise ratio\n")
            f.write(f"- {quality['correlation']:.1%} correlation between original and denoised signals\n")
            f.write(f"- {spectral['high_band_reduction_db']:.1f} dB reduction in high-frequency noise\n")
            f.write(f"- {temporal['peak_preservation_ratio']:.1%} peak preservation ratio\n")
        
        logger.info(f"Evaluation report saved to {report_path}")
        return report_path
    
    def create_visualization_plots(self, original: np.ndarray, denoised: np.ndarray,
                                 results: Dict, save_dir: str):
        """
        Create visualization plots for evaluation
        
        Args:
            original: Original signal
            denoised: Denoised signal
            results: Evaluation results
            save_dir: Directory to save plots
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 1. Signal comparison
        plot_signal_comparison(
            original, denoised,
            sampling_rate=self.sampling_rate,
            channel_idx=0,
            time_range=(0, 10),
            save_path=os.path.join(save_dir, "signal_comparison.png")
        )
        
        # 2. PSD comparison
        plot_psd_comparison(
            original, denoised,
            sampling_rate=self.sampling_rate,
            channel_idx=0,
            save_path=os.path.join(save_dir, "psd_comparison.png")
        )
        
        # 3. Channel-wise performance
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        channel_results = results['channel_analysis']
        
        # Correlation per channel
        axes[0, 0].plot(channel_results['channel_correlations'], 'b-o')
        axes[0, 0].set_title('Correlation per Channel')
        axes[0, 0].set_xlabel('Channel')
        axes[0, 0].set_ylabel('Correlation')
        axes[0, 0].grid(True)
        
        # SNR improvement per channel
        axes[0, 1].plot(channel_results['channel_snr_improvements'], 'r-o')
        axes[0, 1].set_title('SNR Improvement per Channel')
        axes[0, 1].set_xlabel('Channel')
        axes[0, 1].set_ylabel('SNR Improvement (dB)')
        axes[0, 1].grid(True)
        
        # RMSE per channel
        axes[1, 0].plot(channel_results['channel_rmse'], 'g-o')
        axes[1, 0].set_title('RMSE per Channel')
        axes[1, 0].set_xlabel('Channel')
        axes[1, 0].set_ylabel('RMSE')
        axes[1, 0].grid(True)
        
        # Residual histogram
        residual = original - denoised
        axes[1, 1].hist(residual.flatten(), bins=50, alpha=0.7, density=True)
        axes[1, 1].set_title('Residual Distribution')
        axes[1, 1].set_xlabel('Residual Value')
        axes[1, 1].set_ylabel('Density')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, "evaluation_metrics.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Evaluation plots saved to {save_dir}")

def evaluate_denoising(original: np.ndarray, denoised: np.ndarray,
                      reference: Optional[np.ndarray] = None,
                      save_dir: Optional[str] = None,
                      sampling_rate: int = 1000) -> Dict:
    """
    Convenience function for comprehensive denoising evaluation
    
    Args:
        original: Original signal
        denoised: Denoised signal
        reference: Reference channels (optional)
        save_dir: Directory to save results (optional)
        sampling_rate: Sampling rate in Hz
        
    Returns:
        evaluation_results: Dictionary containing all evaluation metrics
    """
    evaluator = Noise2SimEvaluator(sampling_rate)
    results = evaluator.evaluate_signal(original, denoised, reference)
    
    if save_dir:
        # Generate report
        evaluator.generate_evaluation_report(results, save_dir)
        
        # Create visualization plots
        evaluator.create_visualization_plots(original, denoised, results, save_dir)
    
    return results

if __name__ == "__main__":
    # Test evaluation with synthetic data
    import numpy as np
    
    # Generate synthetic MCG-like signal
    t = np.arange(0, 40, 1/1000)  # 40 seconds at 1kHz
    
    # Create synthetic signal with multiple components
    signal = np.zeros((len(t), 36))
    for ch in range(36):
        # Heart rate component
        signal[:, ch] = 0.5 * np.sin(2 * np.pi * 1.2 * t)  # 1.2 Hz heart rate
        # Add some harmonics
        signal[:, ch] += 0.2 * np.sin(2 * np.pi * 2.4 * t)
        # Add channel-specific phase
        signal[:, ch] += 0.1 * np.sin(2 * np.pi * 1.2 * t + ch * 0.1)
    
    # Add noise
    noise = np.random.normal(0, 0.1, signal.shape)
    noisy_signal = signal + noise
    
    # Simulate denoising (simple low-pass filter)
    from scipy.signal import butter, filtfilt
    b, a = butter(4, 0.1, btype='low')
    denoised_signal = np.apply_along_axis(lambda x: filtfilt(b, a, x), 0, noisy_signal)
    
    # Evaluate
    results = evaluate_denoising(noisy_signal, denoised_signal, save_dir="test_evaluation")
    
    print("Evaluation completed. Results saved to test_evaluation/")
    print(f"SNR Improvement: {results['signal_quality']['snr_improvement_db']:.2f} dB")
    print(f"Correlation: {results['signal_quality']['correlation']:.6f}")