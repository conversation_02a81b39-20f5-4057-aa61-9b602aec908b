                    The FreeType Project LICENSE

                    ----------------------------



                            2006-Jan-27



                    Copyright 1996-2002, 2006 by

          <PERSON>, <PERSON>, and <PERSON>







Introduction

============



  The FreeType  Project is distributed in  several archive packages;

  some of them may contain, in addition to the FreeType font engine,

  various tools and  contributions which rely on, or  relate to, the

  FreeType Project.



  This  license applies  to all  files found  in such  packages, and

  which do not  fall under their own explicit  license.  The license

  affects  thus  the  FreeType   font  engine,  the  test  programs,

  documentation and makefiles, at the very least.



  This  license   was  inspired  by  the  BSD,   Artistic,  and  IJG

  (Independent JPEG  Group) licenses, which  all encourage inclusion

  and  use of  free  software in  commercial  and freeware  products

  alike.  As a consequence, its main points are that:



    o We don't promise that this software works. However, we will be

      interested in any kind of bug reports. (`as is' distribution)



    o You can  use this software for whatever you  want, in parts or

      full form, without having to pay us. (`royalty-free' usage)



    o You may not pretend that  you wrote this software.  If you use

      it, or  only parts of it,  in a program,  you must acknowledge

      somewhere  in  your  documentation  that  you  have  used  the

      FreeType code. (`credits')



  We  specifically  permit  and  encourage  the  inclusion  of  this

  software, with  or without modifications,  in commercial products.

  We  disclaim  all warranties  covering  The  FreeType Project  and

  assume no liability related to The FreeType Project.





  Finally,  many  people  asked  us  for  a  preferred  form  for  a

  credit/disclaimer to use in compliance with this license.  We thus

  encourage you to use the following text:



   """  

    Portions of this software are copyright © <year> The FreeType

    Project (www.freetype.org).  All rights reserved.

   """



  Please replace <year> with the value from the FreeType version you

  actually use.





Legal Terms

===========



0. Definitions

--------------



  Throughout this license,  the terms `package', `FreeType Project',

  and  `FreeType  archive' refer  to  the  set  of files  originally

  distributed  by the  authors  (David Turner,  Robert Wilhelm,  and

  Werner Lemberg) as the `FreeType Project', be they named as alpha,

  beta or final release.



  `You' refers to  the licensee, or person using  the project, where

  `using' is a generic term including compiling the project's source

  code as  well as linking it  to form a  `program' or `executable'.

  This  program is  referred to  as  `a program  using the  FreeType

  engine'.



  This  license applies  to all  files distributed  in  the original

  FreeType  Project,   including  all  source   code,  binaries  and

  documentation,  unless  otherwise  stated   in  the  file  in  its

  original, unmodified form as  distributed in the original archive.

  If you are  unsure whether or not a particular  file is covered by

  this license, you must contact us to verify this.



  The FreeType  Project is copyright (C) 1996-2000  by David Turner,

  Robert Wilhelm, and Werner Lemberg.  All rights reserved except as

  specified below.



1. No Warranty

--------------



  THE FREETYPE PROJECT  IS PROVIDED `AS IS' WITHOUT  WARRANTY OF ANY

  KIND, EITHER  EXPRESS OR IMPLIED,  INCLUDING, BUT NOT  LIMITED TO,

  WARRANTIES  OF  MERCHANTABILITY   AND  FITNESS  FOR  A  PARTICULAR

  PURPOSE.  IN NO EVENT WILL ANY OF THE AUTHORS OR COPYRIGHT HOLDERS

  BE LIABLE  FOR ANY DAMAGES CAUSED  BY THE USE OR  THE INABILITY TO

  USE, OF THE FREETYPE PROJECT.



2. Redistribution

-----------------



  This  license  grants  a  worldwide, royalty-free,  perpetual  and

  irrevocable right  and license to use,  execute, perform, compile,

  display,  copy,   create  derivative  works   of,  distribute  and

  sublicense the  FreeType Project (in  both source and  object code

  forms)  and  derivative works  thereof  for  any  purpose; and  to

  authorize others  to exercise  some or all  of the  rights granted

  herein, subject to the following conditions:



    o Redistribution of  source code  must retain this  license file

      (`FTL.TXT') unaltered; any  additions, deletions or changes to

      the original  files must be clearly  indicated in accompanying

      documentation.   The  copyright   notices  of  the  unaltered,

      original  files must  be  preserved in  all  copies of  source

      files.



    o Redistribution in binary form must provide a  disclaimer  that

      states  that  the software is based in part of the work of the

      FreeType Team,  in  the  distribution  documentation.  We also

      encourage you to put an URL to the FreeType web page  in  your

      documentation, though this isn't mandatory.



  These conditions  apply to any  software derived from or  based on

  the FreeType Project,  not just the unmodified files.   If you use

  our work, you  must acknowledge us.  However, no  fee need be paid

  to us.



3. Advertising

--------------



  Neither the  FreeType authors and  contributors nor you  shall use

  the name of the  other for commercial, advertising, or promotional

  purposes without specific prior written permission.



  We suggest,  but do not require, that  you use one or  more of the

  following phrases to refer  to this software in your documentation

  or advertising  materials: `FreeType Project',  `FreeType Engine',

  `FreeType library', or `FreeType Distribution'.



  As  you have  not signed  this license,  you are  not  required to

  accept  it.   However,  as  the FreeType  Project  is  copyrighted

  material, only  this license, or  another one contracted  with the

  authors, grants you  the right to use, distribute,  and modify it.

  Therefore,  by  using,  distributing,  or modifying  the  FreeType

  Project, you indicate that you understand and accept all the terms

  of this license.



4. Contacts

-----------



  There are two mailing lists related to FreeType:



    o <EMAIL>



      Discusses general use and applications of FreeType, as well as

      future and  wanted additions to the  library and distribution.

      If  you are looking  for support,  start in  this list  if you

      haven't found anything to help you in the documentation.



    o <EMAIL>



      Discusses bugs,  as well  as engine internals,  design issues,

      specific licenses, porting, etc.



  Our home page can be found at



    http://www.freetype.org





--- end of FTL.TXT ---