"""
小波降噪框架测试脚本
Author: Assistant
Date: 2025-07-17
Description: 测试框架的各个模块和功能
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入框架模块
try:
    from data_loader import load_data, extract_channels, segment_data, preprocess_signals
    from denoising_algorithms import denoise_baseline_wavelet, denoise_bivariate_shrinkage
    from evaluation import DenoiseEvaluator
    print("✓ 所有模块导入成功")
except ImportError as e:
    print(f"✗ 模块导入失败: {e}")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_data_loading():
    """测试数据加载模块"""
    print("\n=== 测试数据加载模块 ===")
    
    # 使用项目中的真实数据文件
    test_file = "../../files/降噪北京301的标准源2025-7-14/20250711标准源/PLAG_2025_000147.tdms_L.txt"
    
    if not os.path.exists(test_file):
        print(f"✗ 测试文件不存在: {test_file}")
        return False
    
    try:
        # 加载数据
        data = load_data(test_file)
        if data is None:
            print("✗ 数据加载失败")
            return False
        
        print(f"✓ 数据加载成功，形状: {data.shape}")
        
        # 提取通道
        idx, signals, references = extract_channels(data)
        print(f"✓ 通道提取成功: 信号{signals.shape}, 参考{references.shape}")
        
        # 数据分段
        segment = segment_data(signals, start_time=0, duration=5)
        print(f"✓ 数据分段成功: {segment.shape}")
        
        # 预处理
        processed = preprocess_signals(segment[:, :3], method='standardize')
        print(f"✓ 数据预处理成功: {processed.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False

def test_denoising_algorithms():
    """测试降噪算法"""
    print("\n=== 测试降噪算法 ===")
    
    # 生成测试信号
    np.random.seed(42)
    t = np.linspace(0, 2, 2000)
    clean_signal = np.sin(2*np.pi*10*t) + 0.5*np.sin(2*np.pi*25*t)
    noise = 0.3 * np.random.randn(len(clean_signal))
    noisy_signal = clean_signal + noise
    
    print(f"✓ 生成测试信号: {len(noisy_signal)} 点")
    
    try:
        # 测试基准小波降噪
        denoised1 = denoise_baseline_wavelet(noisy_signal, visualize=False)
        print(f"✓ 基准小波降噪成功: {len(denoised1)} 点")
        
        # 测试双变量收缩降噪
        denoised2 = denoise_bivariate_shrinkage(noisy_signal, visualize=False)
        print(f"✓ 双变量收缩降噪成功: {len(denoised2)} 点")
        
        # 简单的性能检查
        snr_orig = 10 * np.log10(np.var(clean_signal) / np.var(noise))
        snr_1 = 10 * np.log10(np.var(denoised1) / np.var(noisy_signal - denoised1))
        snr_2 = 10 * np.log10(np.var(denoised2) / np.var(noisy_signal - denoised2))
        
        print(f"  原始SNR: {snr_orig:.2f} dB")
        print(f"  基准小波SNR: {snr_1:.2f} dB")
        print(f"  双变量收缩SNR: {snr_2:.2f} dB")
        
        return True
        
    except Exception as e:
        print(f"✗ 降噪算法测试失败: {e}")
        return False

def test_evaluation_module():
    """测试评估模块"""
    print("\n=== 测试评估模块 ===")
    
    try:
        # 生成测试数据
        np.random.seed(42)
        t = np.linspace(0, 2, 2000)
        
        # 多通道原始信号
        original = np.column_stack([
            np.sin(2*np.pi*10*t) + 0.3*np.random.randn(len(t)),
            np.sin(2*np.pi*15*t) + 0.3*np.random.randn(len(t)),
            np.sin(2*np.pi*20*t) + 0.3*np.random.randn(len(t))
        ])
        
        # 模拟降噪结果
        denoised = original + 0.1*np.random.randn(*original.shape)
        
        # 参考信号
        reference = np.column_stack([
            np.sin(2*np.pi*12*t),
            np.sin(2*np.pi*18*t)
        ])
        
        print(f"✓ 生成测试数据: 原始{original.shape}, 降噪{denoised.shape}, 参考{reference.shape}")
        
        # 初始化评估器
        evaluator = DenoiseEvaluator(sampling_rate=1000)
        print("✓ 评估器初始化成功")
        
        # 测试相关性计算
        channel_groups = {0: [0, 1], 1: [2]}
        corr_metrics = evaluator.calculate_correlation_with_references(
            denoised, reference, channel_groups
        )
        print(f"✓ 相关性计算成功: {len(corr_metrics)} 个指标")
        
        # 测试SNR计算
        snr_metrics = evaluator.estimate_snr_improvement(original, denoised)
        print(f"✓ SNR计算成功: {len(snr_metrics)} 个指标")
        
        # 测试残差分析
        residual_metrics = evaluator.analyze_residuals(original, denoised)
        print(f"✓ 残差分析成功: {len(residual_metrics)} 个指标")
        
        # 测试综合评估
        evaluation_result = evaluator.comprehensive_evaluation(
            original, denoised, reference, channel_groups, "Test Method"
        )
        print(f"✓ 综合评估成功: 得分 {evaluation_result.get('overall_score', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 评估模块测试失败: {e}")
        return False

def test_complete_workflow():
    """测试完整工作流程"""
    print("\n=== 测试完整工作流程 ===")
    
    try:
        # 生成合成数据（模拟真实数据格式）
        np.random.seed(42)
        n_samples = 5000
        
        # 创建41通道数据: 1个idx + 36个信号通道 + 4个参考通道
        data = np.zeros((n_samples, 41))
        
        # 索引列
        data[:, 0] = np.arange(n_samples)
        
        # 信号通道 (1-36)
        t = np.linspace(0, 5, n_samples)
        for i in range(36):
            clean = np.sin(2*np.pi*(5+i)*t) + 0.3*np.sin(2*np.pi*(20+i*2)*t)
            noise = 0.4 * np.random.randn(n_samples)
            data[:, i+1] = clean + noise
        
        # 参考通道 (37-40)
        for i in range(4):
            data[:, 37+i] = np.sin(2*np.pi*(10+i*5)*t) + 0.2*np.random.randn(n_samples)
        
        print(f"✓ 生成合成数据: {data.shape}")
        
        # 提取通道
        idx, signals, references = extract_channels(data)
        print(f"✓ 通道提取: 信号{signals.shape}, 参考{references.shape}")
        
        # 选择部分通道和时间段
        selected_signals = signals[:2000, :3]  # 前3个通道，前2000个点
        selected_references = references[:2000, :]
        
        # 预处理
        processed_signals = preprocess_signals(selected_signals, 'standardize')
        processed_references = preprocess_signals(selected_references, 'standardize')
        print(f"✓ 预处理: 信号{processed_signals.shape}, 参考{processed_references.shape}")
        
        # 应用降噪
        denoised_signal = denoise_baseline_wavelet(processed_signals[:, 0], visualize=False)
        denoised_signals = np.column_stack([
            denoised_signal,
            denoise_bivariate_shrinkage(processed_signals[:, 1], visualize=False),
            denoise_baseline_wavelet(processed_signals[:, 2], visualize=False)
        ])
        print(f"✓ 降噪处理: {denoised_signals.shape}")
        
        # 评估
        evaluator = DenoiseEvaluator(sampling_rate=1000)
        channel_groups = {0: [0], 1: [1], 2: [2]}
        
        evaluation_result = evaluator.comprehensive_evaluation(
            processed_signals, denoised_signals, processed_references,
            channel_groups, "Complete Workflow Test"
        )
        
        overall_score = evaluation_result.get('overall_score', 0)
        print(f"✓ 完整评估成功: 综合得分 {overall_score:.3f}")
        
        # 验证结果合理性
        if 0 <= overall_score <= 1:
            print("✓ 评估得分在合理范围内")
        else:
            print(f"⚠ 评估得分异常: {overall_score}")
        
        return True
        
    except Exception as e:
        print(f"✗ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("小波降噪框架测试")
    print("="*50)
    
    test_results = []
    
    # 运行各个测试
    test_results.append(("数据加载模块", test_data_loading()))
    test_results.append(("降噪算法", test_denoising_algorithms()))
    test_results.append(("评估模块", test_evaluation_module()))
    test_results.append(("完整工作流程", test_complete_workflow()))
    
    # 总结测试结果
    print("\n" + "="*50)
    print("测试结果总结")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！框架可以正常使用。")
        print("\n使用建议:")
        print("1. 修改 main.py 中的 FILE_PATH 为您的数据文件路径")
        print("2. 调整 SELECTED_CHANNELS 选择要处理的通道")
        print("3. 配置 METHODS_TO_TEST 选择要测试的降噪方法")
        print("4. 运行 python main.py 开始实验")
    else:
        print(f"\n⚠ {total-passed} 个测试失败，请检查环境配置和依赖库。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)