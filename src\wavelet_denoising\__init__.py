"""
小波降噪实验框架
Author: Assistant
Date: 2025-07-17

这是一个完整的小波降噪实验框架，提供多种降噪算法和综合评估功能。

主要模块:
- data_loader: 数据加载和预处理
- denoising_algorithms: 六种降噪算法实现
- evaluation: 多维度评估指标和可视化
- main: 主执行脚本和配置界面

使用方法:
1. 修改 main.py 中的配置变量
2. 运行 python main.py
3. 查看降噪结果和评估报告

支持的降噪方法:
- 基准小波降噪 (Baseline Wavelet)
- SVD多通道降噪 (SVD)
- 双变量收缩降噪 (Bivariate Shrinkage)
- 双树复小波降噪 (DTCWT)
- 小波+残差VMD降噪 (Residual VMD)
- 迭代小波降噪 (Iterative Wavelet)
"""

__version__ = "1.0.0"
__author__ = "Assistant"

# 导入主要类和函数
from .data_loader import load_data, extract_channels, segment_data, preprocess_signals
from .denoising_algorithms import (
    denoise_baseline_wavelet, denoise_svd, denoise_bivariate_shrinkage,
    denoise_dtcwt, denoise_residual_vmd, denoise_iterative_wavelet,
    compare_all_methods
)
from .evaluation import DenoiseEvaluator, plot_wave_comparison, plot_spectrum_comparison

__all__ = [
    # 数据处理
    'load_data', 'extract_channels', 'segment_data', 'preprocess_signals',
    
    # 降噪算法
    'denoise_baseline_wavelet', 'denoise_svd', 'denoise_bivariate_shrinkage',
    'denoise_dtcwt', 'denoise_residual_vmd', 'denoise_iterative_wavelet',
    'compare_all_methods',
    
    # 评估工具
    'DenoiseEvaluator', 'plot_wave_comparison', 'plot_spectrum_comparison'
]