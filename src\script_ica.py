"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_ica
date: 2025/7/22 13:02
desc: 
"""
import numpy as np
import matplotlib.pyplot as plt
import pywt
from scipy import signal
from typing import Dict, List, Tuple, Optional, Any
import os
from sklearn.decomposition import PCA, FastICA
from scipy.linalg import eigh, sqrtm, inv
from scipy.signal import stft, istft, hilbert
import pywt
from scipy import signal
from scipy.signal import find_peaks
from sklearn.decomposition import FastICA

class SOBI:
    """
    SOBI (Second Order Blind Identification) 算法实现
    基于二阶统计量的盲源分离方法
    """

    def __init__(self, n_components=None, tau_max=10, n_tau=5, random_state=None):
        """
        参数:
        n_components (int): 提取的成分数量，默认为输入数据的通道数
        tau_max (int): 最大时延，默认为10
        n_tau (int): 使用的时延数量，默认为5
        random_state (int): 随机种子
        """
        self.n_components = n_components
        self.tau_max = tau_max
        self.n_tau = n_tau
        self.random_state = random_state
        self.mixing_matrix_ = None
        self.unmixing_matrix_ = None
        self.whitening_matrix_ = None
        self.mean_ = None

    def _whiten_data(self, X):
        """数据白化"""
        # 中心化
        self.mean_ = np.mean(X, axis=0)
        X_centered = X - self.mean_

        # 计算协方差矩阵
        cov_matrix = np.cov(X_centered.T)

        # 特征值分解
        eigenvals, eigenvecs = eigh(cov_matrix)

        # 按特征值降序排列
        idx = np.argsort(eigenvals)[::-1]
        eigenvals = eigenvals[idx]
        eigenvecs = eigenvecs[:, idx]

        # 白化矩阵
        self.whitening_matrix_ = np.dot(eigenvecs, np.diag(1.0 / np.sqrt(eigenvals)))

        # 白化数据
        X_whitened = np.dot(X_centered, self.whitening_matrix_)

        return X_whitened

    def _compute_covariance_matrices(self, X_whitened):
        """计算不同时延下的协方差矩阵"""
        n_samples, n_features = X_whitened.shape

        # 选择时延值
        if self.n_tau == 1:
            taus = [0]
        else:
            taus = np.linspace(0, self.tau_max, self.n_tau, dtype=int)

        covariance_matrices = []

        for tau in taus:
            if tau == 0:
                # tau=0时就是单位矩阵（因为数据已白化）
                cov_tau = np.eye(n_features)
            else:
                # 计算时延协方差矩阵
                if tau < n_samples:
                    X1 = X_whitened[:-tau, :]
                    X2 = X_whitened[tau:, :]
                    cov_tau = np.dot(X1.T, X2) / (n_samples - tau)
                else:
                    cov_tau = np.zeros((n_features, n_features))

            covariance_matrices.append(cov_tau)

        return covariance_matrices

    def _joint_diagonalization(self, matrices):
        """联合对角化多个矩阵"""
        n_matrices = len(matrices)
        n_features = matrices[0].shape[0]

        # 初始化联合对角化矩阵
        if self.random_state is not None:
            np.random.seed(self.random_state)

        # 使用第一个矩阵的特征向量作为初始化
        _, V = eigh(matrices[0])

        # 迭代优化
        max_iter = 100
        for iter_num in range(max_iter):
            # 计算所有矩阵在当前V下的对角化程度
            total_off_diag = 0

            for i in range(n_features):
                for j in range(i + 1, n_features):
                    # 计算Givens旋转角度
                    g11 = g12 = g21 = g22 = 0

                    for matrix in matrices:
                        transformed = np.dot(V.T, np.dot(matrix, V))
                        g11 += transformed[i, i] - transformed[j, j]
                        g12 += transformed[i, j] + transformed[j, i]
                        g21 += transformed[i, j] - transformed[j, i]
                        g22 += transformed[i, i] + transformed[j, j]

                    # 计算旋转角度
                    if abs(g11) > 1e-12 or abs(g12) > 1e-12:
                        theta = 0.25 * np.arctan2(g12, g11)

                        # 应用Givens旋转
                        c = np.cos(theta)
                        s = np.sin(theta)

                        G = np.eye(n_features)
                        G[i, i] = c
                        G[j, j] = c
                        G[i, j] = s
                        G[j, i] = -s

                        V = np.dot(V, G)

                        total_off_diag += abs(g12)

            # 检查收敛
            if total_off_diag < 1e-12:
                break

        return V

    def fit(self, X):
        """训练SOBI模型"""
        n_samples, n_features = X.shape

        if self.n_components is None:
            self.n_components = n_features

        # 白化数据
        X_whitened = self._whiten_data(X)

        # 计算不同时延的协方差矩阵
        covariance_matrices = self._compute_covariance_matrices(X_whitened)

        # 联合对角化
        self.unmixing_matrix_ = self._joint_diagonalization(covariance_matrices)

        # 计算混合矩阵
        whitening_unmixing = np.dot(self.whitening_matrix_, self.unmixing_matrix_)
        self.mixing_matrix_ = inv(whitening_unmixing)

        return self

    def transform(self, X):
        """应用SOBI变换得到源信号"""
        if self.unmixing_matrix_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        # 中心化
        X_centered = X - self.mean_

        # 白化
        X_whitened = np.dot(X_centered, self.whitening_matrix_)

        # 应用解混矩阵
        sources = np.dot(X_whitened, self.unmixing_matrix_)

        return sources

    def fit_transform(self, X):
        """训练并变换"""
        return self.fit(X).transform(X)

    def inverse_transform(self, sources):
        """从源信号重构原始信号"""
        if self.mixing_matrix_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        # 应用混合矩阵
        reconstructed = np.dot(sources, self.mixing_matrix_.T)

        # 加回均值
        reconstructed += self.mean_

        return reconstructed
class ConvolutiveBSS:
    """
    卷积盲源分离 (Convolutive Blind Source Separation, CBSS)
    基于频域独立向量分析 (IVA) 的实现
    """

    def __init__(self, n_components=None, n_fft=512, hop_length=None,
                 max_iter=100, tol=1e-6, random_state=None):
        self.n_components = n_components
        self.n_fft = n_fft
        self.hop_length = hop_length if hop_length is not None else n_fft // 4
        self.max_iter = max_iter
        self.tol = tol
        self.random_state = random_state
        self.unmixing_filters_ = None
        self.n_frequencies_ = None
        self.n_frames_ = None

    def _initialize_unmixing_filters(self, n_channels, n_frequencies):
        """初始化解混滤波器"""
        if self.random_state is not None:
            np.random.seed(self.random_state)

        W = np.zeros((n_frequencies, n_channels, n_channels), dtype=complex)
        for f in range(n_frequencies):
            W[f] = np.eye(n_channels, dtype=complex) + 0.01 * (
                    np.random.randn(n_channels, n_channels) + 1j * np.random.randn(n_channels, n_channels)
            )
        return W

    def _natural_gradient_iva(self, X):
        """使用自然梯度的独立向量分析 (IVA)"""
        n_frequencies, n_channels, n_frames = X.shape
        W = self._initialize_unmixing_filters(n_channels, n_frequencies)

        for iteration in range(self.max_iter):
            W_old = W.copy()

            for f in range(n_frequencies):
                X_f = X[f]  # (n_channels, n_frames)
                Y_f = np.dot(W[f], X_f)  # (n_channels, n_frames)

                # 计算源的幅度
                source_norms = np.abs(Y_f) + 1e-12
                phi = 1.0 / source_norms  # 权重函数

                # 构建期望矩阵
                E_phi_yy = np.zeros((n_channels, n_channels), dtype=complex)
                for i in range(n_channels):
                    for j in range(n_channels):
                        E_phi_yy[i, j] = np.mean(phi[i] * np.conj(Y_f[j]) * Y_f[i])

                # 自然梯度更新
                I = np.eye(n_channels, dtype=complex)
                gradient = I - E_phi_yy
                learning_rate = 0.1
                W[f] = W[f] + learning_rate * np.dot(gradient, W[f])

            # 检查收敛性
            diff = np.mean(np.abs(W - W_old))
            if diff < self.tol:
                print(f"CBSS在第 {iteration + 1} 次迭代后收敛")
                break

        return W

    def fit(self, X):
        """训练CBSS模型"""
        n_samples, n_channels = X.shape
        if self.n_components is None:
            self.n_components = n_channels

        # 对每个通道进行STFT
        X_stft_list = []
        min_frames = float('inf')

        # 先计算所有通道的STFT，并找到最小帧数
        for ch in range(n_channels):
            f, t, Zxx = stft(X[:, ch], nperseg=self.n_fft,
                             noverlap=self.n_fft - self.hop_length,
                             return_onesided=True)
            X_stft_list.append(Zxx)
            min_frames = min(min_frames, Zxx.shape[1])

        # 截断所有通道到相同的帧数
        n_frequencies = X_stft_list[0].shape[0]
        X_stft = np.zeros((n_channels, n_frequencies, min_frames), dtype=complex)
        for ch in range(n_channels):
            X_stft[ch] = X_stft_list[ch][:, :min_frames]

        self.n_frequencies_, self.n_frames_ = n_frequencies, min_frames

        # 转换维度顺序: (n_frequencies, n_channels, n_frames)
        X_freq = np.transpose(X_stft, (1, 0, 2))

        # 应用IVA算法
        self.unmixing_filters_ = self._natural_gradient_iva(X_freq)

        return self

    def transform(self, X):
        """应用CBSS变换得到源信号"""
        if self.unmixing_filters_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        n_samples, n_channels = X.shape

        # STFT变换
        X_stft_list = []
        min_frames = float('inf')

        # 先计算所有通道的STFT，并找到最小帧数
        for ch in range(n_channels):
            f, t, Zxx = stft(X[:, ch], nperseg=self.n_fft,
                             noverlap=self.n_fft - self.hop_length,
                             return_onesided=True)
            X_stft_list.append(Zxx)
            min_frames = min(min_frames, Zxx.shape[1])

        # 截断所有通道到相同的帧数
        n_frequencies = X_stft_list[0].shape[0]
        X_stft = np.zeros((n_channels, n_frequencies, min_frames), dtype=complex)
        for ch in range(n_channels):
            X_stft[ch] = X_stft_list[ch][:, :min_frames]

        # 转换维度顺序: (n_frequencies, n_channels, n_frames)
        X_freq = np.transpose(X_stft, (1, 0, 2))
        n_frequencies, n_channels_used, n_frames = X_freq.shape

        # 应用解混滤波器
        Y_freq = np.zeros_like(X_freq)
        for f in range(n_frequencies):
            Y_freq[f] = np.dot(self.unmixing_filters_[f], X_freq[f])

        # 转换回时域
        sources = np.zeros((n_samples, n_channels))
        for ch in range(n_channels):
            Y_ch = Y_freq[:, ch, :]
            t, y = istft(Y_ch, nperseg=self.n_fft,
                         noverlap=self.n_fft - self.hop_length)

            # 确保长度匹配
            min_len = min(len(y), n_samples)
            sources[:min_len, ch] = y[:min_len]

        return sources

    def fit_transform(self, X):
        """训练并变换"""
        return self.fit(X).transform(X)

    def inverse_transform(self, sources):
        """从源信号重构原始信号"""
        if self.unmixing_filters_ is None:
            raise ValueError("模型未训练，请先调用fit方法")

        n_samples, n_channels = sources.shape

        # STFT变换源信号
        S_stft_list = []
        min_frames = float('inf')

        # 先计算所有通道的STFT，并找到最小帧数
        for ch in range(n_channels):
            f, t, Zxx = stft(sources[:, ch], nperseg=self.n_fft,
                             noverlap=self.n_fft - self.hop_length,
                             return_onesided=True)
            S_stft_list.append(Zxx)
            min_frames = min(min_frames, Zxx.shape[1])

        # 截断所有通道到相同的帧数
        n_frequencies = S_stft_list[0].shape[0]
        S_stft = np.zeros((n_channels, n_frequencies, min_frames), dtype=complex)
        for ch in range(n_channels):
            S_stft[ch] = S_stft_list[ch][:, :min_frames]

        # 转换维度顺序: (n_frequencies, n_channels, n_frames)
        S_freq = np.transpose(S_stft, (1, 0, 2))
        n_frequencies, n_channels_used, n_frames = S_freq.shape

        # 计算混合滤波器（解混滤波器的逆）
        X_freq = np.zeros_like(S_freq)
        for f in range(n_frequencies):
            try:
                # 使用训练时保存的解混滤波器进行逆变换
                mixing_filter = np.linalg.inv(self.unmixing_filters_[f])
                X_freq[f] = np.dot(mixing_filter, S_freq[f])
            except np.linalg.LinAlgError:
                # 如果矩阵奇异，使用伪逆
                mixing_filter = np.linalg.pinv(self.unmixing_filters_[f])
                X_freq[f] = np.dot(mixing_filter, S_freq[f])

        # 转换回时域
        reconstructed = np.zeros((n_samples, n_channels))
        for ch in range(n_channels):
            X_ch = X_freq[:, ch, :]
            t, x = istft(X_ch, nperseg=self.n_fft,
                         noverlap=self.n_fft - self.hop_length)

            # 确保长度匹配
            min_len = min(len(x), n_samples)
            reconstructed[:min_len, ch] = x[:min_len]

        return reconstructed
