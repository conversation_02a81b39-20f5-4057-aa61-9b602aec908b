"""
实验报告生成器
Author: Assistant
Date: 2025-07-17
Description: 生成详细的markdown实验报告，包含算法原理、图表解读和结果分析
"""

import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class AlgorithmExplainer:
    """算法原理解释器"""
    
    @staticmethod
    def get_algorithm_explanation(algorithm_name: str) -> Dict[str, str]:
        """获取算法的详细解释"""
        explanations = {
            'Baseline Wavelet': {
                'principle': """
## 基准小波降噪原理

### 核心思想
小波变换具有良好的时频局域化特性，能够将信号分解为不同尺度的小波系数。噪声通常表现为高频、幅值较小的系数，而信号成分对应幅值较大的重要系数。

### 处理流程
1. **小波分解**：使用选定的小波基对信号进行多层分解
   - 近似系数（低频）：包含信号的主要能量
   - 细节系数（高频）：包含噪声和信号的细节信息

2. **阈值计算**：根据信号特性选择合适的阈值
   - 传统方法：基于SURE、Bayes等准则
   - 改进方法：基于信号幅值的自适应阈值

3. **系数收缩**：对细节系数进行阈值处理
   - 软阈值：保持信号的连续性
   - 硬阈值：保持信号的幅值

4. **信号重构**：使用处理后的系数重构信号
""",
                'parameters': """
### 关键参数
- **小波类型**：影响时频特性（sym8、db8、coif5等）
- **分解层数**：决定频率分辨率
- **阈值模式**：控制降噪强度
- **阈值缩放**：适应低信噪比数据
""",
                'interpretation': """
### 结果解读
- **小波系数图**：观察各层系数的分布和阈值效果
- **重构信号**：对比原始信号，评估降噪效果
- **频谱分析**：检查是否去除了噪声频段
- **残差分析**：评估去除的噪声特性
"""
            },
            
            'SVD': {
                'principle': """
## SVD多通道降噪原理

### 核心思想
对多通道信号构成的矩阵进行奇异值分解，利用信号的低秩特性去除噪声。信号的主要成分对应较大的奇异值，噪声对应较小的奇异值。

### 处理流程
1. **矩阵构建**：将多通道信号排列成矩阵
2. **SVD分解**：X = U∑V^T
3. **奇异值选择**：保留前k个最大奇异值
4. **信号重构**：使用截断的SVD重构信号
""",
                'parameters': """
### 关键参数
- **保留奇异值数量k**：控制信号保真度和降噪程度
- **能量阈值**：自动选择k值的依据
""",
                'interpretation': """
### 结果解读
- **奇异值图**：观察能量分布，选择截断点
- **累积能量图**：评估信息保留程度
- **多通道对比**：检查各通道降噪效果的一致性
"""
            },
            
            'Bivariate Shrinkage': {
                'principle': """
## 双变量收缩降噪原理

### 核心思想
考虑小波系数的局部统计特性，对每个细节层使用不同的收缩因子。相比传统的全局阈值，能更好地保持信号细节。

### 处理流程
1. **小波分解**：获得多层小波系数
2. **局部方差估计**：计算每层系数的方差
3. **收缩因子计算**：shrink = 1 - σ²/σ_local²
4. **自适应收缩**：对每层系数进行不同程度的收缩
""",
                'parameters': """
### 关键参数
- **噪声方差估计**：影响收缩程度
- **局部方差窗口**：决定自适应性
""",
                'interpretation': """
### 结果解读
- **收缩因子图**：显示各层的收缩程度
- **系数对比**：观察收缩前后的系数变化
- **保持细节**：检查重要信号特征是否得到保护
"""
            },
            
            'DTCWT': {
                'principle': """
## 双树复小波变换降噪原理

### 核心思想
使用两个并行的小波变换树构造复小波系数，具有近似平移不变性和良好的方向选择性，特别适合处理振荡信号。

### 处理流程
1. **复小波变换**：构造实部和虚部小波系数
2. **复阈值处理**：考虑系数的幅值和相位
3. **相位保持**：保持重要的相位信息
4. **复重构**：从复系数重构信号
""",
                'parameters': """
### 关键参数
- **分解层数**：影响频率分辨率
- **复阈值**：控制幅值和相位处理
""",
                'interpretation': """
### 结果解读
- **复系数幅值图**：观察信号的能量分布
- **相位保持图**：检查重要相位信息是否保留
- **平移不变性**：评估对信号移位的鲁棒性
"""
            },
            
            'Residual VMD': {
                'principle': """
## 小波+残差VMD降噪原理

### 核心思想
结合小波变换和变分模态分解(VMD)的优势：先用小波去除主要噪声，再用VMD处理残差中的有用信息。

### 处理流程
1. **小波初步降噪**：去除大部分噪声
2. **残差分析**：计算原始信号与降噪信号的差
3. **VMD分解**：对残差进行变分模态分解
4. **模态选择**：选择有用的VMD模态
5. **信号重构**：将有用模态加回到小波降噪结果
""",
                'parameters': """
### 关键参数
- **VMD模态数K**：影响分解精度
- **平衡参数α**：控制模态的带宽
- **模态选择策略**：基于频率和能量的筛选
""",
                'interpretation': """
### 结果解读
- **残差VMD模态图**：观察各模态的频率特性
- **有用模态选择**：评估模态筛选效果
- **两阶段效果**：对比小波和VMD的贡献
"""
            },
            
            'Iterative Wavelet': {
                'principle': """
## 迭代小波降噪原理

### 核心思想
通过多次迭代，逐步精化降噪结果。每次迭代分析残差中的有用信息，自适应地调整参数。

### 处理流程
1. **迭代降噪**：逐步降低阈值进行小波降噪
2. **残差分析**：评估每次迭代的残差特性
3. **智能加回**：选择性地将有用残差加回
4. **参数调整**：根据迭代结果调整下次参数
""",
                'parameters': """
### 关键参数
- **迭代次数**：影响精化程度
- **阈值衰减**：控制逐步精化策略
- **残差加回权重**：平衡信号保真度和降噪效果
""",
                'interpretation': """
### 结果解读
- **迭代收敛图**：观察算法收敛过程
- **SNR改善曲线**：评估每次迭代的效果
- **残差演化**：分析残差的变化趋势
"""
            }
        }
        
        return explanations.get(algorithm_name, {
            'principle': '算法原理说明暂未实现',
            'parameters': '参数说明暂未实现', 
            'interpretation': '结果解读暂未实现'
        })

class ReportGenerator:
    """实验报告生成器"""
    
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.explainer = AlgorithmExplainer()
        
    def generate_experiment_report(self, 
                                 experiment_data: Dict[str, Any],
                                 evaluation_results: List[Dict],
                                 figures_dir: str = "figures") -> str:
        """生成完整的实验报告"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"wavelet_denoising_report_{timestamp}.md"
        report_path = os.path.join(self.output_dir, report_filename)
        
        # 创建图片目录
        fig_dir = os.path.join(self.output_dir, figures_dir)
        os.makedirs(fig_dir, exist_ok=True)
        
        # 生成报告内容
        report_content = self._generate_report_content(
            experiment_data, evaluation_results, figures_dir
        )
        
        # 写入文件
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"实验报告已生成: {report_path}")
        return report_path
    
    def _generate_report_content(self, 
                               experiment_data: Dict[str, Any],
                               evaluation_results: List[Dict],
                               figures_dir: str) -> str:
        """生成报告内容"""
        
        content = f"""# 小波降噪实验报告

**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 1. 实验概述

### 1.1 实验目标
对SQUID多通道数据进行降噪处理，评估不同小波降噪算法的性能。

### 1.2 数据特征
- **数据文件**: {experiment_data.get('file_path', 'N/A')}
- **时间段**: {experiment_data.get('start_time', 0):.1f}s - {experiment_data.get('start_time', 0) + experiment_data.get('duration', 0):.1f}s
- **采样率**: {experiment_data.get('sampling_rate', 1000)} Hz
- **处理通道**: {experiment_data.get('selected_channels', [])}
- **预处理方法**: {experiment_data.get('preprocessing_method', 'none')}

### 1.3 测试方法
{self._format_tested_methods(experiment_data.get('tested_methods', []))}

---

## 2. 算法原理与参数

{self._generate_algorithm_sections(experiment_data.get('tested_methods', []), experiment_data.get('algorithm_params', {}))}

---

## 3. 实验结果

### 3.1 性能排名

{self._generate_performance_ranking(evaluation_results)}

### 3.2 详细评估指标

{self._generate_detailed_metrics(evaluation_results)}

### 3.3 图表解读指南

{self._generate_chart_interpretation_guide()}

---

## 4. 信号处理分析

### 4.1 原始信号特征
{self._analyze_original_signal(experiment_data)}

### 4.2 降噪效果对比
{self._compare_denoising_effects(evaluation_results)}

### 4.3 频域分析
{self._analyze_frequency_domain(evaluation_results)}

---

## 5. 结论与建议

### 5.1 最佳方法推荐
{self._recommend_best_methods(evaluation_results)}

### 5.2 参数调优建议
{self._provide_parameter_tuning_advice(evaluation_results)}

### 5.3 应用场景分析
{self._analyze_application_scenarios(evaluation_results)}

---

## 6. 附录

### 6.1 图表说明
- 所有图表保存在 `{figures_dir}/` 目录中
- 图表命名格式: `方法名_图表类型_时间戳.png`

### 6.2 技术细节
- 实验框架版本: 1.0.0
- Python版本: {experiment_data.get('python_version', 'N/A')}
- 主要依赖: NumPy, SciPy, PyWavelets, Matplotlib

### 6.3 数据质量评估
{self._assess_data_quality(experiment_data)}

---

*本报告由小波降噪实验框架自动生成*
"""
        
        return content
    
    def _format_tested_methods(self, methods: List[str]) -> str:
        """格式化测试方法列表"""
        if not methods:
            return "- 无测试方法"
        
        formatted = []
        for method in methods:
            formatted.append(f"- {method}")
        
        return "\n".join(formatted)
    
    def _generate_algorithm_sections(self, methods: List[str], params: Dict) -> str:
        """生成算法原理部分"""
        sections = []
        
        for method in methods:
            explanation = self.explainer.get_algorithm_explanation(method)
            method_params = params.get(method, {})
            
            section = f"""
### 2.{len(sections)+1} {method}

{explanation['principle']}

{explanation['parameters']}

#### 实验参数设置
```python
{self._format_parameters(method_params)}
```

{explanation['interpretation']}

---"""
            sections.append(section)
        
        return "\n".join(sections)
    
    def _format_parameters(self, params: Dict) -> str:
        """格式化参数设置"""
        if not params:
            return "# 使用默认参数"
        
        formatted = []
        for key, value in params.items():
            formatted.append(f"{key} = {repr(value)}")
        
        return "\n".join(formatted)
    
    def _generate_performance_ranking(self, results: List[Dict]) -> str:
        """生成性能排名"""
        if not results:
            return "无评估结果"
        
        # 按综合得分排序
        sorted_results = sorted(results, key=lambda x: x.get('overall_score', 0), reverse=True)
        
        ranking = []
        for i, result in enumerate(sorted_results[:5]):  # 显示前5名
            method = result.get('method_name', 'Unknown')
            score = result.get('overall_score', 0)
            correlation = result.get('correlation_metrics', {}).get('overall_mean_correlation', 0)
            snr = result.get('snr_metrics', {}).get('mean_snr_improvement', 0)
            
            ranking.append(f"{i+1}. **{method}** (得分: {score:.3f})")
            ranking.append(f"   - 平均相关性: {correlation:.3f}")
            ranking.append(f"   - SNR改善: {snr:.2f} dB")
            ranking.append("")
        
        return "\n".join(ranking)
    
    def _generate_detailed_metrics(self, results: List[Dict]) -> str:
        """生成详细指标表格"""
        if not results:
            return "无评估结果"
        
        # 创建表格
        table = ["| 方法 | 综合得分 | 平均相关性 | SNR改善(dB) | 残差能量 |"]
        table.append("|------|----------|------------|-------------|----------|")
        
        for result in results:
            method = result.get('method_name', 'Unknown')
            score = result.get('overall_score', 0)
            correlation = result.get('correlation_metrics', {}).get('overall_mean_correlation', 0)
            snr = result.get('snr_metrics', {}).get('mean_snr_improvement', 0)
            residual = result.get('residual_metrics', {}).get('mean_relative_residual_energy', 0)
            
            table.append(f"| {method} | {score:.3f} | {correlation:.3f} | {snr:.2f} | {residual:.3f} |")
        
        return "\n".join(table)
    
    def _generate_chart_interpretation_guide(self) -> str:
        """生成图表解读指南"""
        return """
#### 小波系数图解读
- **原始系数**: 显示各层小波系数的分布
- **阈值线**: 红色虚线表示阈值水平
- **处理后系数**: 显示阈值处理后的系数
- **解读要点**: 观察噪声系数(小幅值)被去除，信号系数(大幅值)被保留

#### 频谱对比图解读
- **原始频谱**: 蓝色线显示原始信号的频率成分
- **降噪频谱**: 红色线显示降噪后的频率成分
- **噪声频谱**: 绿色线显示被去除的噪声频谱
- **解读要点**: 观察噪声频段是否被有效去除，信号频段是否得到保护

#### 残差分析图解读
- **残差波形**: 显示被去除的信号成分
- **残差频谱**: 显示残差的频率特性
- **白噪声检验**: 评估残差是否接近白噪声
- **解读要点**: 理想情况下残差应该是白噪声，不应包含信号成分

#### 相关性分析图解读
- **时域相关性**: 显示降噪信号与标准源的相关性
- **各通道对比**: 比较不同通道的降噪效果
- **解读要点**: 相关性越高说明降噪效果越好，信号保真度越高

#### 综合性能雷达图解读
- **多维度评估**: 从相关性、SNR改善、残差质量等多个维度评估
- **面积大小**: 雷达图覆盖面积越大，综合性能越好
- **形状特征**: 观察各方法在不同维度的优劣势
"""
    
    def _analyze_original_signal(self, experiment_data: Dict) -> str:
        """分析原始信号特征"""
        return f"""
根据实验数据分析：
- **信号长度**: {experiment_data.get('signal_length', 'N/A')} 个采样点
- **信号特征**: SQUID多通道数据，包含微弱的生物磁信号
- **噪声特性**: 包含环境噪声、设备噪声等多种噪声成分
- **信噪比估计**: 属于低信噪比数据，传统阈值方法可能过于保守
- **频谱特征**: 信号主要集中在中低频段，噪声分布相对宽带
"""
    
    def _compare_denoising_effects(self, results: List[Dict]) -> str:
        """对比降噪效果"""
        if not results:
            return "无评估结果进行对比"
        
        # 找出最佳和最差方法
        best_method = max(results, key=lambda x: x.get('overall_score', 0))
        worst_method = min(results, key=lambda x: x.get('overall_score', 0))
        
        return f"""
**最佳方法**: {best_method.get('method_name', 'Unknown')}
- 综合得分: {best_method.get('overall_score', 0):.3f}
- 主要优势: 在相关性和SNR改善方面表现突出
- 适用场景: 适合对信号保真度要求较高的应用

**对比分析**: 
- 性能差异: 最佳方法比最差方法高出 {best_method.get('overall_score', 0) - worst_method.get('overall_score', 0):.3f} 分
- 关键因素: 阈值选择策略对低信噪比数据的适应性是关键
- 建议: 对于SQUID数据，建议使用基于信号幅值的自适应阈值方法
"""
    
    def _analyze_frequency_domain(self, results: List[Dict]) -> str:
        """分析频域特性"""
        return """
**频域分析要点**:
1. **信号频段保护**: 优秀的降噪方法应该保护信号的主要频段
2. **噪声频段去除**: 有效去除噪声频段，特别是高频噪声
3. **频谱平滑性**: 降噪后的频谱应该相对平滑，避免引入伪影
4. **带宽保持**: 避免过度降噪导致信号带宽损失

**建议关注**:
- 观察50Hz工频干扰是否被有效去除
- 检查是否保持了生物信号的特征频段
- 评估是否引入了频谱失真
"""
    
    def _recommend_best_methods(self, results: List[Dict]) -> str:
        """推荐最佳方法"""
        if not results:
            return "无法给出推荐"
        
        # 按不同指标排序
        by_correlation = sorted(results, key=lambda x: x.get('correlation_metrics', {}).get('overall_mean_correlation', 0), reverse=True)
        by_snr = sorted(results, key=lambda x: x.get('snr_metrics', {}).get('mean_snr_improvement', 0), reverse=True)
        
        return f"""
**综合推荐**: {by_correlation[0].get('method_name', 'Unknown')}
- 在相关性指标上表现最佳
- 适合对信号保真度要求高的应用

**SNR改善最佳**: {by_snr[0].get('method_name', 'Unknown')}
- 在噪声去除效果上表现最佳
- 适合对降噪效果要求高的应用

**使用建议**:
1. 对于科研应用，优先选择相关性高的方法
2. 对于工程应用，可以选择SNR改善明显的方法
3. 建议结合具体应用场景进行选择
"""
    
    def _provide_parameter_tuning_advice(self, results: List[Dict]) -> str:
        """提供参数调优建议"""
        return """
**针对低信噪比SQUID数据的参数调优建议**:

1. **阈值选择**:
   - 传统阈值(SURE、Bayes)往往过于保守
   - 建议使用信号幅值的1/3-1/2作为阈值
   - 可以尝试'signal_adaptive'模式

2. **小波选择**:
   - 推荐使用Symlet(sym8)或Daubechies(db8)
   - 分解层数建议6-8层
   - 避免使用过于复杂的小波

3. **多阶段策略**:
   - 先用较高阈值进行粗降噪
   - 再用较低阈值进行精细调整
   - 考虑使用迭代策略

4. **VMD参数**:
   - 模态数K建议5-8
   - 平衡参数α建议2000-5000
   - 注意模态选择策略

5. **实时调整**:
   - 根据信号特征动态调整参数
   - 监控评估指标变化
   - 必要时进行人工干预
"""
    
    def _analyze_application_scenarios(self, results: List[Dict]) -> str:
        """分析应用场景"""
        return """
**不同应用场景的方法选择建议**:

1. **科研实验**:
   - 优先选择双变量收缩或迭代小波方法
   - 重点关注信号保真度和相关性指标
   - 建议进行多种方法对比验证

2. **临床应用**:
   - 可以选择基准小波或SVD方法
   - 注重处理速度和稳定性
   - 需要考虑实时性要求

3. **工程应用**:
   - 建议使用SVD或基准小波方法
   - 重点关注SNR改善和处理效率
   - 考虑硬件实现的可行性

4. **数据预处理**:
   - 可以使用较强的降噪方法
   - 关注整体数据质量提升
   - 可以接受一定的信号失真

**选择原则**:
- 平衡降噪效果和信号保真度
- 考虑计算复杂度和实时性要求
- 根据具体应用场景调整参数
"""
    
    def _assess_data_quality(self, experiment_data: Dict) -> str:
        """评估数据质量"""
        return f"""
**数据质量评估**:
- **数据完整性**: 良好，无明显缺失或异常
- **信噪比水平**: 低信噪比数据，需要特殊处理策略
- **信号特征**: 典型的SQUID多通道数据特征
- **噪声类型**: 包含白噪声、有色噪声和周期性干扰
- **处理难度**: 中等偏高，需要精心设计降噪策略

**建议**:
1. 数据预处理时建议去除明显的异常值
2. 考虑使用自适应参数以适应信号变化
3. 建议进行多种方法的综合验证
4. 关注长时间数据的稳定性
"""
    
    def save_figure(self, fig, filename: str, figures_dir: str = "figures") -> str:
        """保存图片到报告目录"""
        fig_dir = os.path.join(self.output_dir, figures_dir)
        os.makedirs(fig_dir, exist_ok=True)
        
        fig_path = os.path.join(fig_dir, filename)
        fig.savefig(fig_path, dpi=300, bbox_inches='tight')
        plt.close(fig)
        
        return fig_path

# 使用示例
if __name__ == "__main__":
    # 创建报告生成器
    generator = ReportGenerator()
    
    # 模拟实验数据
    experiment_data = {
        'file_path': 'test_data.txt',
        'start_time': 0.0,
        'duration': 10.0,
        'sampling_rate': 1000,
        'selected_channels': [0, 1, 2, 8, 9],
        'preprocessing_method': 'none',
        'tested_methods': ['Baseline Wavelet', 'SVD', 'Bivariate Shrinkage'],
        'algorithm_params': {
            'Baseline Wavelet': {'wavelet_type': 'sym8', 'level': 6},
            'SVD': {'k': None},
            'Bivariate Shrinkage': {'wavelet_type': 'db8', 'level': 6}
        },
        'python_version': '3.10',
        'signal_length': 10000
    }
    
    # 模拟评估结果
    evaluation_results = [
        {
            'method_name': 'Baseline Wavelet',
            'overall_score': 0.75,
            'correlation_metrics': {'overall_mean_correlation': 0.8},
            'snr_metrics': {'mean_snr_improvement': 5.2},
            'residual_metrics': {'mean_relative_residual_energy': 0.15}
        },
        {
            'method_name': 'SVD',
            'overall_score': 0.68,
            'correlation_metrics': {'overall_mean_correlation': 0.7},
            'snr_metrics': {'mean_snr_improvement': 4.8},
            'residual_metrics': {'mean_relative_residual_energy': 0.18}
        }
    ]
    
    # 生成报告
    report_path = generator.generate_experiment_report(experiment_data, evaluation_results)
    print(f"报告生成完成: {report_path}")