"""
Lazy loading dataset implementation for memory-efficient data loading
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from typing import List, Tuple, Dict, Optional
import logging
import os
from pathlib import Path
import mmap
from collections import OrderedDict
import gc

from .utils import (
    load_mcg_data, 
    create_patches, 
    group_channels, 
    create_mask_indices, 
    apply_mask,
    set_seed
)
from .config import Config

logger = logging.getLogger(__name__)

class LRUCache:
    """
    Least Recently Used cache for file data
    """
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = OrderedDict()
    
    def get(self, key: str) -> Optional[np.ndarray]:
        """Get item from cache"""
        if key in self.cache:
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            return self.cache[key]
        return None
    
    def put(self, key: str, value: np.ndarray) -> None:
        """Put item in cache"""
        if key in self.cache:
            # Update existing item
            self.cache.move_to_end(key)
        else:
            # Add new item
            if len(self.cache) >= self.capacity:
                # Remove least recently used item
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                gc.collect()  # Force garbage collection
            
        self.cache[key] = value
    
    def clear(self) -> None:
        """Clear cache"""
        self.cache.clear()
        gc.collect()

class LazyMCGDataset(Dataset):
    """
    Lazy loading MCG Dataset with memory optimization
    
    This dataset:
    1. Loads data files on-demand
    2. Caches recently used files
    3. Creates patches dynamically
    4. Reduces memory footprint significantly
    """
    
    def __init__(self, 
                 file_paths: List[str], 
                 config: Config, 
                 group_idx: int = 0,
                 transform: Optional[callable] = None):
        """
        Initialize Lazy MCG Dataset
        
        Args:
            file_paths: List of data file paths
            config: Configuration object
            group_idx: Channel group index (0-3)
            transform: Optional transform function
        """
        self.file_paths = file_paths
        self.config = config
        self.group_idx = group_idx
        self.transform = transform
        
        # Validate group index
        if group_idx not in config.channel_groups:
            raise ValueError(f"Invalid group_idx: {group_idx}")
        
        self.channel_indices = config.channel_groups[group_idx]
        
        # Initialize cache
        self.cache = LRUCache(config.cache_size) if config.lazy_loading else None
        
        # Pre-compute dataset structure without loading data
        self.sample_info = []  # List of (file_idx, patch_idx) tuples
        self.patches_per_file = []  # Number of patches per file
        
        self._compute_dataset_structure()
        
        logger.info(f"Lazy dataset initialized for group {group_idx}")
        logger.info(f"Total samples: {len(self.sample_info)}")
        logger.info(f"Files: {len(self.file_paths)}")
        logger.info(f"Cache size: {config.cache_size}")
        logger.info(f"Channels in group: {self.channel_indices}")
    
    def _compute_dataset_structure(self):
        """Compute dataset structure without loading full data"""
        for file_idx, file_path in enumerate(self.file_paths):
            try:
                # Get file shape without loading full data
                n_samples = self._get_file_length(file_path)
                
                # Calculate number of patches for this file
                n_patches = (n_samples - self.config.patch_length) // self.config.overlap_step + 1
                
                if n_patches > 0:
                    self.patches_per_file.append(n_patches)
                    
                    # Add sample info for each patch
                    for patch_idx in range(n_patches):
                        self.sample_info.append((file_idx, patch_idx))
                else:
                    self.patches_per_file.append(0)
                    logger.warning(f"No patches generated for file {file_path}")
                
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                self.patches_per_file.append(0)
                continue
    
    def _get_file_length(self, file_path: str) -> int:
        """Get file length without loading full data"""
        try:
            # Count lines in file efficiently
            with open(file_path, 'r') as f:
                # Skip header if present
                first_line = f.readline().strip()
                if not first_line or first_line.startswith('#') or 'time' in first_line.lower():
                    # Has header, start counting from next line
                    line_count = sum(1 for _ in f)
                else:
                    # No header, count this line too
                    line_count = 1 + sum(1 for _ in f)
            
            return line_count
            
        except Exception as e:
            logger.warning(f"Could not get length for {file_path}, falling back to full load: {e}")
            # Fallback to loading full file
            signal_channels, _ = load_mcg_data(file_path)
            return signal_channels.shape[0]
    
    def _load_file_data(self, file_idx: int) -> Optional[np.ndarray]:
        """Load and cache file data"""
        file_path = self.file_paths[file_idx]
        
        # Check cache first
        if self.cache is not None:
            cached_data = self.cache.get(file_path)
            if cached_data is not None:
                return cached_data
        
        try:
            # Load file data
            signal_channels, _ = load_mcg_data(file_path)
            
            # Create patches
            patches = create_patches(
                signal_channels, 
                self.config.patch_length, 
                self.config.overlap_step
            )
            
            # Group channels and select current group
            grouped_patches = group_channels(patches, self.config.channel_groups)
            group_patches = grouped_patches[self.group_idx]
            
            # Cache the data
            if self.cache is not None:
                self.cache.put(file_path, group_patches)
            
            return group_patches
            
        except Exception as e:
            logger.error(f"Error loading file {file_path}: {e}")
            return None
    
    def __len__(self) -> int:
        """Return total number of samples"""
        return len(self.sample_info)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample with masking
        
        Args:
            idx: Sample index
            
        Returns:
            sample: Dictionary containing:
                - input: Masked patch (9, patch_length)
                - target: Original values at masked positions
                - mask_indices: Indices of masked positions
                - file_idx: Source file index
                - patch_idx: Patch index within file
        """
        if idx >= len(self.sample_info):
            raise IndexError(f"Index {idx} out of range for dataset of size {len(self.sample_info)}")
        
        file_idx, patch_idx = self.sample_info[idx]
        
        # Load file data (from cache or disk)
        file_patches = self._load_file_data(file_idx)
        
        if file_patches is None or patch_idx >= len(file_patches):
            # Return a dummy sample if loading fails
            logger.warning(f"Failed to load sample {idx}, returning dummy data")
            dummy_patch = np.zeros((9, self.config.patch_length))
            dummy_target = np.zeros((9, self.config.n_mask_points))
            dummy_mask = np.arange(self.config.n_mask_points)
            
            return {
                'input': torch.FloatTensor(dummy_patch),
                'target': torch.FloatTensor(dummy_target),
                'mask_indices': torch.LongTensor(dummy_mask),
                'file_idx': torch.LongTensor([file_idx]),
                'patch_idx': torch.LongTensor([patch_idx])
            }
        
        # Get the specific patch
        patch = file_patches[patch_idx].copy()  # Shape: (9, patch_length)
        
        # Create mask indices with new configuration
        mask_config = self.config.get_mask_config()
        mask_indices = create_mask_indices(
            self.config.patch_length, 
            mask_config['n_mask_points'],
            mask_config['mask_type'],
            mask_config['block_size'],
            mask_config['n_blocks']
        )
        
        # Extract target values (original values at masked positions)
        target = patch[:, mask_indices]  # Shape: (9, n_mask_points)
        
        # Apply mask to create input
        input_patch = apply_mask(patch, mask_indices)
        
        # Apply transform if provided
        if self.transform:
            input_patch = self.transform(input_patch)
            target = self.transform(target)
        
        # Convert to tensors
        sample = {
            'input': torch.FloatTensor(input_patch),
            'target': torch.FloatTensor(target),
            'mask_indices': torch.LongTensor(mask_indices),
            'file_idx': torch.LongTensor([file_idx]),
            'patch_idx': torch.LongTensor([patch_idx])
        }
        
        return sample
    
    def clear_cache(self):
        """Clear the data cache"""
        if self.cache is not None:
            self.cache.clear()
            logger.info("Dataset cache cleared")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        if self.cache is not None:
            return {
                'cache_size': len(self.cache.cache),
                'cache_capacity': self.cache.capacity,
                'cache_hit_rate': getattr(self, '_cache_hits', 0) / max(getattr(self, '_cache_requests', 1), 1)
            }
        return {'cache_size': 0, 'cache_capacity': 0, 'cache_hit_rate': 0.0}

class LazyMixedGroupDataset(Dataset):
    """
    Dataset that mixes data from all 4 channel groups for single model training
    Each sample randomly selects one of the 4 groups
    """
    
    def __init__(self, file_paths: List[str], config: Config, transform: Optional[callable] = None):
        """
        Initialize mixed group dataset
        
        Args:
            file_paths: List of data file paths
            config: Configuration object
            transform: Optional transform function
        """
        self.file_paths = file_paths
        self.config = config
        self.transform = transform
        
        # Create individual group datasets
        self.group_datasets = []
        for group_idx in range(4):
            dataset = LazyMCGDataset(file_paths, config, group_idx, transform)
            self.group_datasets.append(dataset)
        
        # Calculate total samples across all groups
        self.total_samples = sum(len(dataset) for dataset in self.group_datasets)
        
        # Create mapping from global index to (group_idx, local_idx)
        self.index_mapping = []
        for group_idx, dataset in enumerate(self.group_datasets):
            for local_idx in range(len(dataset)):
                self.index_mapping.append((group_idx, local_idx))
        
        logger.info(f"Mixed group dataset initialized")
        logger.info(f"Total samples across all groups: {self.total_samples}")
        logger.info(f"Samples per group: {[len(d) for d in self.group_datasets]}")
    
    def __len__(self) -> int:
        """Return total number of samples across all groups"""
        return self.total_samples
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a sample from any of the 4 groups
        
        Args:
            idx: Global sample index
            
        Returns:
            sample: Dictionary containing 9-channel patch data
        """
        if idx >= self.total_samples:
            raise IndexError(f"Index {idx} out of range for dataset of size {self.total_samples}")
        
        # Map global index to group and local index
        group_idx, local_idx = self.index_mapping[idx]
        
        # Get sample from the appropriate group dataset
        sample = self.group_datasets[group_idx][local_idx]
        
        # Add group information for debugging/tracking
        sample['group_idx'] = torch.LongTensor([group_idx])
        
        return sample
    
    def clear_cache(self):
        """Clear caches for all group datasets"""
        for dataset in self.group_datasets:
            dataset.clear_cache()
        logger.info("Mixed dataset caches cleared")
    
    def get_cache_stats(self) -> Dict[str, any]:
        """Get cache statistics for all groups"""
        stats = {}
        for group_idx, dataset in enumerate(self.group_datasets):
            stats[f'group_{group_idx}'] = dataset.get_cache_stats()
        return stats

class LazyMCGDataModule:
    """
    Data module for managing lazy MCG datasets and dataloaders
    """
    
    def __init__(self, config: Config):
        """
        Initialize lazy data module
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.train_files, self.test_files = config.get_data_paths()
        
        logger.info(f"Lazy data module initialized")
        logger.info(f"Training files: {len(self.train_files)}")
        logger.info(f"Testing files: {len(self.test_files)}")
        
        if config.single_model:
            # Single model mode: create one mixed dataset with all groups
            self.train_dataset = LazyMixedGroupDataset(self.train_files, config)
            self.test_dataset = LazyMixedGroupDataset(self.test_files, config)
            self.train_datasets = None
            self.test_datasets = None
        else:
            # Multi-model mode: create datasets for each channel group
            self.train_datasets = []
            self.test_datasets = []
            
            for group_idx in range(4):
                train_dataset = LazyMCGDataset(
                    self.train_files, 
                    config, 
                    group_idx=group_idx
                )
                test_dataset = LazyMCGDataset(
                    self.test_files, 
                    config, 
                    group_idx=group_idx
                )
                
                self.train_datasets.append(train_dataset)
                self.test_datasets.append(test_dataset)
    
    def get_train_loader(self, group_idx: int = 0) -> DataLoader:
        """
        Get training dataloader for specific channel group or mixed data
        
        Args:
            group_idx: Channel group index (0-3), ignored in single_model mode
            
        Returns:
            DataLoader for training
        """
        if self.config.single_model:
            return DataLoader(
                self.train_dataset,
                batch_size=self.config.batch_size,
                shuffle=True,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
        else:
            return DataLoader(
                self.train_datasets[group_idx],
                batch_size=self.config.batch_size,
                shuffle=True,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
    
    def get_test_loader(self, group_idx: int = 0) -> DataLoader:
        """
        Get test dataloader for specific channel group or mixed data
        
        Args:
            group_idx: Channel group index (0-3), ignored in single_model mode
            
        Returns:
            DataLoader for testing
        """
        if self.config.single_model:
            return DataLoader(
                self.test_dataset,
                batch_size=self.config.batch_size,
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=False,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
        else:
            return DataLoader(
                self.test_datasets[group_idx],
                batch_size=self.config.batch_size,
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=False,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
    
    def get_dataset_stats(self) -> Dict:
        """Get dataset statistics"""
        stats = {
            'train_files': len(self.train_files),
            'test_files': len(self.test_files),
            'single_model': self.config.single_model
        }
        
        if self.config.single_model:
            # Single model statistics
            stats['mixed_data'] = {
                'train_samples': len(self.train_dataset),
                'test_samples': len(self.test_dataset),
                'train_cache_stats': self.train_dataset.get_cache_stats(),
                'test_cache_stats': self.test_dataset.get_cache_stats()
            }
        else:
            # Multi-model statistics
            stats['groups'] = {}
            for group_idx in range(4):
                train_cache_stats = self.train_datasets[group_idx].get_cache_stats()
                test_cache_stats = self.test_datasets[group_idx].get_cache_stats()
                
                stats['groups'][group_idx] = {
                    'train_samples': len(self.train_datasets[group_idx]),
                    'test_samples': len(self.test_datasets[group_idx]),
                    'channels': self.config.channel_groups[group_idx],
                    'train_cache_stats': train_cache_stats,
                    'test_cache_stats': test_cache_stats
                }
        
        return stats
    
    def clear_caches(self):
        """Clear all dataset caches"""
        if self.config.single_model:
            self.train_dataset.clear_cache()
            self.test_dataset.clear_cache()
        else:
            for dataset in self.train_datasets + self.test_datasets:
                dataset.clear_cache()
        logger.info("All dataset caches cleared")

# Factory function to create appropriate dataset
def create_mcg_data_module(config: Config):
    """
    Create MCG data module based on configuration
    
    Args:
        config: Configuration object
        
    Returns:
        Data module (simple, efficient, lazy or regular)
    """
    # Use simple data module for better performance and clarity
    from .simple_dataset import SimpleMCGDataModule
    return SimpleMCGDataModule(config)

if __name__ == "__main__":
    # Test lazy dataset
    config = Config()
    config.lazy_loading = True
    config.cache_size = 3
    
    # Create lazy data module
    data_module = LazyMCGDataModule(config)
    
    # Test dataset
    stats = data_module.get_dataset_stats()
    print(f"Dataset statistics: {stats}")
    
    # Test dataloader
    train_loader = data_module.get_train_loader(0)
    
    print("Testing lazy dataloader...")
    for i, batch in enumerate(train_loader):
        print(f"Batch {i}: input shape {batch['input'].shape}, target shape {batch['target'].shape}")
        if i >= 2:  # Test first few batches
            break
    
    print("Lazy dataset test completed!")