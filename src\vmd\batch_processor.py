"""
批量处理模块
Author: Assistant
Date: 2025-07-16
Description: 批量处理多个txt文件，生成综合分析和报告
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
import time
from datetime import datetime
import json
from dataclasses import dataclass, asdict

from vmd_engine import VMDEngine, ProcessingResult
from config_manager import ConfigManager
from visualization import VisualizationManager, ReportGenerator

@dataclass
class BatchProcessingStats:
    """批量处理统计信息"""
    total_files: int = 0
    successful_files: int = 0
    failed_files: int = 0
    total_channels: int = 0
    total_processing_time: float = 0.0
    average_correlation: float = 0.0
    average_snr: float = 0.0
    files_processed: List[str] = None
    files_failed: List[str] = None
    
    def __post_init__(self):
        if self.files_processed is None:
            self.files_processed = []
        if self.files_failed is None:
            self.files_failed = []

@dataclass
class FileProcessingResult:
    """单个文件的处理结果"""
    file_name: str
    file_path: str
    processing_time: float
    channels_processed: List[int]
    success: bool
    error_message: str = ""
    results: Dict[int, ProcessingResult] = None
    
    def __post_init__(self):
        if self.results is None:
            self.results = {}

class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.vmd_engine = VMDEngine(config_manager)
        self.vis_manager = VisualizationManager(config_manager)
        self.report_generator = ReportGenerator(config_manager)
        
        # 批量处理结果
        self.batch_results: Dict[str, FileProcessingResult] = {}
        self.batch_stats = BatchProcessingStats()
    
    def find_txt_files(self, directory: str) -> List[Path]:
        """查找目录中的所有txt文件"""
        try:
            directory = Path(directory)
            
            if not directory.exists():
                raise FileNotFoundError(f"目录不存在: {directory}")
            
            if not directory.is_dir():
                raise ValueError(f"路径不是目录: {directory}")
            
            # 查找所有txt文件
            txt_files = list(directory.glob("*.txt"))
            
            # 按文件名排序
            txt_files.sort()
            
            self.logger.info(f"在目录 {directory} 中找到 {len(txt_files)} 个txt文件")
            
            return txt_files
            
        except Exception as e:
            self.logger.error(f"查找txt文件失败: {e}")
            raise
    
    def process_single_file(self, file_path: Path) -> FileProcessingResult:
        """处理单个文件"""
        try:
            start_time = time.time()
            file_name = file_path.stem
            
            self.logger.info(f"开始处理文件: {file_name}")
            
            # 处理文件
            results = self.vmd_engine.process_file(str(file_path))
            
            # 保存结果
            self.vmd_engine.save_results(str(file_path), results)
            
            processing_time = time.time() - start_time
            channels_processed = list(results.keys())
            
            result = FileProcessingResult(
                file_name=file_name,
                file_path=str(file_path),
                processing_time=processing_time,
                channels_processed=channels_processed,
                success=True,
                results=results
            )
            
            self.logger.info(f"文件 {file_name} 处理完成，耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = str(e)
            
            result = FileProcessingResult(
                file_name=file_path.stem,
                file_path=str(file_path),
                processing_time=processing_time,
                channels_processed=[],
                success=False,
                error_message=error_msg
            )
            
            self.logger.error(f"处理文件 {file_path} 失败: {error_msg}")
            return result
    
    def process_batch(self, directory: str, 
                     file_filter: Optional[str] = None,
                     max_files: Optional[int] = None) -> BatchProcessingStats:
        """批量处理文件夹中的所有txt文件"""
        try:
            batch_start_time = time.time()
            
            # 查找所有txt文件
            txt_files = self.find_txt_files(directory)
            
            # 应用文件过滤器
            if file_filter:
                txt_files = [f for f in txt_files if file_filter in f.name]
                self.logger.info(f"应用过滤器 '{file_filter}' 后剩余 {len(txt_files)} 个文件")
            
            # 限制文件数量
            if max_files and len(txt_files) > max_files:
                txt_files = txt_files[:max_files]
                self.logger.info(f"限制处理文件数量为 {max_files} 个")
            
            if not txt_files:
                raise ValueError("没有找到需要处理的文件")
            
            # 初始化统计信息
            self.batch_stats = BatchProcessingStats()
            self.batch_stats.total_files = len(txt_files)
            
            self.logger.info(f"开始批量处理 {len(txt_files)} 个文件")
            
            # 处理每个文件
            for i, file_path in enumerate(txt_files):
                try:
                    self.logger.info(f"处理进度: {i+1}/{len(txt_files)} - {file_path.name}")
                    
                    result = self.process_single_file(file_path)
                    self.batch_results[file_path.name] = result
                    
                    if result.success:
                        self.batch_stats.successful_files += 1
                        self.batch_stats.files_processed.append(file_path.name)
                        self.batch_stats.total_channels += len(result.channels_processed)
                    else:
                        self.batch_stats.failed_files += 1
                        self.batch_stats.files_failed.append(file_path.name)
                    
                    self.batch_stats.total_processing_time += result.processing_time
                    
                except Exception as e:
                    self.logger.error(f"处理文件 {file_path} 时发生严重错误: {e}")
                    self.batch_stats.failed_files += 1
                    self.batch_stats.files_failed.append(file_path.name)
            
            # 计算统计指标
            self._calculate_batch_statistics()
            
            batch_total_time = time.time() - batch_start_time
            
            self.logger.info(f"批量处理完成！")
            self.logger.info(f"总耗时: {batch_total_time:.2f}秒")
            self.logger.info(f"成功: {self.batch_stats.successful_files}/{self.batch_stats.total_files}")
            self.logger.info(f"失败: {self.batch_stats.failed_files}")
            
            return self.batch_stats
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            raise
    
    def _calculate_batch_statistics(self):
        """计算批量处理的统计指标"""
        try:
            correlations = []
            snrs = []
            
            for file_result in self.batch_results.values():
                if file_result.success and file_result.results:
                    for channel_result in file_result.results.values():
                        if channel_result.similarity_metrics:
                            corr = channel_result.similarity_metrics.get('correlation_to_original', 0)
                            snr = channel_result.similarity_metrics.get('snr_to_original', 0)
                            
                            if corr > 0:
                                correlations.append(corr)
                            if snr > 0:
                                snrs.append(snr)
            
            # 计算平均值
            if correlations:
                self.batch_stats.average_correlation = np.mean(correlations)
            if snrs:
                self.batch_stats.average_snr = np.mean(snrs)
            
        except Exception as e:
            self.logger.warning(f"计算批量统计指标失败: {e}")
    
    def generate_batch_visualizations(self, output_dir: str):
        """生成批量处理的综合可视化"""
        try:
            output_path = Path(output_dir)
            batch_vis_dir = output_path / "batch_analysis"
            batch_vis_dir.mkdir(exist_ok=True)
            
            self.logger.info("开始生成批量处理可视化...")
            
            # 1. 生成批量处理概览图
            self._plot_batch_overview(batch_vis_dir)
            
            # 2. 生成文件间对比图
            self._plot_files_comparison(batch_vis_dir)
            
            # 3. 生成统计分析图
            self._plot_batch_statistics(batch_vis_dir)
            
            # 4. 生成质量分析图
            self._plot_quality_analysis(batch_vis_dir)
            
            self.logger.info(f"批量可视化已生成至: {batch_vis_dir}")
            
        except Exception as e:
            self.logger.error(f"生成批量可视化失败: {e}")
            raise
    
    def _plot_batch_overview(self, output_dir: Path):
        """绘制批量处理概览图"""
        try:
            import matplotlib.pyplot as plt
            from visualization import get_label
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(get_label('批量处理概览', 'Batch Processing Overview'), fontsize=16)
            
            # 1. 处理成功率饼图
            success_data = [self.batch_stats.successful_files, self.batch_stats.failed_files]
            success_labels = [get_label('成功', 'Success'), get_label('失败', 'Failed')]
            colors = ['#2ecc71', '#e74c3c']
            
            axes[0, 0].pie(success_data, labels=success_labels, colors=colors, autopct='%1.1f%%')
            axes[0, 0].set_title(get_label('文件处理成功率', 'File Processing Success Rate'))
            
            # 2. 文件处理时间条形图
            if self.batch_results:
                file_names = []
                processing_times = []
                
                for name, result in list(self.batch_results.items())[:10]:  # 显示前10个文件
                    file_names.append(name[:15] + '...' if len(name) > 15 else name)
                    processing_times.append(result.processing_time)
                
                bars = axes[0, 1].bar(range(len(file_names)), processing_times, color='skyblue')
                axes[0, 1].set_xticks(range(len(file_names)))
                axes[0, 1].set_xticklabels(file_names, rotation=45, ha='right')
                axes[0, 1].set_ylabel(get_label('处理时间 (秒)', 'Processing Time (s)'))
                axes[0, 1].set_title(get_label('文件处理时间', 'File Processing Time'))
                axes[0, 1].grid(True, alpha=0.3)
                
                # 添加数值标签
                for bar, time_val in zip(bars, processing_times):
                    axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                                   f'{time_val:.1f}s', ha='center', va='bottom', fontsize=8)
            
            # 3. 通道处理统计
            channel_counts = {}
            for result in self.batch_results.values():
                if result.success:
                    for channel in result.channels_processed:
                        channel_counts[channel] = channel_counts.get(channel, 0) + 1
            
            if channel_counts:
                channels = list(channel_counts.keys())
                counts = list(channel_counts.values())
                
                axes[1, 0].bar(channels, counts, color='lightgreen')
                axes[1, 0].set_xlabel(get_label('通道编号', 'Channel Number'))
                axes[1, 0].set_ylabel(get_label('处理次数', 'Processing Count'))
                axes[1, 0].set_title(get_label('通道处理统计', 'Channel Processing Statistics'))
                axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 批量处理统计信息文本
            stats_text = f"""
{get_label('批量处理统计', 'Batch Processing Statistics')}:
{get_label('总文件数', 'Total Files')}: {self.batch_stats.total_files}
{get_label('成功文件数', 'Successful Files')}: {self.batch_stats.successful_files}
{get_label('失败文件数', 'Failed Files')}: {self.batch_stats.failed_files}
{get_label('总通道数', 'Total Channels')}: {self.batch_stats.total_channels}
{get_label('总处理时间', 'Total Time')}: {self.batch_stats.total_processing_time:.2f}s
{get_label('平均相关性', 'Avg Correlation')}: {self.batch_stats.average_correlation:.3f}
{get_label('平均信噪比', 'Avg SNR')}: {self.batch_stats.average_snr:.2f}dB
"""
            
            axes[1, 1].text(0.1, 0.9, stats_text, transform=axes[1, 1].transAxes,
                           fontsize=12, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.5))
            axes[1, 1].axis('off')
            
            plt.tight_layout()
            
            save_path = output_dir / "batch_overview.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"批量概览图已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"生成批量概览图失败: {e}")
    
    def _plot_files_comparison(self, output_dir: Path):
        """绘制文件间对比图"""
        try:
            import matplotlib.pyplot as plt
            from visualization import get_label
            
            # 收集所有成功处理的文件的平均指标
            file_metrics = {}
            
            for file_name, file_result in self.batch_results.items():
                if file_result.success and file_result.results:
                    correlations = []
                    snrs = []
                    
                    for channel_result in file_result.results.values():
                        if channel_result.similarity_metrics:
                            corr = channel_result.similarity_metrics.get('correlation_to_original', 0)
                            snr = channel_result.similarity_metrics.get('snr_to_original', 0)
                            
                            if corr > 0:
                                correlations.append(corr)
                            if snr > 0:
                                snrs.append(snr)
                    
                    if correlations and snrs:
                        file_metrics[file_name] = {
                            'avg_correlation': np.mean(correlations),
                            'avg_snr': np.mean(snrs),
                            'processing_time': file_result.processing_time
                        }
            
            if not file_metrics:
                self.logger.warning("没有足够的数据生成文件对比图")
                return
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(get_label('文件间对比分析', 'Inter-File Comparison Analysis'), fontsize=16)
            
            file_names = list(file_metrics.keys())
            correlations = [metrics['avg_correlation'] for metrics in file_metrics.values()]
            snrs = [metrics['avg_snr'] for metrics in file_metrics.values()]
            times = [metrics['processing_time'] for metrics in file_metrics.values()]
            
            # 1. 文件相关性对比
            axes[0, 0].plot(range(len(file_names)), correlations, 'o-', color='blue', linewidth=2)
            axes[0, 0].set_xticks(range(len(file_names)))
            axes[0, 0].set_xticklabels([name[:10] + '...' if len(name) > 10 else name 
                                      for name in file_names], rotation=45, ha='right')
            axes[0, 0].set_ylabel(get_label('平均相关性', 'Average Correlation'))
            axes[0, 0].set_title(get_label('文件相关性对比', 'File Correlation Comparison'))
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 文件信噪比对比
            axes[0, 1].plot(range(len(file_names)), snrs, 'o-', color='green', linewidth=2)
            axes[0, 1].set_xticks(range(len(file_names)))
            axes[0, 1].set_xticklabels([name[:10] + '...' if len(name) > 10 else name 
                                      for name in file_names], rotation=45, ha='right')
            axes[0, 1].set_ylabel(get_label('平均信噪比 (dB)', 'Average SNR (dB)'))
            axes[0, 1].set_title(get_label('文件信噪比对比', 'File SNR Comparison'))
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 相关性分布直方图
            axes[1, 0].hist(correlations, bins=20, color='skyblue', alpha=0.7, edgecolor='black')
            axes[1, 0].set_xlabel(get_label('相关性', 'Correlation'))
            axes[1, 0].set_ylabel(get_label('文件数量', 'Number of Files'))
            axes[1, 0].set_title(get_label('相关性分布', 'Correlation Distribution'))
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 相关性与信噪比散点图
            scatter = axes[1, 1].scatter(correlations, snrs, c=times, cmap='viridis', 
                                       s=60, alpha=0.7, edgecolors='black')
            axes[1, 1].set_xlabel(get_label('平均相关性', 'Average Correlation'))
            axes[1, 1].set_ylabel(get_label('平均信噪比 (dB)', 'Average SNR (dB)'))
            axes[1, 1].set_title(get_label('相关性 vs 信噪比', 'Correlation vs SNR'))
            axes[1, 1].grid(True, alpha=0.3)
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=axes[1, 1])
            cbar.set_label(get_label('处理时间 (秒)', 'Processing Time (s)'))
            
            plt.tight_layout()
            
            save_path = output_dir / "files_comparison.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"文件对比图已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"生成文件对比图失败: {e}")
    
    def _plot_batch_statistics(self, output_dir: Path):
        """绘制批量统计分析图"""
        try:
            import matplotlib.pyplot as plt
            from visualization import get_label
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(get_label('批量统计分析', 'Batch Statistical Analysis'), fontsize=16)
            
            # 收集所有指标数据
            all_correlations = []
            all_snrs = []
            all_mse = []
            all_rmse = []
            channel_performance = {}
            
            for file_result in self.batch_results.values():
                if file_result.success and file_result.results:
                    for channel, channel_result in file_result.results.items():
                        if channel_result.similarity_metrics:
                            metrics = channel_result.similarity_metrics
                            
                            corr = metrics.get('correlation_to_original', 0)
                            snr = metrics.get('snr_to_original', 0)
                            mse = metrics.get('mse_to_original', 0)
                            rmse = metrics.get('rmse_to_original', 0)
                            
                            if corr > 0:
                                all_correlations.append(corr)
                            if snr > 0:
                                all_snrs.append(snr)
                            if mse > 0:
                                all_mse.append(mse)
                            if rmse > 0:
                                all_rmse.append(rmse)
                            
                            # 通道性能统计
                            if channel not in channel_performance:
                                channel_performance[channel] = {'corr': [], 'snr': []}
                            if corr > 0:
                                channel_performance[channel]['corr'].append(corr)
                            if snr > 0:
                                channel_performance[channel]['snr'].append(snr)
            
            # 1. 相关性分布箱型图
            if all_correlations:
                axes[0, 0].boxplot([all_correlations], labels=[get_label('相关性', 'Correlation')])
                axes[0, 0].set_title(get_label('相关性分布', 'Correlation Distribution'))
                axes[0, 0].grid(True, alpha=0.3)
            
            # 2. 信噪比分布箱型图
            if all_snrs:
                axes[0, 1].boxplot([all_snrs], labels=[get_label('信噪比', 'SNR')])
                axes[0, 1].set_title(get_label('信噪比分布 (dB)', 'SNR Distribution (dB)'))
                axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 通道性能对比
            if channel_performance:
                channels = sorted(channel_performance.keys())
                channel_avg_corr = []
                channel_avg_snr = []
                
                for ch in channels:
                    avg_corr = np.mean(channel_performance[ch]['corr']) if channel_performance[ch]['corr'] else 0
                    avg_snr = np.mean(channel_performance[ch]['snr']) if channel_performance[ch]['snr'] else 0
                    channel_avg_corr.append(avg_corr)
                    channel_avg_snr.append(avg_snr)
                
                x = np.arange(len(channels))
                width = 0.35
                
                bars1 = axes[1, 0].bar(x - width/2, channel_avg_corr, width, 
                                     label=get_label('相关性', 'Correlation'), alpha=0.8)
                bars2 = axes[1, 0].bar(x + width/2, [snr/100 for snr in channel_avg_snr], width, 
                                     label=get_label('信噪比/100', 'SNR/100'), alpha=0.8)
                
                axes[1, 0].set_xlabel(get_label('通道', 'Channel'))
                axes[1, 0].set_ylabel(get_label('平均值', 'Average Value'))
                axes[1, 0].set_title(get_label('通道性能对比', 'Channel Performance Comparison'))
                axes[1, 0].set_xticks(x)
                axes[1, 0].set_xticklabels(channels)
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 处理质量等级分布
            quality_levels = {'优秀': 0, '良好': 0, '一般': 0, '较差': 0}
            
            for corr in all_correlations:
                if corr >= 0.9:
                    quality_levels['优秀'] += 1
                elif corr >= 0.8:
                    quality_levels['良好'] += 1
                elif corr >= 0.6:
                    quality_levels['一般'] += 1
                else:
                    quality_levels['较差'] += 1
            
            quality_labels = [get_label(k, k) for k in quality_levels.keys()]
            quality_values = list(quality_levels.values())
            colors = ['#2ecc71', '#f39c12', '#e67e22', '#e74c3c']
            
            if sum(quality_values) > 0:
                axes[1, 1].pie(quality_values, labels=quality_labels, colors=colors, 
                             autopct='%1.1f%%', startangle=90)
                axes[1, 1].set_title(get_label('处理质量等级分布', 'Processing Quality Distribution'))
            
            plt.tight_layout()
            
            save_path = output_dir / "batch_statistics.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"批量统计图已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"生成批量统计图失败: {e}")
    
    def _plot_quality_analysis(self, output_dir: Path):
        """绘制质量分析图"""
        try:
            import matplotlib.pyplot as plt
            from visualization import get_label
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(get_label('批量处理质量分析', 'Batch Processing Quality Analysis'), fontsize=16)
            
            # 收集质量数据
            processing_times = []
            correlations_original = []
            correlations_reference = []
            snrs = []
            
            for file_result in self.batch_results.values():
                if file_result.success and file_result.results:
                    processing_times.append(file_result.processing_time)
                    
                    file_corr_orig = []
                    file_corr_ref = []
                    file_snrs = []
                    
                    for channel_result in file_result.results.values():
                        if channel_result.similarity_metrics:
                            metrics = channel_result.similarity_metrics
                            
                            corr_orig = metrics.get('correlation_to_original', 0)
                            corr_ref = metrics.get('correlation_to_reference', 0)
                            snr = metrics.get('snr_to_original', 0)
                            
                            if corr_orig > 0:
                                file_corr_orig.append(corr_orig)
                            if corr_ref > 0:
                                file_corr_ref.append(corr_ref)
                            if snr > 0:
                                file_snrs.append(snr)
                    
                    if file_corr_orig:
                        correlations_original.append(np.mean(file_corr_orig))
                    if file_corr_ref:
                        correlations_reference.append(np.mean(file_corr_ref))
                    if file_snrs:
                        snrs.append(np.mean(file_snrs))
            
            # 1. 处理时间 vs 质量相关性
            if processing_times and correlations_original:
                scatter1 = axes[0, 0].scatter(processing_times, correlations_original, 
                                            alpha=0.6, c='blue', s=50)
                axes[0, 0].set_xlabel(get_label('处理时间 (秒)', 'Processing Time (s)'))
                axes[0, 0].set_ylabel(get_label('平均相关性', 'Average Correlation'))
                axes[0, 0].set_title(get_label('处理时间 vs 质量', 'Processing Time vs Quality'))
                axes[0, 0].grid(True, alpha=0.3)
                
                # 添加趋势线
                if len(processing_times) > 1:
                    z = np.polyfit(processing_times, correlations_original, 1)
                    p = np.poly1d(z)
                    axes[0, 0].plot(processing_times, p(processing_times), "r--", alpha=0.8)
            
            # 2. 原始信号 vs 参考信号相关性对比
            if correlations_original and correlations_reference:
                axes[0, 1].scatter(correlations_original, correlations_reference, 
                                 alpha=0.6, c='green', s=50)
                
                # 添加对角线
                min_val = min(min(correlations_original), min(correlations_reference))
                max_val = max(max(correlations_original), max(correlations_reference))
                axes[0, 1].plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
                
                axes[0, 1].set_xlabel(get_label('与原始信号相关性', 'Correlation to Original'))
                axes[0, 1].set_ylabel(get_label('与参考信号相关性', 'Correlation to Reference'))
                axes[0, 1].set_title(get_label('原始 vs 参考信号相关性', 'Original vs Reference Correlation'))
                axes[0, 1].grid(True, alpha=0.3)
            
            # 3. 质量指标趋势图
            if len(correlations_original) > 1:
                file_indices = range(len(correlations_original))
                
                axes[1, 0].plot(file_indices, correlations_original, 'o-', 
                              label=get_label('相关性', 'Correlation'), color='blue')
                
                if snrs and len(snrs) == len(correlations_original):
                    # 归一化SNR到0-1范围用于显示
                    snrs_normalized = [(s - min(snrs)) / (max(snrs) - min(snrs)) 
                                     for s in snrs] if max(snrs) != min(snrs) else snrs
                    axes[1, 0].plot(file_indices, snrs_normalized, 's-', 
                                  label=get_label('信噪比(归一化)', 'SNR(normalized)'), color='orange')
                
                axes[1, 0].set_xlabel(get_label('文件索引', 'File Index'))
                axes[1, 0].set_ylabel(get_label('指标值', 'Metric Value'))
                axes[1, 0].set_title(get_label('质量指标趋势', 'Quality Metrics Trend'))
                axes[1, 0].legend()
                axes[1, 0].grid(True, alpha=0.3)
            
            # 4. 质量等级汇总
            quality_summary = {
                get_label('处理成功率', 'Success Rate'): f"{self.batch_stats.successful_files/self.batch_stats.total_files*100:.1f}%",
                get_label('平均相关性', 'Avg Correlation'): f"{self.batch_stats.average_correlation:.3f}",
                get_label('平均信噪比', 'Avg SNR'): f"{self.batch_stats.average_snr:.2f}dB",
                get_label('总处理时间', 'Total Time'): f"{self.batch_stats.total_processing_time:.1f}s",
                get_label('平均每文件时间', 'Avg Time/File'): f"{self.batch_stats.total_processing_time/self.batch_stats.total_files:.1f}s"
            }
            
            summary_text = "\n".join([f"{k}: {v}" for k, v in quality_summary.items()])
            
            axes[1, 1].text(0.1, 0.9, summary_text, transform=axes[1, 1].transAxes,
                           fontsize=12, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))
            axes[1, 1].set_title(get_label('质量汇总', 'Quality Summary'))
            axes[1, 1].axis('off')
            
            plt.tight_layout()
            
            save_path = output_dir / "quality_analysis.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"质量分析图已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"生成质量分析图失败: {e}")
    
    def generate_batch_report(self, output_dir: str):
        """生成批量处理的综合报告"""
        try:
            output_path = Path(output_dir)
            
            # 生成HTML报告
            html_content = self._generate_batch_html_report()
            
            # 保存报告
            report_file = output_path / "batch_processing_report.html"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # 生成JSON统计数据
            stats_file = output_path / "batch_statistics.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.batch_stats), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"批量处理报告已保存: {report_file}")
            self.logger.info(f"批量统计数据已保存: {stats_file}")
            
        except Exception as e:
            self.logger.error(f"生成批量报告失败: {e}")
            raise
    
    def _generate_batch_html_report(self) -> str:
        """生成批量处理HTML报告"""
        try:
            processing_config = self.config_manager.get_processing_config()
            vmd_config = self.config_manager.get_vmd_config()
            current_policy = self.config_manager.config.get('current_policy', 'default')
            
            # 生成文件详细信息表格
            files_table_rows = ""
            for file_name, file_result in self.batch_results.items():
                status = "✅ 成功" if file_result.success else "❌ 失败"
                status_class = "success" if file_result.success else "error"
                
                channels_str = ", ".join(map(str, file_result.channels_processed)) if file_result.success else "无"
                
                avg_corr = ""
                avg_snr = ""
                if file_result.success and file_result.results:
                    correlations = []
                    snrs = []
                    for result in file_result.results.values():
                        if result.similarity_metrics:
                            corr = result.similarity_metrics.get('correlation_to_original', 0)
                            snr = result.similarity_metrics.get('snr_to_original', 0)
                            if corr > 0:
                                correlations.append(corr)
                            if snr > 0:
                                snrs.append(snr)
                    
                    if correlations:
                        avg_corr = f"{np.mean(correlations):.3f}"
                    if snrs:
                        avg_snr = f"{np.mean(snrs):.2f}"
                
                error_info = file_result.error_message if not file_result.success else ""
                
                files_table_rows += f"""
                <tr>
                    <td>{file_name}</td>
                    <td class="{status_class}">{status}</td>
                    <td>{file_result.processing_time:.2f}s</td>
                    <td>{channels_str}</td>
                    <td>{avg_corr}</td>
                    <td>{avg_snr}</td>
                    <td>{error_info}</td>
                </tr>
                """
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>批量处理报告</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
                    .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                             color: white; padding: 30px; border-radius: 10px; text-align: center; }}
                    .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                    .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
                    .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }}
                    .stat-value {{ font-size: 2em; font-weight: bold; color: #007bff; }}
                    .stat-label {{ color: #6c757d; margin-top: 5px; }}
                    table {{ border-collapse: collapse; width: 100%; margin: 15px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                    th {{ background-color: #f2f2f2; font-weight: bold; }}
                    .success {{ color: #28a745; font-weight: bold; }}
                    .error {{ color: #dc3545; font-weight: bold; }}
                    .warning {{ color: #ffc107; }}
                    .highlight {{ background-color: #fff3cd; }}
                    .quality-excellent {{ background-color: #d4edda; }}
                    .quality-good {{ background-color: #d1ecf1; }}
                    .quality-fair {{ background-color: #ffeaa7; }}
                    .quality-poor {{ background-color: #f8d7da; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>🚀 VMD批量处理综合报告</h1>
                    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="section">
                    <h2>📊 处理概览</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.total_files}</div>
                            <div class="stat-label">总文件数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.successful_files}</div>
                            <div class="stat-label">成功处理</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.failed_files}</div>
                            <div class="stat-label">处理失败</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.successful_files/self.batch_stats.total_files*100:.1f}%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.total_channels}</div>
                            <div class="stat-label">总通道数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.total_processing_time:.1f}s</div>
                            <div class="stat-label">总处理时间</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>⚙️ 处理配置</h2>
                    <table>
                        <tr><td><strong>处理通道</strong></td><td>{processing_config.channels}</td></tr>
                        <tr><td><strong>信号长度</strong></td><td>{processing_config.sample_length} 点</td></tr>
                        <tr><td><strong>采样率</strong></td><td>{processing_config.sampling_rate} Hz</td></tr>
                        <tr><td><strong>VMD模态数</strong></td><td>{vmd_config.K}</td></tr>
                        <tr><td><strong>Alpha参数</strong></td><td>{vmd_config.alpha}</td></tr>
                        <tr><td><strong>滤波策略</strong></td><td>{current_policy}</td></tr>
                    </table>
                </div>
                
                <div class="section">
                    <h2>📈 质量统计</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.average_correlation:.3f}</div>
                            <div class="stat-label">平均相关性</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.average_snr:.2f}dB</div>
                            <div class="stat-label">平均信噪比</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{self.batch_stats.total_processing_time/self.batch_stats.total_files:.1f}s</div>
                            <div class="stat-label">平均每文件时间</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h2>📋 文件处理详情</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>状态</th>
                                <th>处理时间</th>
                                <th>处理通道</th>
                                <th>平均相关性</th>
                                <th>平均信噪比</th>
                                <th>错误信息</th>
                            </tr>
                        </thead>
                        <tbody>
                            {files_table_rows}
                        </tbody>
                    </table>
                </div>
                
                <div class="section">
                    <h2>🎯 处理建议</h2>
                    <ul>
                        <li><strong>成功率评估:</strong> 
                            {'✅ 优秀' if self.batch_stats.successful_files/self.batch_stats.total_files >= 0.9 
                             else '⚠️ 需要关注' if self.batch_stats.successful_files/self.batch_stats.total_files >= 0.7 
                             else '❌ 需要优化'}
                        </li>
                        <li><strong>质量评估:</strong> 
                            {'✅ 优秀' if self.batch_stats.average_correlation >= 0.8 
                             else '⚠️ 良好' if self.batch_stats.average_correlation >= 0.6 
                             else '❌ 较差'}
                        </li>
                        <li><strong>性能评估:</strong> 
                            平均每文件处理时间 {self.batch_stats.total_processing_time/self.batch_stats.total_files:.1f}秒
                        </li>
                    </ul>
                </div>
                
                <div class="section">
                    <h2>📁 输出文件</h2>
                    <ul>
                        <li>📊 综合可视化图表: batch_analysis/目录</li>
                        <li>📈 单文件处理结果: 各文件对应的输出目录</li>
                        <li>📋 统计数据: batch_statistics.json</li>
                        <li>📄 本报告: batch_processing_report.html</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h2>🔧 技术说明</h2>
                    <p>本报告基于VMD（变分模态分解）技术，对批量数据进行降噪处理和质量分析。</p>
                    <p>报告包含详细的统计指标、可视化分析和质量评估，便于全面了解批量处理效果。</p>
                </div>
            </body>
            </html>
            """
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            raise