"""
Noise 2 Sim - Unsupervised Denoising for Multi-Channel MCG Data
Author: Assistant
Date: 2025-07-17
Description: Implementation of Noise2Void-inspired denoising for 61-channel MCG signals
"""

from .dataset import MCGDataset
from .model import UNet1D
from .config import Config
from .utils import *
from .train import train_model
from .inference import denoise_signal
from .evaluation import evaluate_denoising

__version__ = "1.0.0"
__all__ = [
    "MCGDataset",
    "UNet1D", 
    "Config",
    "train_model",
    "denoise_signal",
    "evaluate_denoising"
]