"""
评估模块
Author: Assistant
Date: 2025-07-17
Description: 多维度降噪效果评估，包含定量指标和可视化分析
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from scipy import signal
from scipy.stats import pearsonr
import logging

logger = logging.getLogger(__name__)

class DenoiseEvaluator:
    """降噪效果评估器"""
    
    def __init__(self, sampling_rate: int = 1000):
        self.sampling_rate = sampling_rate
        self.logger = logging.getLogger(__name__)
    
    def calculate_correlation_with_references(self, denoised_signals: np.ndarray, 
                                           reference_signals: np.ndarray,
                                           channel_groups: Dict[int, List[int]]) -> Dict[str, float]:
        """
        计算降噪后信号与4个标准源的相关性
        
        Args:
            denoised_signals: 降噪后的信号 (samples, 36_channels)
            reference_signals: 4个标准源信号 (samples, 4)
            channel_groups: 通道分组信息
            
        Returns:
            correlation_metrics: 相关性指标字典
        """
        try:
            metrics = {}
            all_correlations = []
            
            # 对每个参考通道组计算相关性
            for ref_idx, signal_channels in channel_groups.items():
                if ref_idx >= reference_signals.shape[1]:
                    continue
                
                ref_signal = reference_signals[:, ref_idx]
                group_correlations = []
                
                for ch_idx in signal_channels:
                    if ch_idx < denoised_signals.shape[1]:
                        # 调整索引（channel_groups中可能是1-based）
                        actual_idx = ch_idx - 1 if ch_idx > 0 else ch_idx
                        if actual_idx < denoised_signals.shape[1]:
                            denoised_signal = denoised_signals[:, actual_idx]
                            
                            # 确保信号长度一致
                            min_length = min(len(ref_signal), len(denoised_signal))
                            correlation, _ = pearsonr(ref_signal[:min_length], 
                                                    denoised_signal[:min_length])
                            
                            # 使用绝对值相关性（如用户要求）
                            abs_correlation = abs(correlation)
                            group_correlations.append(abs_correlation)
                            all_correlations.append(abs_correlation)
                
                if group_correlations:
                    metrics[f'ref_{ref_idx+1}_mean_correlation'] = np.mean(group_correlations)
                    metrics[f'ref_{ref_idx+1}_max_correlation'] = np.max(group_correlations)
                    metrics[f'ref_{ref_idx+1}_std_correlation'] = np.std(group_correlations)
            
            # 整体指标
            if all_correlations:
                metrics['overall_mean_correlation'] = np.mean(all_correlations)
                metrics['overall_max_correlation'] = np.max(all_correlations)
                metrics['overall_min_correlation'] = np.min(all_correlations)
                metrics['overall_std_correlation'] = np.std(all_correlations)
            
            self.logger.info(f"相关性分析完成，整体平均相关性: {metrics.get('overall_mean_correlation', 0):.3f}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算与标准源相关性失败: {e}")
            return {}
    
    def estimate_snr_improvement(self, original_signals: np.ndarray, 
                               denoised_signals: np.ndarray) -> Dict[str, float]:
        """
        估算信噪比改善
        
        Args:
            original_signals: 原始信号
            denoised_signals: 降噪后信号
            
        Returns:
            snr_metrics: SNR指标字典
        """
        try:
            metrics = {}
            
            if original_signals.ndim == 1:
                original_signals = original_signals.reshape(-1, 1)
            if denoised_signals.ndim == 1:
                denoised_signals = denoised_signals.reshape(-1, 1)
            
            snr_improvements = []
            snr_original_list = []
            snr_denoised_list = []
            
            for ch in range(min(original_signals.shape[1], denoised_signals.shape[1])):
                original = original_signals[:, ch]
                denoised = denoised_signals[:, ch]
                
                # 确保信号长度一致
                min_length = min(len(original), len(denoised))
                original = original[:min_length]
                denoised = denoised[:min_length]
                
                # 计算噪声（原始信号 - 降噪信号）
                noise_removed = original - denoised
                
                # 估算原始SNR（假设降噪信号更接近真实信号）
                signal_power_orig = np.var(denoised)  # 使用降噪信号作为信号功率估计
                noise_power_orig = np.var(noise_removed)  # 移除的噪声功率
                
                # 估算降噪后SNR
                signal_power_denoised = np.var(denoised)
                noise_power_denoised = np.var(original - denoised)  # 残余噪声
                
                # 避免除零
                if noise_power_orig > 1e-10:
                    snr_original = 10 * np.log10(signal_power_orig / noise_power_orig)
                else:
                    snr_original = float('inf')
                
                if noise_power_denoised > 1e-10:
                    snr_denoised = 10 * np.log10(signal_power_denoised / noise_power_denoised)
                else:
                    snr_denoised = float('inf')
                
                # SNR改善量
                if snr_original != float('inf') and snr_denoised != float('inf'):
                    snr_improvement = snr_denoised - snr_original
                else:
                    snr_improvement = 0
                
                snr_improvements.append(snr_improvement)
                snr_original_list.append(snr_original if snr_original != float('inf') else 100)
                snr_denoised_list.append(snr_denoised if snr_denoised != float('inf') else 100)
            
            # 整体指标
            if snr_improvements:
                metrics['mean_snr_improvement'] = np.mean(snr_improvements)
                metrics['median_snr_improvement'] = np.median(snr_improvements)
                metrics['std_snr_improvement'] = np.std(snr_improvements)
                metrics['min_snr_improvement'] = np.min(snr_improvements)
                metrics['max_snr_improvement'] = np.max(snr_improvements)
                
                metrics['mean_snr_original'] = np.mean(snr_original_list)
                metrics['mean_snr_denoised'] = np.mean(snr_denoised_list)
            
            self.logger.info(f"SNR分析完成，平均改善: {metrics.get('mean_snr_improvement', 0):.2f} dB")
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算SNR改善失败: {e}")
            return {}
    
    def analyze_residuals(self, original_signals: np.ndarray, 
                         denoised_signals: np.ndarray) -> Dict[str, float]:
        """
        残差分析：计算残差能量和白噪声特性
        
        Args:
            original_signals: 原始信号
            denoised_signals: 降噪后信号
            
        Returns:
            residual_metrics: 残差指标字典
        """
        try:
            metrics = {}
            
            if original_signals.ndim == 1:
                original_signals = original_signals.reshape(-1, 1)
            if denoised_signals.ndim == 1:
                denoised_signals = denoised_signals.reshape(-1, 1)
            
            all_residual_energies = []
            all_whiteness_scores = []
            all_kurtosis_values = []
            
            for ch in range(min(original_signals.shape[1], denoised_signals.shape[1])):
                original = original_signals[:, ch]
                denoised = denoised_signals[:, ch]
                
                # 确保信号长度一致
                min_length = min(len(original), len(denoised))
                residual = original[:min_length] - denoised[:min_length]
                
                # 残差能量
                residual_energy = np.var(residual)
                signal_energy = np.var(original[:min_length])
                relative_residual_energy = residual_energy / signal_energy if signal_energy > 0 else 0
                
                all_residual_energies.append(relative_residual_energy)
                
                # 白噪声特性分析
                whiteness_score = self._assess_whiteness(residual)
                all_whiteness_scores.append(whiteness_score)
                
                # 峰度分析（高斯性检验）
                from scipy.stats import kurtosis
                kurt = kurtosis(residual)
                all_kurtosis_values.append(kurt)
            
            # 整体指标
            if all_residual_energies:
                metrics['mean_relative_residual_energy'] = np.mean(all_residual_energies)
                metrics['median_relative_residual_energy'] = np.median(all_residual_energies)
                metrics['std_relative_residual_energy'] = np.std(all_residual_energies)
                
                metrics['mean_whiteness_score'] = np.mean(all_whiteness_scores)
                metrics['mean_kurtosis'] = np.mean(all_kurtosis_values)
                
                # 残差能量分级
                low_residual_ratio = np.mean(np.array(all_residual_energies) < 0.1)
                metrics['low_residual_energy_ratio'] = low_residual_ratio
            
            self.logger.info(f"残差分析完成，平均相对残差能量: {metrics.get('mean_relative_residual_energy', 0):.3f}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"残差分析失败: {e}")
            return {}
    
    def _assess_whiteness(self, signal: np.ndarray, max_lag: int = 50) -> float:
        """
        评估信号的白噪声特性（通过自相关函数）
        
        Args:
            signal: 输入信号
            max_lag: 最大滞后数
            
        Returns:
            whiteness_score: 白噪声得分 (0-1，1表示完全白噪声)
        """
        try:
            # 计算自相关函数
            correlations = []
            signal_normalized = (signal - np.mean(signal)) / np.std(signal)
            
            for lag in range(1, min(max_lag + 1, len(signal) // 4)):
                if lag < len(signal):
                    correlation = np.corrcoef(signal_normalized[:-lag], 
                                           signal_normalized[lag:])[0, 1]
                    correlations.append(abs(correlation))
            
            if correlations:
                # 白噪声得分：自相关系数的倒数
                mean_autocorr = np.mean(correlations)
                whiteness_score = 1 / (1 + 10 * mean_autocorr)  # 映射到0-1
            else:
                whiteness_score = 0.5
            
            return whiteness_score
            
        except Exception as e:
            return 0.5
    
    def comprehensive_evaluation(self, original_signals: np.ndarray,
                               denoised_signals: np.ndarray,
                               reference_signals: np.ndarray,
                               channel_groups: Dict[int, List[int]],
                               method_name: str = "Unknown") -> Dict[str, any]:
        """
        综合评估
        
        Args:
            original_signals: 原始信号
            denoised_signals: 降噪后信号  
            reference_signals: 标准源信号
            channel_groups: 通道分组
            method_name: 方法名称
            
        Returns:
            evaluation_results: 综合评估结果
        """
        try:
            self.logger.info(f"开始综合评估: {method_name}")
            
            results = {
                'method_name': method_name,
                'correlation_metrics': {},
                'snr_metrics': {},
                'residual_metrics': {},
                'overall_score': 0.0
            }
            
            # 相关性评估
            results['correlation_metrics'] = self.calculate_correlation_with_references(
                denoised_signals, reference_signals, channel_groups
            )
            
            # SNR评估
            results['snr_metrics'] = self.estimate_snr_improvement(
                original_signals, denoised_signals
            )
            
            # 残差评估
            results['residual_metrics'] = self.analyze_residuals(
                original_signals, denoised_signals
            )
            
            # 计算综合得分
            results['overall_score'] = self._calculate_overall_score(results)
            
            self.logger.info(f"{method_name} 评估完成，综合得分: {results['overall_score']:.3f}")
            return results
            
        except Exception as e:
            self.logger.error(f"综合评估失败: {e}")
            return {'method_name': method_name, 'error': str(e)}
    
    def _calculate_overall_score(self, results: Dict) -> float:
        """
        计算综合得分
        
        Args:
            results: 评估结果
            
        Returns:
            overall_score: 综合得分 (0-1)
        """
        try:
            score_components = []
            
            # 相关性得分 (权重: 0.4)
            corr_metrics = results.get('correlation_metrics', {})
            if 'overall_mean_correlation' in corr_metrics:
                corr_score = corr_metrics['overall_mean_correlation']
                score_components.append(('correlation', corr_score, 0.4))
            
            # SNR改善得分 (权重: 0.3)
            snr_metrics = results.get('snr_metrics', {})
            if 'mean_snr_improvement' in snr_metrics:
                snr_improvement = snr_metrics['mean_snr_improvement']
                # 将SNR改善映射到0-1分数
                snr_score = np.tanh(snr_improvement / 10)  # 10dB改善映射到约0.76分
                snr_score = max(0, snr_score)  # 确保非负
                score_components.append(('snr', snr_score, 0.3))
            
            # 残差得分 (权重: 0.3)
            residual_metrics = results.get('residual_metrics', {})
            if 'mean_relative_residual_energy' in residual_metrics:
                residual_energy = residual_metrics['mean_relative_residual_energy']
                # 残差能量越低越好
                residual_score = np.exp(-5 * residual_energy)  # 0.2能量比映射到约0.37分
                score_components.append(('residual', residual_score, 0.3))
            
            # 计算加权平均
            if score_components:
                weighted_sum = sum(score * weight for _, score, weight in score_components)
                total_weight = sum(weight for _, _, weight in score_components)
                overall_score = weighted_sum / total_weight if total_weight > 0 else 0
            else:
                overall_score = 0.0
            
            return overall_score
            
        except Exception as e:
            self.logger.error(f"计算综合得分失败: {e}")
            return 0.0
    
    def visualize_evaluation_results(self, evaluation_results: List[Dict]):
        """
        可视化评估结果对比
        
        Args:
            evaluation_results: 多个方法的评估结果列表
        """
        try:
            if not evaluation_results:
                return
            
            n_methods = len(evaluation_results)
            method_names = [result['method_name'] for result in evaluation_results]
            
            fig, axes = plt.subplots(2, 3, figsize=(18, 10))
            
            # 1. 综合得分对比
            overall_scores = [result.get('overall_score', 0) for result in evaluation_results]
            colors = plt.cm.Set3(np.linspace(0, 1, n_methods))
            
            bars1 = axes[0, 0].bar(method_names, overall_scores, color=colors)
            axes[0, 0].set_title('Overall Performance Score')
            axes[0, 0].set_ylabel('Score (0-1)')
            axes[0, 0].tick_params(axis='x', rotation=45)
            axes[0, 0].grid(True, alpha=0.3)
            
            # 在柱状图上显示数值
            for bar, score in zip(bars1, overall_scores):
                height = bar.get_height()
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                              f'{score:.3f}', ha='center', va='bottom')
            
            # 2. 相关性对比
            mean_correlations = []
            max_correlations = []
            for result in evaluation_results:
                corr_metrics = result.get('correlation_metrics', {})
                mean_correlations.append(corr_metrics.get('overall_mean_correlation', 0))
                max_correlations.append(corr_metrics.get('overall_max_correlation', 0))
            
            x = np.arange(n_methods)
            width = 0.35
            
            axes[0, 1].bar(x - width/2, mean_correlations, width, label='Mean', color='skyblue')
            axes[0, 1].bar(x + width/2, max_correlations, width, label='Max', color='lightcoral')
            axes[0, 1].set_title('Correlation with References')
            axes[0, 1].set_ylabel('Correlation Coefficient')
            axes[0, 1].set_xticks(x)
            axes[0, 1].set_xticklabels(method_names, rotation=45)
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. SNR改善对比
            snr_improvements = []
            for result in evaluation_results:
                snr_metrics = result.get('snr_metrics', {})
                snr_improvements.append(snr_metrics.get('mean_snr_improvement', 0))
            
            bars3 = axes[0, 2].bar(method_names, snr_improvements, color=colors)
            axes[0, 2].set_title('SNR Improvement')
            axes[0, 2].set_ylabel('SNR Improvement (dB)')
            axes[0, 2].tick_params(axis='x', rotation=45)
            axes[0, 2].grid(True, alpha=0.3)
            
            for bar, snr in zip(bars3, snr_improvements):
                height = bar.get_height()
                axes[0, 2].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                              f'{snr:.1f}', ha='center', va='bottom')
            
            # 4. 残差能量对比
            residual_energies = []
            whiteness_scores = []
            for result in evaluation_results:
                residual_metrics = result.get('residual_metrics', {})
                residual_energies.append(residual_metrics.get('mean_relative_residual_energy', 0))
                whiteness_scores.append(residual_metrics.get('mean_whiteness_score', 0))
            
            axes[1, 0].bar(x - width/2, residual_energies, width, label='Residual Energy', color='orange')
            axes[1, 0].bar(x + width/2, whiteness_scores, width, label='Whiteness Score', color='green')
            axes[1, 0].set_title('Residual Analysis')
            axes[1, 0].set_ylabel('Relative Energy / Whiteness Score')
            axes[1, 0].set_xticks(x)
            axes[1, 0].set_xticklabels(method_names, rotation=45)
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            
            # 5. 雷达图 - 多维度性能对比
            categories = ['Correlation', 'SNR Improvement', 'Low Residual', 'Whiteness']
            
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            angles += angles[:1]  # 闭合图形
            
            ax_radar = plt.subplot(1, 3, 2, projection='polar')
            
            for i, result in enumerate(evaluation_results):
                # 标准化指标到0-1范围
                corr_score = result.get('correlation_metrics', {}).get('overall_mean_correlation', 0)
                snr_score = min(1, max(0, result.get('snr_metrics', {}).get('mean_snr_improvement', 0) / 20))
                residual_score = 1 - min(1, result.get('residual_metrics', {}).get('mean_relative_residual_energy', 1))
                whiteness_score = result.get('residual_metrics', {}).get('mean_whiteness_score', 0)
                
                values = [corr_score, snr_score, residual_score, whiteness_score]
                values += values[:1]  # 闭合图形
                
                ax_radar.plot(angles, values, 'o-', linewidth=2, label=method_names[i], color=colors[i])
                ax_radar.fill(angles, values, alpha=0.1, color=colors[i])
            
            ax_radar.set_xticks(angles[:-1])
            ax_radar.set_xticklabels(categories)
            ax_radar.set_ylim(0, 1)
            ax_radar.set_title('Multi-dimensional Performance', pad=20)
            ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
            ax_radar.grid(True)
            
            # 将雷达图添加到主图形中
            plt.figure(fig.number)
            axes[1, 1].remove()
            ax_radar_new = fig.add_subplot(2, 3, 5, projection='polar')
            
            for i, result in enumerate(evaluation_results):
                corr_score = result.get('correlation_metrics', {}).get('overall_mean_correlation', 0)
                snr_score = min(1, max(0, result.get('snr_metrics', {}).get('mean_snr_improvement', 0) / 20))
                residual_score = 1 - min(1, result.get('residual_metrics', {}).get('mean_relative_residual_energy', 1))
                whiteness_score = result.get('residual_metrics', {}).get('mean_whiteness_score', 0)
                
                values = [corr_score, snr_score, residual_score, whiteness_score]
                values += values[:1]
                
                ax_radar_new.plot(angles, values, 'o-', linewidth=2, label=method_names[i], color=colors[i])
                ax_radar_new.fill(angles, values, alpha=0.1, color=colors[i])
            
            ax_radar_new.set_xticks(angles[:-1])
            ax_radar_new.set_xticklabels(categories)
            ax_radar_new.set_ylim(0, 1)
            ax_radar_new.set_title('Multi-dimensional Performance')
            ax_radar_new.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
            ax_radar_new.grid(True)
            
            # 6. 详细指标表格
            axes[1, 2].axis('off')
            
            # 创建表格数据
            table_data = []
            headers = ['Method', 'Overall', 'Correlation', 'SNR (dB)', 'Residual']
            
            for result in evaluation_results:
                method = result['method_name']
                overall = f"{result.get('overall_score', 0):.3f}"
                correlation = f"{result.get('correlation_metrics', {}).get('overall_mean_correlation', 0):.3f}"
                snr = f"{result.get('snr_metrics', {}).get('mean_snr_improvement', 0):.1f}"
                residual = f"{result.get('residual_metrics', {}).get('mean_relative_residual_energy', 0):.3f}"
                
                table_data.append([method, overall, correlation, snr, residual])
            
            table = axes[1, 2].table(cellText=table_data,
                                   colLabels=headers,
                                   cellLoc='center',
                                   loc='center',
                                   colWidths=[0.25, 0.15, 0.2, 0.15, 0.15])
            
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 2)
            axes[1, 2].set_title('Detailed Metrics')
            
            plt.tight_layout()
            plt.show()
            
            # 打印详细报告
            self._print_evaluation_report(evaluation_results)
            
        except Exception as e:
            self.logger.error(f"可视化评估结果失败: {e}")
    
    def _print_evaluation_report(self, evaluation_results: List[Dict]):
        """打印详细评估报告"""
        print("\n" + "="*80)
        print("降噪方法综合评估报告")
        print("="*80)
        
        for i, result in enumerate(evaluation_results):
            method = result['method_name']
            print(f"\n{i+1}. {method}")
            print("-" * 50)
            
            # 综合得分
            overall_score = result.get('overall_score', 0)
            print(f"综合得分: {overall_score:.3f}")
            
            # 相关性指标
            corr_metrics = result.get('correlation_metrics', {})
            if corr_metrics:
                print(f"平均相关性: {corr_metrics.get('overall_mean_correlation', 0):.3f}")
                print(f"最大相关性: {corr_metrics.get('overall_max_correlation', 0):.3f}")
            
            # SNR指标
            snr_metrics = result.get('snr_metrics', {})
            if snr_metrics:
                print(f"SNR改善: {snr_metrics.get('mean_snr_improvement', 0):.2f} dB")
                print(f"原始SNR: {snr_metrics.get('mean_snr_original', 0):.2f} dB")
                print(f"降噪后SNR: {snr_metrics.get('mean_snr_denoised', 0):.2f} dB")
            
            # 残差指标
            residual_metrics = result.get('residual_metrics', {})
            if residual_metrics:
                print(f"相对残差能量: {residual_metrics.get('mean_relative_residual_energy', 0):.3f}")
                print(f"白噪声得分: {residual_metrics.get('mean_whiteness_score', 0):.3f}")
        
        # 排名
        print("\n" + "="*50)
        print("性能排名 (按综合得分)")
        print("="*50)
        
        sorted_results = sorted(evaluation_results, 
                              key=lambda x: x.get('overall_score', 0), 
                              reverse=True)
        
        for i, result in enumerate(sorted_results):
            method = result['method_name']
            score = result.get('overall_score', 0)
            print(f"{i+1:2d}. {method:<25} {score:.3f}")
        
        print("="*80)

def plot_wave_comparison(original: np.ndarray, denoised: np.ndarray, 
                        reference: Optional[np.ndarray] = None,
                        sampling_rate: int = 1000, title: str = "Signal Comparison"):
    """
    波形对比图：叠加显示、单独源信号、单独降噪后
    
    Args:
        original: 原始信号
        denoised: 降噪后信号
        reference: 参考信号（可选）
        sampling_rate: 采样率
        title: 图标题
    """
    time_axis = np.arange(len(original)) / sampling_rate
    
    fig, axes = plt.subplots(3, 1, figsize=(12, 10))
    
    # 1. 叠加显示
    axes[0].plot(time_axis, original, 'b-', alpha=0.7, label='Original')
    axes[0].plot(time_axis, denoised, 'r-', label='Denoised')
    if reference is not None:
        axes[0].plot(time_axis, reference, 'g-', alpha=0.8, label='Reference')
    axes[0].set_title(f'{title} - Overlapped View')
    axes[0].set_ylabel('Amplitude')
    axes[0].legend()
    axes[0].grid(True)
    
    # 2. 单独源信号
    axes[1].plot(time_axis, original, 'b-')
    axes[1].set_title('Original Signal')
    axes[1].set_ylabel('Amplitude')
    axes[1].grid(True)
    
    # 3. 单独降噪后
    axes[2].plot(time_axis, denoised, 'r-')
    axes[2].set_title('Denoised Signal')
    axes[2].set_xlabel('Time (s)')
    axes[2].set_ylabel('Amplitude')
    axes[2].grid(True)
    
    plt.tight_layout()
    plt.show()

def plot_spectrum_comparison(original: np.ndarray, denoised: np.ndarray,
                           sampling_rate: int = 1000, title: str = "Spectrum Comparison"):
    """
    频谱对比图：绘制原始信号和降噪后信号的功率谱密度图
    
    Args:
        original: 原始信号
        denoised: 降噪后信号
        sampling_rate: 采样率
        title: 图标题
    """
    from scipy.signal import welch
    
    # 计算功率谱密度
    freqs_orig, psd_orig = welch(original, sampling_rate, nperseg=min(1024, len(original)//4))
    freqs_denoised, psd_denoised = welch(denoised, sampling_rate, nperseg=min(1024, len(denoised)//4))
    
    fig, axes = plt.subplots(2, 1, figsize=(12, 8))
    
    # 1. 功率谱密度对比
    axes[0].semilogy(freqs_orig, psd_orig, 'b-', alpha=0.7, label='Original')
    axes[0].semilogy(freqs_denoised, psd_denoised, 'r-', label='Denoised')
    axes[0].set_title(f'{title} - Power Spectral Density')
    axes[0].set_xlabel('Frequency (Hz)')
    axes[0].set_ylabel('PSD')
    axes[0].legend()
    axes[0].grid(True)
    
    # 2. 噪声移除效果（频域）
    noise_spectrum = psd_orig - psd_denoised
    axes[1].plot(freqs_orig, noise_spectrum, 'g-', label='Removed Noise Spectrum')
    axes[1].set_title('Noise Removal in Frequency Domain')
    axes[1].set_xlabel('Frequency (Hz)')
    axes[1].set_ylabel('PSD Difference')
    axes[1].legend()
    axes[1].grid(True)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # 测试示例
    evaluator = DenoiseEvaluator(sampling_rate=1000)
    
    # 生成测试数据
    np.random.seed(42)
    t = np.linspace(0, 2, 2000)
    
    # 多通道信号
    signals = []
    for i in range(4):
        clean = np.sin(2*np.pi*(10+i*5)*t) + 0.5*np.sin(2*np.pi*(25+i*3)*t)
        noise = 0.3 * np.random.randn(len(clean))
        signals.append(clean + noise)
    
    original_signals = np.column_stack(signals)
    
    # 模拟不同的降噪结果
    denoised_signals_1 = original_signals + 0.1 * np.random.randn(*original_signals.shape)
    denoised_signals_2 = original_signals + 0.05 * np.random.randn(*original_signals.shape)
    
    # 参考信号
    reference_signals = np.column_stack([
        np.sin(2*np.pi*15*t), np.sin(2*np.pi*20*t),
        np.sin(2*np.pi*25*t), np.sin(2*np.pi*30*t)
    ])
    
    # 通道分组
    channel_groups = {0: [0], 1: [1], 2: [2], 3: [3]}
    
    # 评估
    result1 = evaluator.comprehensive_evaluation(original_signals, denoised_signals_1,
                                               reference_signals, channel_groups, "Method 1")
    result2 = evaluator.comprehensive_evaluation(original_signals, denoised_signals_2,
                                               reference_signals, channel_groups, "Method 2")
    
    # 可视化对比
    evaluator.visualize_evaluation_results([result1, result2])