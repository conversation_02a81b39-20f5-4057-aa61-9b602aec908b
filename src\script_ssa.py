"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_ssa
date: 2025/7/22 11:52
desc: 
"""
# streamlit_app.py
import streamlit as st
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


# =============================================================================
# ▼▼▼ 将上面重构好的 SsaDenoiser 类代码复制到这里 ▼▼▼
# =============================================================================
class SsaDenoiser:
    """
    使用奇异谱分析（SSA）对时间序列进行去噪。
    这个版本经过修改，允许选择任意成分进行重构。
    """

    def __init__(self, window_length: int):
        if window_length < 2:
            raise ValueError("窗口长度必须大于等于2。")
        self.L = window_length
        self.X = None
        self.U = None
        self.s = None
        self.Vh = None

    def _embed(self, signal: np.ndarray) -> np.ndarray:
        N = len(signal)
        if N < self.L:
            raise ValueError(f"信号长度({N})必须大于或等于窗口长度({self.L})。")
        K = N - self.L + 1
        return np.column_stack([signal[i:i + self.L] for i in range(K)])

    def _diagonal_averaging(self, matrix: np.ndarray) -> np.ndarray:
        N = matrix.shape[1] + self.L - 1
        reconstructed_signal = np.zeros(N)
        counts = np.zeros(N)
        for i in range(self.L):
            for j in range(matrix.shape[1]):
                index = i + j
                reconstructed_signal[index] += matrix[i, j]
                counts[index] += 1
        counts[counts == 0] = 1
        return reconstructed_signal / counts

    def decompose(self, signal: np.ndarray):
        self.X = self._embed(signal)
        self.U, self.s, self.Vh = np.linalg.svd(self.X, full_matrices=False)
        return self

    def reconstruct(self, component_indices: list) -> np.ndarray:
        if self.U is None:
            raise RuntimeError("必须先调用 decompose() 方法。")
        if not component_indices:  # 如果列表为空，返回零信号
            N = self.X.shape[1] + self.L - 1
            return np.zeros(N)
        indices = np.array(component_indices)
        X_reconstructed = self.U[:, indices] @ np.diag(self.s[indices]) @ self.Vh[indices, :]
        return self._diagonal_averaging(X_reconstructed)


# =============================================================================
# ▲▲▲ SsaDenoiser 类代码结束 ▲▲▲
# =============================================================================


# --- Streamlit 应用缓存函数 ---
@st.cache_data
def load_data(uploaded_file):
    """从上传的文件加载数据"""
    try:
        # 使用pandas读取，更稳健，可以处理各种分隔符
        df = pd.read_csv(uploaded_file, sep=r'\s+', header=None, engine='python')
        return df.to_numpy()
    except Exception as e:
        st.error(f"加载文件时出错: {e}")
        return None


@st.cache_data
def perform_decomposition(_ssa_denoiser, signal):
    """缓存SVD分解的结果"""
    _ssa_denoiser.decompose(signal)
    return _ssa_denoiser


# --- 绘图函数 ---
def plot_results(original, filtered, ssa, selected_indices):
    """绘制所有结果的函数"""
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    fig, axes = plt.subplots(3, 1, figsize=(15, 18), gridspec_kw={'height_ratios': [2, 3, 3]})

    # 1. 奇异值谱图 (Scree Plot)
    ax1 = axes[0]
    singular_values = ssa.s
    all_indices = np.arange(len(singular_values))

    # 绘制所有奇异值
    ax1.plot(all_indices, np.log10(singular_values), 'o-', color='grey', alpha=0.6, label='所有奇异值 (对数尺度)')
    # 突出显示选中的奇异值
    if selected_indices:
        ax1.plot(selected_indices, np.log10(singular_values[selected_indices]), 'o', color='crimson', markersize=8,
                 label='选中的成分')

    ax1.set_title('奇异值谱图 (用于选择成分)', fontsize=16)
    ax1.set_xlabel('成分索引')
    ax1.set_ylabel('log10(奇异值)')
    ax1.legend()
    ax1.grid(True, which='both')
    ax1.set_xticks(np.arange(0, len(singular_values), step=max(1, len(singular_values) // 25)))

    # 2. 时域去噪效果对比
    ax2 = axes[1]
    noise = original - filtered
    ax2.plot(original, color='dodgerblue', alpha=0.8, label='原始信号')
    ax2.plot(filtered, color='crimson', linewidth=2.5, label='重构信号 (所选成分之和)')
    ax2.set_title('时域去噪效果对比', fontsize=16)
    ax2.set_xlabel('样本点 (Sample)')
    ax2.set_ylabel('幅值')
    ax2.legend()
    ax2.grid(True)
    ax2.set_xlim(0, len(original))

    # 3. 提取的噪声
    ax3 = axes[2]
    ax3.plot(noise, color='green', label='被剔除的成分 (原始信号 - 重构信号)')
    ax3.set_title('被剔除的成分 (噪声)', fontsize=16)
    ax3.set_xlabel('样本点 (Sample)')
    ax3.set_ylabel('幅值')
    ax3.legend()
    ax3.grid(True)
    ax3.set_xlim(0, len(original))

    plt.tight_layout(pad=3.0)
    st.pyplot(fig)


# =============================================================================
# ▼▼▼ 主应用界面 ▼▼▼
# =============================================================================
st.set_page_config(layout="wide")
st.title('交互式SSA降噪分析工具')
st.markdown("""
这是一个用于奇异谱分析（SSA）降噪的交互式工具。您可以上传自己的数据，
并通过调整参数来探索SSA的效果，**核心功能是自由选择任意组合的奇异值成分**进行信号重构。
""")

# --- 侧边栏：参数配置 ---
st.sidebar.header("⚙️ 参数配置")

# 1. 文件上传
st.sidebar.subheader("1. 上传数据文件")
uploaded_file = st.sidebar.file_uploader("选择一个.txt或.csv文件", type=['txt', 'csv'])

if uploaded_file is None:
    st.info("请在左侧侧边栏上传您的数据文件开始分析。")
    st.stop()

# 加载数据
raw_data = load_data(uploaded_file)
if raw_data is None:
    st.stop()

# --- 在主界面显示数据信息 ---
st.header("📊 数据概览")
st.write(f"成功加载文件: `{uploaded_file.name}`")
st.write(f"数据形状 (样本数, 通道数): `{raw_data.shape}`")
st.dataframe(pd.DataFrame(raw_data).head())

# --- 侧边栏：数据选择 ---
st.sidebar.subheader("2. 数据选择")
n_samples, n_channels = raw_data.shape
fs = 1000  # 默认采样率

# 通道选择
channel_options = list(range(n_channels))
# 默认选择通道14，如果存在的话
default_channel = 14 if 14 < n_channels else 0
channel_idx = st.sidebar.selectbox("选择数据通道", options=channel_options, index=default_channel)

# 采样点范围选择
# 默认选择 10000 到 24000，如果数据足够长
default_start = 10000 if n_samples > 10000 else 0
default_end = 24000 if n_samples > 24000 else n_samples
start_sample, end_sample = st.sidebar.slider(
    "选择采样点范围",
    0, n_samples - 1,
    (default_start, default_end)
)
signal_to_test = raw_data[start_sample:end_sample, channel_idx]

# --- 侧边栏：SSA参数 ---
st.sidebar.subheader("3. SSA参数")

# 窗口长度L
# L不能大于信号长度
max_L = len(signal_to_test) // 2  # 建议L不超过信号长度的一半
if max_L < 2:
    st.error(f"所选信号段太短({len(signal_to_test)}个点)，无法进行SSA分析。请在侧边栏选择更长的采样点范围。")
    st.stop()
window_length = st.sidebar.slider("窗口长度 (L)", min_value=10, max_value=max_L, value=min(400, max_L), step=10)
st.sidebar.info(f"窗口长度`L`决定了可以分解出的最大成分数量，即`L`个。")

# --- 执行SSA分解 ---
st.header("🔬 SSA 分析与重构")
try:
    # 实例化
    ssa = SsaDenoiser(window_length=window_length)
    # 执行昂贵的分解步骤（结果会被缓存）
    ssa = perform_decomposition(ssa, signal_to_test)
except ValueError as e:
    st.error(f"SSA参数错误: {e}")
    st.stop()

# --- 侧边栏：成分选择 (最核心的功能) ---
st.sidebar.subheader("4. 选择重构成分")
num_available_components = len(ssa.s)
component_options = list(range(num_available_components))

st.sidebar.info("按住 `Ctrl` (或 `Cmd`) 可多选。")
selected_components = st.sidebar.multiselect(
    "选择要保留的成分索引",
    options=component_options,
    default=list(range(min(6, num_available_components)))  # 默认选前6个
)

# --- 执行重构并显示结果 ---
if not selected_components:
    st.warning("您没有选择任何成分。重构信号将为零。请在左侧选择要保留的成分。")

# 执行轻量级的重构
filtered_signal = ssa.reconstruct(selected_components)

# 显示选择信息
st.markdown(f"**当前配置:**")
st.markdown(
    f"- **信号源:** `{uploaded_file.name}`, 通道 `{channel_idx}`, 采样点 `{start_sample}` 到 `{end_sample}` ({len(signal_to_test)}个点)")
st.markdown(f"- **SSA窗口长度 (L):** `{window_length}` (共分解出 `{num_available_components}` 个成分)")
st.markdown(f"- **选择保留的成分索引:** `{sorted(selected_components)}`")

# 绘图
plot_results(
    original=signal_to_test,
    filtered=filtered_signal,
    ssa=ssa,
    selected_indices=selected_components
)

# --- 提供下载功能 ---
st.header("📥 下载结果")
if st.button("准备下载文件"):
    results_df = pd.DataFrame({
        'original_signal': signal_to_test,
        'filtered_signal': filtered_signal,
        'removed_noise': signal_to_test - filtered_signal
    })

    csv = results_df.to_csv(index=False).encode('utf-8')

    st.download_button(
        label="点击下载CSV文件",
        data=csv,
        file_name=f"ssa_denoised_channel_{channel_idx}_{start_sample}-{end_sample}.csv",
        mime="text/csv",
    )