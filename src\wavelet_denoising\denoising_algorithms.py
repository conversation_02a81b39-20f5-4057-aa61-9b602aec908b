"""
降噪算法库
Author: Assistant 
Date: 2025-07-17
Description: 实现多种小波和组合降噪算法
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Optional, Dict, Any
import pywt
from scipy import signal
from scipy.linalg import svd
import logging

logger = logging.getLogger(__name__)

def denoise_baseline_wavelet(signal: np.ndarray, wavelet_type: str = 'sym8', 
                           level: int = 6, threshold_method: str = 'soft',
                           threshold_mode: str = 'sure', threshold_scale: float = None,
                           visualize: bool = False) -> np.ndarray:
    """
    标准小波阈值降噪 - 基准方法
    
    Args:
        signal: 输入信号
        wavelet_type: 小波类型 ('sym8', 'db8', 'coif5', 'bior4.4')
        level: 分解层数
        threshold_method: 阈值方法 ('soft', 'hard')
        threshold_mode: 阈值选择模式 ('sure', 'bayes', 'minimax')
        visualize: 是否显示过程可视化
        
    Returns:
        denoised_signal: 降噪后的信号
    """
    try:
        # 小波分解
        coeffs = pywt.wavedec(signal, wavelet_type, level=level)
        
        # 估计噪声方差 (使用最高频细节系数)
        sigma = np.median(np.abs(coeffs[-1])) / 0.6745
        
        # 计算阈值 - 针对低信噪比数据改进
        N = len(signal)
        
        if threshold_scale is not None:
            # 用户指定阈值缩放因子
            signal_max = np.max(np.abs(signal))
            threshold = signal_max * threshold_scale
        elif threshold_mode == 'signal_adaptive':
            # 基于信号幅值的自适应阈值（适用于低信噪比）
            signal_max = np.max(np.abs(signal))
            signal_std = np.std(signal)
            # 使用信号幅值的1/3作为阈值，同时考虑信号变化
            threshold = min(signal_max * 0.35, signal_std * 2.5)
        elif threshold_mode == 'energy_adaptive':
            # 基于能量分布的自适应阈值
            signal_energy = np.var(signal)
            threshold = np.sqrt(signal_energy) * 0.6
        elif threshold_mode == 'percentile_adaptive':
            # 基于百分位数的自适应阈值
            threshold = np.percentile(np.abs(signal), 70)
        elif threshold_mode == 'sure':
            threshold = sigma * np.sqrt(2 * np.log(N))
        elif threshold_mode == 'bayes':
            threshold = sigma * np.sqrt(2 * np.log(N * np.log(N)))
        elif threshold_mode == 'minimax':
            threshold = sigma * (0.3936 + 0.1829 * np.log(N))
        else:
            # 默认使用信号自适应阈值
            signal_max = np.max(np.abs(signal))
            threshold = signal_max * 0.35
        
        # 应用阈值
        coeffs_thresh = list(coeffs)
        coeffs_thresh[1:] = [pywt.threshold(detail, threshold, threshold_method) 
                            for detail in coeffs[1:]]
        
        # 重构信号
        denoised_signal = pywt.waverec(coeffs_thresh, wavelet_type)
        
        # 确保长度一致
        if len(denoised_signal) != len(signal):
            denoised_signal = denoised_signal[:len(signal)]
        
        if visualize:
            _visualize_wavelet_decomposition(signal, denoised_signal, coeffs, coeffs_thresh, 
                                           wavelet_type, threshold)
        
        logger.info(f"小波基准降噪完成: {wavelet_type}, 层数={level}, 阈值={threshold:.4f}")
        return denoised_signal
        
    except Exception as e:
        logger.error(f"小波基准降噪失败: {e}")
        return signal

def denoise_svd(multichannel_signal: np.ndarray, k: int = None, 
                visualize: bool = False) -> np.ndarray:
    """
    SVD多通道降噪 - 策略一
    
    Args:
        multichannel_signal: 多通道信号 (samples, channels)
        k: 保留的奇异值数量，None时自动选择
        visualize: 是否显示过程可视化
        
    Returns:
        denoised_signals: 降噪后的多通道信号
    """
    try:
        # SVD分解
        U, s, Vt = svd(multichannel_signal, full_matrices=False)
        
        # 自动选择奇异值数量
        if k is None:
            # 使用能量保留比例确定k
            energy_cumsum = np.cumsum(s**2) / np.sum(s**2)
            k = np.argmax(energy_cumsum >= 0.99) + 1
            k = max(k, min(5, len(s)))  # 至少保留5个，但不超过总数
        
        k = min(k, len(s))  # 确保k不超过奇异值总数
        
        # 重构信号
        s_truncated = s.copy()
        s_truncated[k:] = 0
        denoised_signals = U @ np.diag(s_truncated) @ Vt
        
        if visualize:
            _visualize_svd_decomposition(multichannel_signal, denoised_signals, s, k)
        
        logger.info(f"SVD降噪完成: 保留前{k}个奇异值")
        return denoised_signals
        
    except Exception as e:
        logger.error(f"SVD降噪失败: {e}")
        return multichannel_signal

def denoise_bivariate_shrinkage(signal: np.ndarray, wavelet_type: str = 'db8',
                               level: int = 6, visualize: bool = False) -> np.ndarray:
    """
    双变量收缩降噪 - 策略二
    
    Args:
        signal: 输入信号
        wavelet_type: 小波类型
        level: 分解层数
        visualize: 是否显示过程可视化
        
    Returns:
        denoised_signal: 降噪后的信号
    """
    try:
        # 小波分解
        coeffs = pywt.wavedec(signal, wavelet_type, level=level)
        
        # 估计噪声方差
        sigma = np.median(np.abs(coeffs[-1])) / 0.6745
        
        # 双变量收缩
        coeffs_shrink = [coeffs[0]]  # 保留近似系数
        
        for i in range(1, len(coeffs)):
            detail = coeffs[i]
            
            # 计算局部方差
            local_var = np.var(detail)
            
            # 双变量收缩因子
            if local_var > sigma**2:
                shrink_factor = 1 - sigma**2 / local_var
                shrunk_detail = detail * shrink_factor
            else:
                shrunk_detail = np.zeros_like(detail)
            
            coeffs_shrink.append(shrunk_detail)
        
        # 重构信号
        denoised_signal = pywt.waverec(coeffs_shrink, wavelet_type)
        
        # 确保长度一致
        if len(denoised_signal) != len(signal):
            denoised_signal = denoised_signal[:len(signal)]
        
        if visualize:
            _visualize_bivariate_shrinkage(signal, denoised_signal, coeffs, coeffs_shrink, 
                                         wavelet_type, sigma)
        
        logger.info(f"双变量收缩降噪完成: {wavelet_type}, 噪声方差={sigma:.4f}")
        return denoised_signal
        
    except Exception as e:
        logger.error(f"双变量收缩降噪失败: {e}")
        return signal

def denoise_dtcwt(signal: np.ndarray, level: int = 6, threshold_scale: float = None,
                  visualize: bool = False) -> np.ndarray:
    """
    双树复小波变换(DTCWT)降噪 - 策略二
    使用改进的复小波变换实现
    
    Args:
        signal: 输入信号
        level: 分解层数
        threshold_scale: 阈值缩放因子
        visualize: 是否显示过程可视化
        
    Returns:
        denoised_signal: 降噪后的信号
    """
    try:
        # 方法1：使用复db小波族进行离散小波变换
        # 创建复值小波系数
        wavelet_real = 'db8'
        wavelet_imag = 'db8'
        
        # 实部和虚部分别进行小波变换
        coeffs_real = pywt.wavedec(signal, wavelet_real, level=level)
        # 对信号进行希尔伯特变换得到虚部
        from scipy.signal import hilbert
        signal_analytic = hilbert(signal)
        signal_imag = np.imag(signal_analytic)
        coeffs_imag = pywt.wavedec(signal_imag, wavelet_imag, level=level)
        
        # 构造复小波系数
        coeffs_complex = []
        for i in range(len(coeffs_real)):
            coeffs_complex.append(coeffs_real[i] + 1j * coeffs_imag[i])
        
        # 计算阈值
        if threshold_scale is not None:
            signal_max = np.max(np.abs(signal))
            threshold = signal_max * threshold_scale
        else:
            # 使用复系数的中值估计噪声
            detail_complex = coeffs_complex[-1]
            noise_var = np.median(np.abs(detail_complex)) / 0.6745
            signal_max = np.max(np.abs(signal))
            threshold = min(signal_max * 0.3, noise_var * np.sqrt(2 * np.log(len(signal))))
        
        # 复值软阈值
        coeffs_thresh_complex = [coeffs_complex[0]]  # 保留近似系数
        
        for i in range(1, len(coeffs_complex)):
            detail_complex = coeffs_complex[i]
            magnitude = np.abs(detail_complex)
            phase = np.angle(detail_complex)
            
            # 复值软阈值
            magnitude_thresh = np.maximum(magnitude - threshold, 0) * \
                             np.sign(magnitude)
            
            # 重构复系数
            detail_thresh = magnitude_thresh * np.exp(1j * phase)
            coeffs_thresh_complex.append(detail_thresh)
        
        # 分别重构实部和虚部
        coeffs_real_thresh = [np.real(c) for c in coeffs_thresh_complex]
        coeffs_imag_thresh = [np.imag(c) for c in coeffs_thresh_complex]
        
        denoised_real = pywt.waverec(coeffs_real_thresh, wavelet_real)
        denoised_imag = pywt.waverec(coeffs_imag_thresh, wavelet_imag)
        
        # 使用实部作为最终结果，虚部信息用于增强
        denoised_signal = denoised_real
        
        # 长度调整
        if len(denoised_signal) != len(signal):
            denoised_signal = denoised_signal[:len(signal)]
        
        if visualize:
            _visualize_dtcwt_improved(signal, denoised_signal, coeffs_complex, 
                                    coeffs_thresh_complex, threshold)
        
        logger.info(f"DTCWT降噪完成: 层数={level}, 阈值={threshold:.4f}")
        return denoised_signal
        
    except Exception as e:
        logger.error(f"DTCWT降噪失败: {e}")
        # 回退到标准小波降噪
        return denoise_baseline_wavelet(signal, threshold_mode='signal_adaptive', visualize=False)

def denoise_residual_vmd(signal: np.ndarray, wavelet_type: str = 'db8', 
                        vmd_k: int = 5, vmd_alpha: int = 2000,
                        visualize: bool = False) -> np.ndarray:
    """
    小波+残差VMD降噪方案
    
    Args:
        signal: 输入信号
        wavelet_type: 小波类型
        vmd_k: VMD模态数
        vmd_alpha: VMD平衡参数
        visualize: 是否显示过程可视化
        
    Returns:
        denoised_signal: 降噪后的信号
    """
    try:
        # 第一步：小波降噪
        wavelet_denoised = denoise_baseline_wavelet(signal, wavelet_type, 
                                                   visualize=False)
        
        # 计算残差
        residual = signal - wavelet_denoised
        
        # 第二步：对残差进行VMD分解
        try:
            # 导入VMD模块
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../scripts'))
            from vmdpy import VMD
            
            # VMD分解残差
            u, u_hat, omega = VMD(residual, vmd_alpha, 0, vmd_k, 0, 1, 1e-7)
            
            # 选择有用的VMD模态（基于频率和能量）
            useful_modes = []
            for i, mode in enumerate(u):
                # 简单的模态选择策略：保留中低频模态
                mode_freq = omega[-1, i]  # 最终频率
                mode_energy = np.var(mode)
                
                if 1 < mode_freq < 100 and mode_energy > 0.01 * np.var(residual):
                    useful_modes.append(mode)
            
            # 重构有用信号
            if useful_modes:
                recovered_signal = np.sum(useful_modes, axis=0)
            else:
                recovered_signal = np.zeros_like(residual)
            
            # 最终信号
            final_denoised = wavelet_denoised + recovered_signal
            
        except ImportError:
            logger.warning("VMD模块不可用，仅使用小波降噪")
            final_denoised = wavelet_denoised
            residual = signal - wavelet_denoised
            recovered_signal = np.zeros_like(residual)
        
        if visualize:
            _visualize_residual_vmd(signal, wavelet_denoised, residual, 
                                  recovered_signal, final_denoised)
        
        logger.info(f"小波+残差VMD降噪完成")
        return final_denoised
        
    except Exception as e:
        logger.error(f"小波+残差VMD降噪失败: {e}")
        return signal

def denoise_iterative_wavelet(signal: np.ndarray, iterations: int = 3,
                             wavelet_type: str = 'db8', threshold_scale: float = None,
                             visualize: bool = False) -> np.ndarray:
    """
    迭代小波降噪方案
    
    Args:
        signal: 输入信号
        iterations: 迭代次数
        wavelet_type: 小波类型
        visualize: 是否显示过程可视化
        
    Returns:
        denoised_signal: 降噪后的信号
    """
    try:
        current_signal = signal.copy()
        iteration_results = [signal]
        
        for i in range(iterations):
            # 小波降噪，逐步降低阈值
            if threshold_scale is not None:
                current_threshold_scale = threshold_scale * (0.8 ** i)  # 逐步降低阈值
                denoised = denoise_baseline_wavelet(current_signal, wavelet_type, 
                                                  threshold_scale=current_threshold_scale,
                                                  visualize=False)
            else:
                # 使用自适应阈值，逐步精化
                threshold_mode = 'signal_adaptive' if i == 0 else 'energy_adaptive'
                denoised = denoise_baseline_wavelet(current_signal, wavelet_type, 
                                                  threshold_mode=threshold_mode,
                                                  visualize=False)
            
            # 计算残差
            residual = current_signal - denoised
            
            # 残差分析：保留高信噪比的残差分量
            # 使用自适应阈值，逐步提高要求
            residual_std = np.std(residual)
            residual_max = np.max(np.abs(residual))
            
            # 逐步提高残差处理的阈值
            residual_threshold_factor = 0.4 + 0.1 * i  # 逐步提高
            residual_threshold = residual_max * residual_threshold_factor
            
            # 对残差进行第二次小波处理
            residual_denoised = denoise_baseline_wavelet(residual, wavelet_type, 
                                                       threshold_scale=residual_threshold_factor,
                                                       visualize=False)
            
            # 智能残差加回策略
            residual_energy = np.var(residual_denoised)
            signal_energy = np.var(denoised)
            
            if residual_energy > 0.03 * signal_energy:  # 残差能量阈值逐步降低
                # 自适应权重，逐步减小
                alpha = min(0.25 - 0.05 * i, residual_energy / signal_energy)
                alpha = max(0.05, alpha)  # 保证最小权重
                current_signal = denoised + alpha * residual_denoised
            else:
                current_signal = denoised
            
            iteration_results.append(current_signal.copy())
            
            logger.info(f"迭代 {i+1}/{iterations} 完成，残差能量比: {residual_energy/signal_energy:.4f}, 权重: {alpha if residual_energy > 0.03 * signal_energy else 0:.3f}")
        
        if visualize:
            _visualize_iterative_wavelet(signal, iteration_results, wavelet_type)
        
        logger.info(f"迭代小波降噪完成: {iterations}次迭代")
        return current_signal
        
    except Exception as e:
        logger.error(f"迭代小波降噪失败: {e}")
        return signal

# 可视化辅助函数
def _visualize_wavelet_decomposition(original, denoised, coeffs, coeffs_thresh, 
                                   wavelet_type, threshold):
    """可视化小波分解过程"""
    fig, axes = plt.subplots(3, 2, figsize=(15, 10))
    
    # 原始和降噪信号
    time_axis = np.arange(len(original))
    axes[0, 0].plot(time_axis, original, 'b-', alpha=0.7, label='Original')
    axes[0, 0].plot(time_axis, denoised, 'r-', label='Denoised')
    axes[0, 0].set_title(f'Wavelet Denoising ({wavelet_type})')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 残差
    residual = original - denoised
    axes[0, 1].plot(time_axis, residual, 'g-')
    axes[0, 1].set_title('Residual (Removed Noise)')
    axes[0, 1].grid(True)
    
    # 小波系数（原始）
    level_lengths = [len(c) for c in coeffs]
    all_coeffs = np.concatenate(coeffs)
    axes[1, 0].plot(all_coeffs)
    axes[1, 0].axhline(y=threshold, color='r', linestyle='--', label=f'Threshold={threshold:.3f}')
    axes[1, 0].axhline(y=-threshold, color='r', linestyle='--')
    axes[1, 0].set_title('Original Wavelet Coefficients')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # 小波系数（阈值后）
    all_coeffs_thresh = np.concatenate(coeffs_thresh)
    axes[1, 1].plot(all_coeffs_thresh)
    axes[1, 1].set_title('Thresholded Wavelet Coefficients')
    axes[1, 1].grid(True)
    
    # 频谱对比
    from scipy.fft import fft, fftfreq
    N = len(original)
    freqs = fftfreq(N, 1.0)[:N//2]
    
    fft_orig = np.abs(fft(original))[:N//2]
    fft_denoised = np.abs(fft(denoised))[:N//2]
    
    axes[2, 0].semilogy(freqs, fft_orig, label='Original')
    axes[2, 0].semilogy(freqs, fft_denoised, label='Denoised')
    axes[2, 0].set_title('Frequency Spectrum')
    axes[2, 0].set_xlabel('Frequency')
    axes[2, 0].legend()
    axes[2, 0].grid(True)
    
    # 系数分布
    axes[2, 1].hist(all_coeffs, bins=50, alpha=0.7, label='Original', density=True)
    axes[2, 1].hist(all_coeffs_thresh, bins=50, alpha=0.7, label='Thresholded', density=True)
    axes[2, 1].set_title('Coefficient Distribution')
    axes[2, 1].legend()
    axes[2, 1].grid(True)
    
    plt.tight_layout()
    plt.show()

def _visualize_svd_decomposition(original, denoised, singular_values, k):
    """可视化SVD分解过程"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 信号对比
    if original.ndim == 2:
        # 显示第一个通道
        axes[0, 0].plot(original[:, 0], 'b-', alpha=0.7, label='Original Ch1')
        axes[0, 0].plot(denoised[:, 0], 'r-', label='Denoised Ch1')
    else:
        axes[0, 0].plot(original, 'b-', alpha=0.7, label='Original')
        axes[0, 0].plot(denoised, 'r-', label='Denoised')
    axes[0, 0].set_title('Signal Comparison')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 奇异值
    axes[0, 1].semilogy(singular_values, 'bo-')
    axes[0, 1].axvline(x=k, color='r', linestyle='--', label=f'Cutoff k={k}')
    axes[0, 1].set_title('Singular Values')
    axes[0, 1].set_xlabel('Index')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # 能量保留
    energy_cumsum = np.cumsum(singular_values**2) / np.sum(singular_values**2)
    axes[1, 0].plot(energy_cumsum, 'go-')
    axes[1, 0].axvline(x=k, color='r', linestyle='--')
    axes[1, 0].axhline(y=energy_cumsum[k-1] if k > 0 else 0, color='r', linestyle='--')
    axes[1, 0].set_title(f'Cumulative Energy (k={k}: {energy_cumsum[k-1 if k > 0 else 0]:.3f})')
    axes[1, 0].set_xlabel('Components')
    axes[1, 0].grid(True)
    
    # 多通道降噪效果
    if original.ndim == 2 and original.shape[1] > 1:
        for i in range(min(3, original.shape[1])):
            axes[1, 1].plot(original[:200, i] - denoised[:200, i], 
                          label=f'Residual Ch{i+1}')
        axes[1, 1].set_title('Residuals (First 200 samples)')
        axes[1, 1].legend()
    else:
        axes[1, 1].plot(original[:200] - denoised[:200])
        axes[1, 1].set_title('Residual (First 200 samples)')
    axes[1, 1].grid(True)
    
    plt.tight_layout()
    plt.show()

def _visualize_bivariate_shrinkage(original, denoised, coeffs, coeffs_shrink, 
                                 wavelet_type, sigma):
    """可视化双变量收缩过程"""
    fig, axes = plt.subplots(2, 3, figsize=(15, 8))
    
    # 信号对比
    time_axis = np.arange(len(original))
    axes[0, 0].plot(time_axis, original, 'b-', alpha=0.7, label='Original')
    axes[0, 0].plot(time_axis, denoised, 'r-', label='Denoised')
    axes[0, 0].set_title(f'Bivariate Shrinkage ({wavelet_type})')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 残差
    residual = original - denoised
    axes[0, 1].plot(time_axis, residual, 'g-')
    axes[0, 1].set_title(f'Residual (σ={sigma:.3f})')
    axes[0, 1].grid(True)
    
    # 收缩因子
    shrink_factors = []
    for i in range(1, len(coeffs)):
        detail_var = np.var(coeffs[i])
        if detail_var > sigma**2:
            factor = 1 - sigma**2 / detail_var
        else:
            factor = 0
        shrink_factors.append(factor)
    
    axes[0, 2].bar(range(len(shrink_factors)), shrink_factors)
    axes[0, 2].set_title('Shrinkage Factors by Level')
    axes[0, 2].set_xlabel('Detail Level')
    axes[0, 2].grid(True)
    
    # 系数对比（选择几个层次）
    for i, level in enumerate([1, len(coeffs)//2, -1]):
        if level == -1:
            level = len(coeffs) - 1
        
        axes[1, i].plot(coeffs[level], 'b-', alpha=0.7, label='Original')
        axes[1, i].plot(coeffs_shrink[level], 'r-', label='Shrunk')
        axes[1, i].set_title(f'Level {level} Coefficients')
        axes[1, i].legend()
        axes[1, i].grid(True)
    
    plt.tight_layout()
    plt.show()

def _visualize_dtcwt_improved(original, denoised, coeffs_complex, coeffs_thresh_complex, threshold):
    """可视化DTCWT过程"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 信号对比
    time_axis = np.arange(len(original))
    axes[0, 0].plot(time_axis, original, 'b-', alpha=0.7, label='Original')
    axes[0, 0].plot(time_axis, denoised, 'r-', label='Denoised')
    axes[0, 0].set_title('DTCWT Denoising')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 残差分析
    residual = original - denoised
    axes[0, 1].plot(time_axis, residual, 'g-')
    axes[0, 1].set_title('Removed Noise (Residual)')
    axes[0, 1].grid(True)
    
    # 阈值设置显示
    axes[1, 0].text(0.1, 0.8, f'Threshold: {threshold:.4f}', transform=axes[1, 0].transAxes, fontsize=12)
    axes[1, 0].text(0.1, 0.6, f'Signal Max: {np.max(np.abs(original)):.4f}', transform=axes[1, 0].transAxes, fontsize=12)
    axes[1, 0].text(0.1, 0.4, f'Threshold Ratio: {threshold/np.max(np.abs(original)):.3f}', transform=axes[1, 0].transAxes, fontsize=12)
    axes[1, 0].set_title('Threshold Information')
    axes[1, 0].set_xlim(0, 1)
    axes[1, 0].set_ylim(0, 1)
    
    # 复系数幅度对比
    if len(coeffs_complex) > 1:
        magnitude_orig = np.abs(coeffs_complex[1])
        magnitude_thresh = np.abs(coeffs_thresh_complex[1])
        
        axes[1, 1].plot(magnitude_orig[:500], 'b-', alpha=0.7, label='Original Magnitude')
        axes[1, 1].plot(magnitude_thresh[:500], 'r-', label='Thresholded Magnitude')
        axes[1, 1].axhline(y=threshold, color='r', linestyle='--', alpha=0.5, label=f'Threshold={threshold:.3f}')
        axes[1, 1].set_title('Complex Coefficients Magnitude')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
    
    plt.tight_layout()
    plt.show()

def _visualize_residual_vmd(original, wavelet_denoised, residual, recovered, final):
    """可视化残差VMD过程"""
    fig, axes = plt.subplots(3, 2, figsize=(15, 10))
    
    time_axis = np.arange(len(original))
    
    # 第一步：小波降噪
    axes[0, 0].plot(time_axis, original, 'b-', alpha=0.7, label='Original')
    axes[0, 0].plot(time_axis, wavelet_denoised, 'r-', label='Wavelet Denoised')
    axes[0, 0].set_title('Step 1: Wavelet Denoising')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 残差分析
    axes[0, 1].plot(time_axis, residual, 'g-', label='Residual')
    axes[0, 1].plot(time_axis, recovered, 'm-', label='VMD Recovered')
    axes[0, 1].set_title('Step 2: Residual Analysis')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # 最终结果
    axes[1, 0].plot(time_axis, original, 'b-', alpha=0.7, label='Original')
    axes[1, 0].plot(time_axis, final, 'r-', label='Final Denoised')
    axes[1, 0].set_title('Final Result')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # 改善效果
    final_residual = original - final
    axes[1, 1].plot(time_axis, residual, 'g-', alpha=0.7, label='After Wavelet')
    axes[1, 1].plot(time_axis, final_residual, 'r-', label='After VMD+Wavelet')
    axes[1, 1].set_title('Noise Removal Comparison')
    axes[1, 1].legend()
    axes[1, 1].grid(True)
    
    # 频谱分析
    from scipy.fft import fft, fftfreq
    N = len(original)
    freqs = fftfreq(N, 1.0)[:N//2]
    
    fft_orig = np.abs(fft(original))[:N//2]
    fft_wavelet = np.abs(fft(wavelet_denoised))[:N//2]
    fft_final = np.abs(fft(final))[:N//2]
    
    axes[2, 0].semilogy(freqs, fft_orig, 'b-', alpha=0.7, label='Original')
    axes[2, 0].semilogy(freqs, fft_wavelet, 'g-', label='After Wavelet')
    axes[2, 0].semilogy(freqs, fft_final, 'r-', label='After VMD+Wavelet')
    axes[2, 0].set_title('Frequency Spectrum')
    axes[2, 0].set_xlabel('Frequency')
    axes[2, 0].legend()
    axes[2, 0].grid(True)
    
    # 能量分析
    energies = [
        np.var(original),
        np.var(wavelet_denoised),
        np.var(recovered),
        np.var(final),
        np.var(residual),
        np.var(final_residual)
    ]
    labels = ['Original', 'Wavelet', 'VMD Recovered', 'Final', 'Residual1', 'Final Residual']
    
    axes[2, 1].bar(labels, energies)
    axes[2, 1].set_title('Signal Energy Analysis')
    axes[2, 1].set_ylabel('Variance')
    axes[2, 1].tick_params(axis='x', rotation=45)
    axes[2, 1].grid(True)
    
    plt.tight_layout()
    plt.show()

def _visualize_iterative_wavelet(original, iteration_results, wavelet_type):
    """可视化迭代小波过程"""
    fig, axes = plt.subplots(2, 3, figsize=(15, 8))
    
    time_axis = np.arange(len(original))
    iterations = len(iteration_results) - 1
    
    # 迭代过程
    axes[0, 0].plot(time_axis, original, 'b-', alpha=0.7, linewidth=2, label='Original')
    
    colors = ['orange', 'green', 'red', 'purple', 'brown']
    for i in range(1, len(iteration_results)):
        color = colors[(i-1) % len(colors)]
        axes[0, 0].plot(time_axis, iteration_results[i], color=color, 
                       label=f'Iteration {i}')
    
    axes[0, 0].set_title(f'Iterative Wavelet Denoising ({wavelet_type})')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 收敛分析
    convergence_metric = []
    for i in range(1, len(iteration_results)):
        if i == 1:
            diff = np.sum((iteration_results[i] - original)**2)
        else:
            diff = np.sum((iteration_results[i] - iteration_results[i-1])**2)
        convergence_metric.append(diff)
    
    axes[0, 1].semilogy(range(1, len(iteration_results)), convergence_metric, 'bo-')
    axes[0, 1].set_title('Convergence Analysis')
    axes[0, 1].set_xlabel('Iteration')
    axes[0, 1].set_ylabel('Change (log scale)')
    axes[0, 1].grid(True)
    
    # 残差演化
    for i in range(1, min(4, len(iteration_results))):
        residual = original - iteration_results[i]
        axes[0, 2].plot(time_axis[:200], residual[:200], 
                       label=f'Iter {i}', alpha=0.7)
    axes[0, 2].set_title('Residual Evolution (First 200 samples)')
    axes[0, 2].legend()
    axes[0, 2].grid(True)
    
    # 频谱演化
    from scipy.fft import fft, fftfreq
    N = len(original)
    freqs = fftfreq(N, 1.0)[:N//2]
    
    fft_orig = np.abs(fft(original))[:N//2]
    axes[1, 0].semilogy(freqs, fft_orig, 'b-', alpha=0.7, linewidth=2, label='Original')
    
    for i in range(1, min(4, len(iteration_results))):
        fft_iter = np.abs(fft(iteration_results[i]))[:N//2]
        color = colors[(i-1) % len(colors)]
        axes[1, 0].semilogy(freqs, fft_iter, color=color, label=f'Iter {i}')
    
    axes[1, 0].set_title('Frequency Spectrum Evolution')
    axes[1, 0].set_xlabel('Frequency')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # SNR改善
    snr_improvement = []
    for i in range(1, len(iteration_results)):
        signal_power = np.var(iteration_results[i])
        noise_power = np.var(original - iteration_results[i])
        if noise_power > 0:
            snr = 10 * np.log10(signal_power / noise_power)
        else:
            snr = float('inf')
        snr_improvement.append(snr)
    
    axes[1, 1].plot(range(1, len(iteration_results)), snr_improvement, 'ro-')
    axes[1, 1].set_title('SNR Improvement by Iteration')
    axes[1, 1].set_xlabel('Iteration')
    axes[1, 1].set_ylabel('SNR (dB)')
    axes[1, 1].grid(True)
    
    # 能量保留
    energy_retention = []
    orig_energy = np.var(original)
    for i in range(1, len(iteration_results)):
        retained_energy = np.var(iteration_results[i])
        retention = retained_energy / orig_energy
        energy_retention.append(retention)
    
    axes[1, 2].plot(range(1, len(iteration_results)), energy_retention, 'go-')
    axes[1, 2].set_title('Energy Retention by Iteration')
    axes[1, 2].set_xlabel('Iteration')
    axes[1, 2].set_ylabel('Energy Retention Ratio')
    axes[1, 2].grid(True)
    
    plt.tight_layout()
    plt.show()

# 算法性能对比工具
def compare_all_methods(signal: np.ndarray, visualize: bool = True) -> Dict[str, np.ndarray]:
    """
    对比所有降噪方法的性能
    
    Args:
        signal: 输入信号
        visualize: 是否显示对比可视化
        
    Returns:
        results: 各方法的降噪结果字典
    """
    results = {}
    
    # 基准小波
    results['Baseline Wavelet'] = denoise_baseline_wavelet(signal, visualize=False)
    
    # 双变量收缩
    results['Bivariate Shrinkage'] = denoise_bivariate_shrinkage(signal, visualize=False)
    
    # DTCWT
    results['DTCWT'] = denoise_dtcwt(signal, visualize=False)
    
    # 残差VMD
    results['Residual VMD'] = denoise_residual_vmd(signal, visualize=False)
    
    # 迭代小波
    results['Iterative Wavelet'] = denoise_iterative_wavelet(signal, visualize=False)
    
    if visualize:
        _visualize_method_comparison(signal, results)
    
    return results

def _visualize_method_comparison(original: np.ndarray, results: Dict[str, np.ndarray]):
    """可视化所有方法的对比"""
    n_methods = len(results)
    fig, axes = plt.subplots(3, 2, figsize=(15, 12))
    
    time_axis = np.arange(len(original))
    
    # 信号对比
    axes[0, 0].plot(time_axis, original, 'k-', linewidth=2, alpha=0.7, label='Original')
    
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
    for i, (method, denoised) in enumerate(results.items()):
        color = colors[i % len(colors)]
        axes[0, 0].plot(time_axis, denoised, color=color, alpha=0.8, label=method)
    
    axes[0, 0].set_title('All Methods Comparison')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 残差对比
    for i, (method, denoised) in enumerate(results.items()):
        color = colors[i % len(colors)]
        residual = original - denoised
        axes[0, 1].plot(time_axis[:500], residual[:500], color=color, 
                       alpha=0.7, label=method)
    axes[0, 1].set_title('Residuals Comparison (First 500 samples)')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # 频谱对比
    from scipy.fft import fft, fftfreq
    N = len(original)
    freqs = fftfreq(N, 1.0)[:N//2]
    
    fft_orig = np.abs(fft(original))[:N//2]
    axes[1, 0].semilogy(freqs, fft_orig, 'k-', linewidth=2, alpha=0.7, label='Original')
    
    for i, (method, denoised) in enumerate(results.items()):
        color = colors[i % len(colors)]
        fft_denoised = np.abs(fft(denoised))[:N//2]
        axes[1, 0].semilogy(freqs, fft_denoised, color=color, alpha=0.8, label=method)
    
    axes[1, 0].set_title('Frequency Spectrum Comparison')
    axes[1, 0].set_xlabel('Frequency')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # SNR对比
    snr_values = []
    method_names = []
    for method, denoised in results.items():
        signal_power = np.var(denoised)
        noise_power = np.var(original - denoised)
        if noise_power > 0:
            snr = 10 * np.log10(signal_power / noise_power)
        else:
            snr = float('inf')
        snr_values.append(snr)
        method_names.append(method)
    
    bars = axes[1, 1].bar(method_names, snr_values, color=colors[:len(results)])
    axes[1, 1].set_title('SNR Comparison')
    axes[1, 1].set_ylabel('SNR (dB)')
    axes[1, 1].tick_params(axis='x', rotation=45)
    axes[1, 1].grid(True)
    
    # 在柱状图上显示数值
    for bar, snr in zip(bars, snr_values):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{snr:.1f}', ha='center', va='bottom')
    
    # 相关性对比（与原信号的相关性）
    corr_values = []
    for method, denoised in results.items():
        correlation = np.corrcoef(original, denoised)[0, 1]
        corr_values.append(correlation)
    
    bars2 = axes[2, 0].bar(method_names, corr_values, color=colors[:len(results)])
    axes[2, 0].set_title('Correlation with Original Signal')
    axes[2, 0].set_ylabel('Correlation Coefficient')
    axes[2, 0].tick_params(axis='x', rotation=45)
    axes[2, 0].grid(True)
    axes[2, 0].set_ylim([0, 1])
    
    # 在柱状图上显示数值
    for bar, corr in zip(bars2, corr_values):
        height = bar.get_height()
        axes[2, 0].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{corr:.3f}', ha='center', va='bottom')
    
    # 能量保留
    orig_energy = np.var(original)
    energy_retention = []
    for method, denoised in results.items():
        retained_energy = np.var(denoised)
        retention = retained_energy / orig_energy
        energy_retention.append(retention)
    
    bars3 = axes[2, 1].bar(method_names, energy_retention, color=colors[:len(results)])
    axes[2, 1].set_title('Energy Retention')
    axes[2, 1].set_ylabel('Energy Retention Ratio')
    axes[2, 1].tick_params(axis='x', rotation=45)
    axes[2, 1].grid(True)
    
    # 在柱状图上显示数值
    for bar, retention in zip(bars3, energy_retention):
        height = bar.get_height()
        axes[2, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{retention:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
    
    # 打印数值结果
    print("\n" + "="*60)
    print("降噪方法性能对比")
    print("="*60)
    print(f"{'Method':<20} {'SNR (dB)':<10} {'Correlation':<12} {'Energy Retention':<15}")
    print("-"*60)
    for i, (method, _) in enumerate(results.items()):
        print(f"{method:<20} {snr_values[i]:<10.2f} {corr_values[i]:<12.3f} {energy_retention[i]:<15.3f}")
    print("="*60)

if __name__ == "__main__":
    # 测试示例
    np.random.seed(42)
    
    # 生成测试信号
    t = np.linspace(0, 1, 1000)
    clean_signal = np.sin(2*np.pi*10*t) + 0.5*np.sin(2*np.pi*25*t)
    noise = 0.3 * np.random.randn(len(clean_signal))
    noisy_signal = clean_signal + noise
    
    print("测试所有降噪方法...")
    results = compare_all_methods(noisy_signal, visualize=True)