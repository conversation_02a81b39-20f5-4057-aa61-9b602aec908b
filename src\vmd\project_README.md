# VMD 61通道数据处理工程

## 项目简介

本项目是一个基于VMD（变分模态分解）的61通道数据处理工程，专门用于处理多通道信号数据的降噪和分析。项目围绕 `vmd_analyzer_squid` 的核心功能扩展而成，提供了完整的配置化、并行化和可视化处理能力。

## 主要特性

- **统一配置管理**: 通过YAML配置文件管理所有参数
- **灵活的滤波策略**: 支持多种预设和自定义滤波策略
- **并行处理**: 支持多进程并行处理，提高处理效率
- **多格式输出**: 支持TXT、CSV、MAT等多种输出格式
- **丰富的可视化**: 自动生成信号对比图、模态分析图、相似性矩阵等
- **详细的处理报告**: 生成包含相似性统计的HTML报告
- **61通道数据结构支持**: 专门针对61通道数据结构进行优化
- **🆕 批量处理功能**: 一次性处理整个文件夹的所有txt文件
- **🆕 综合分析报告**: 生成跨文件的统计分析和批量对比
- **🆕 智能文件过滤**: 支持文件名过滤和数量限制

## 项目结构

```
Denoise_61chan/
├── config.yaml              # 主配置文件
├── main.py                   # 主执行脚本
├── config_manager.py         # 配置管理系统
├── vmd_engine.py            # VMD处理引擎
├── visualization.py         # 可视化和报告生成
├── requirements.txt         # 依赖包列表
├── scripts/                 # 脚本目录
│   ├── vmd_analyzer_squid.py # 原始Streamlit应用
│   ├── vmdpy.py             # VMD核心库
│   └── ...                  # 其他脚本
├── output/                  # 输出目录
├── logs/                    # 日志目录
└── cache/                   # 缓存目录
```

## 61通道数据结构

项目专门处理61通道数据结构：
- 第1通道：idx（索引）
- 第2-37通道：实际数据（9个通道一组，共4组）
- 第38-41通道：标准源信号（每组对应一个参考信号）

**分组结构：**
- 第1组（通道2-10）：参考信号通道38
- 第2组（通道11-19）：参考信号通道39
- 第3组（通道20-28）：参考信号通道40
- 第4组（通道29-37）：参考信号通道41

## 滤波策略

### 默认策略
- 最低频中心分量：0.6-30Hz 带通滤波
- 其他模态：20Hz 低通滤波
- 高频噪声模态：直接丢弃

### 自定义策略
用户可以在配置文件中定义自己的滤波策略，为每个模态指定不同的滤波参数。

## 快速开始

### 1. 环境准备

```bash
pip install -r requirements.txt
```

### 2. 配置文件设置

编辑 `config.yaml` 文件，设置数据路径和处理参数：

```yaml
data:
  input_path: "/path/to/your/data"
  processing:
    channels: [1, 2, 3, 4, 5, 6, 7, 8, 9]  # 处理通道
    sample_length: 10000                    # 处理长度
    sampling_rate: 1000                     # 采样率
```

### 3. 基本使用

#### 单文件处理
```bash
# 使用默认配置处理单个文件
python main.py --input /path/to/file.txt

# 指定配置文件
python main.py --config my_config.yaml --input /path/to/file.txt

# 处理特定通道
python main.py --input /path/to/file.txt --channels 1 2 3

# 测试模式（处理前3秒数据）
python main.py --input /path/to/file.txt --test
```

#### 🆕 批量处理
```bash
# 批量处理文件夹中的所有txt文件
python main.py --input /path/to/txt/folder/ --batch

# 使用文件过滤器
python main.py --input /path/to/txt/folder/ --batch --filter "PLAG"

# 限制处理文件数量
python main.py --input /path/to/txt/folder/ --batch --max-files 10

# 批量测试模式
python main.py --input /path/to/txt/folder/ --batch --test --max-files 3
```

### 4. 高级用法

```bash
# 使用特定滤波策略
python main.py --policy custom

# 强制并行处理
python main.py --parallel

# 不生成可视化图片
python main.py --no-vis

# 不生成报告
python main.py --no-report

# 传统逐个处理模式（不生成批量报告）
python main.py --input /path/to/folder/ --legacy
```

## 输出结果

### 单文件处理结果
- `*_processed.txt`: 与输入格式相同的处理结果
- `*_processed.csv`: CSV格式的处理结果
- `*_processed.mat`: MATLAB格式的处理结果（可选）
- `*_visualizations/`: 单文件可视化图片目录
  - `channel_X_comparison.png`: 单通道信号对比图
  - `channel_X_modes.png`: 单通道模态分析图
  - `channels_overview.png`: 多通道概览图
  - `similarity_matrix.png`: 相似性矩阵图
- `*_report.html`: 单文件处理报告

### 🆕 批量处理结果
- `batch_processing_report.html`: **综合批量处理报告**
- `batch_statistics.json`: 批量统计数据（JSON格式）
- `batch_analysis/`: **批量分析可视化目录**
  - `batch_overview.png`: 批量处理概览图
  - `files_comparison.png`: 文件间对比分析图
  - `batch_statistics.png`: 批量统计分析图
  - `quality_analysis.png`: 质量分析图
- 各单文件的处理结果（如上述单文件格式）

## 配置文件说明

### 主要配置项

```yaml
# 数据配置
data:
  input_path: "数据目录路径"
  output_path: "输出目录路径"
  processing:
    channels: [1, 2, 3, 4, 5, 6, 7, 8, 9]
    sample_length: 10000
    sampling_rate: 1000

# VMD参数
vmd:
  K: 5              # 模态数量
  alpha: 2000.0     # 带宽限制
  tau: 0.0          # 噪声容忍度
  DC: true          # 第一个模态为DC分量
  init: 1           # 初始化方式
  tol: 1e-7         # 收敛容忍度

# 滤波策略
current_policy: "default"  # 当前使用的策略

# 并行处理
parallel:
  enabled: true
  num_workers: 0    # 0为自动检测

# 输出配置
output:
  formats: ["txt", "csv"]
  visualization:
    enabled: true
    comparison_plots: true
    mode_plots: true
  report:
    enabled: true
    similarity_analysis: true
```

## 性能优化

### 并行处理
- 支持多进程并行处理多个通道
- 自动检测CPU核心数优化并行度
- 可通过配置文件控制并行参数

### 缓存机制
- VMD计算结果自动缓存，避免重复计算
- 支持配置缓存目录和大小限制

### 内存管理
- 合理的内存使用限制
- 大文件分段处理支持

## 常见问题

### Q: 如何处理不同采样率的数据？
A: 在配置文件中设置正确的 `sampling_rate` 参数。

### Q: 如何添加新的滤波策略？
A: 在配置文件的 `filter_policies` 部分添加新的策略定义。

### Q: 处理速度慢怎么办？
A: 启用并行处理，调整 `num_workers` 参数，或减少处理的通道数量。

### Q: 如何只处理特定时间段的数据？
A: 修改配置文件中的 `sample_segment` 参数。

## 开发说明

### 扩展新功能
1. 新的滤波器：在 `vmd_engine.py` 中添加滤波器实现
2. 新的可视化：在 `visualization.py` 中添加绘图函数
3. 新的输出格式：在 `vmd_engine.py` 中添加保存函数

### 调试模式
使用 `--test` 参数启用测试模式，只处理前3秒数据，适合调试和参数调优。

## 依赖包

主要依赖：
- numpy
- scipy
- matplotlib
- pandas
- PyYAML
- seaborn
- streamlit (用于原始可视化界面)

## 许可证

[添加许可证信息]

## 贡献

欢迎提交Issue和Pull Request。

## 更新日志

### v1.0 (2025-07-15)
- 初始版本发布
- 完整的VMD处理pipeline
- 配置化管理系统
- 并行处理支持
- 可视化和报告生成