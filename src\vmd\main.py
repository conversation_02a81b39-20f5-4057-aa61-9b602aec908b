#!/usr/bin/env python3
"""
VMD 61通道数据处理工程主执行脚本
Author: Assistant
Date: 2025-07-15
Description: 统一的VMD处理工程主入口
"""

import argparse
import logging
import time
from pathlib import Path
import sys
import os
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager
from vmd_engine import VMDEngine
from visualization import VisualizationManager, ReportGenerator
from batch_processor import BatchProcessor

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("../../logs/main.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def process_single_file(file_path: str, config_manager: ConfigManager, 
                       vmd_engine: VMDEngine, vis_manager: VisualizationManager,
                       report_generator: ReportGenerator):
    """处理单个文件"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始处理文件: {file_path}")
        start_time = time.time()
        
        # 处理文件
        results = vmd_engine.process_file(file_path)
        
        # 保存结果
        vmd_engine.save_results(file_path, results)
        
        # 生成可视化
        file_name = Path(file_path).stem
        processing_config = config_manager.get_processing_config()
        vis_manager.generate_all_visualizations(results, file_name, processing_config.sampling_rate)
        
        # 生成报告
        total_time = time.time() - start_time
        report_generator.save_report(results, file_name, total_time)
        
        logger.info(f"文件处理完成: {file_path}, 耗时: {total_time:.2f}秒")
        return results
        
    except Exception as e:
        logger.error(f"处理文件失败: {file_path}, 错误: {e}")
        raise

def process_directory_legacy(dir_path: str, config_manager: ConfigManager,
                           vmd_engine: VMDEngine, vis_manager: VisualizationManager,
                           report_generator: ReportGenerator):
    """处理目录中的所有文件（传统方式，逐个处理）"""
    logger = logging.getLogger(__name__)
    
    try:
        dir_path = Path(dir_path)
        
        if not dir_path.exists():
            raise FileNotFoundError(f"目录不存在: {dir_path}")
        
        # 查找所有txt文件
        txt_files = list(dir_path.glob("*.txt"))
        
        if not txt_files:
            logger.warning(f"目录中没有找到txt文件: {dir_path}")
            return
        
        logger.info(f"找到 {len(txt_files)} 个txt文件")
        
        # 逐个处理文件
        all_results = {}
        for file_path in txt_files:
            try:
                results = process_single_file(str(file_path), config_manager, vmd_engine, 
                                           vis_manager, report_generator)
                all_results[str(file_path)] = results
            except Exception as e:
                logger.error(f"处理文件 {file_path} 失败: {e}")
                continue
        
        logger.info(f"目录处理完成，成功处理 {len(all_results)} 个文件")
        return all_results
        
    except Exception as e:
        logger.error(f"处理目录失败: {dir_path}, 错误: {e}")
        raise

def process_directory_batch(dir_path: str, config_manager: ConfigManager,
                          file_filter: Optional[str] = None,
                          max_files: Optional[int] = None):
    """批量处理目录中的所有文件"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始批量处理目录: {dir_path}")
        
        # 初始化批量处理器
        batch_processor = BatchProcessor(config_manager)
        
        # 执行批量处理
        batch_stats = batch_processor.process_batch(dir_path, file_filter, max_files)
        
        # 获取输出目录
        output_path = config_manager.get_path('../../output')
        
        # 生成批量可视化
        batch_processor.generate_batch_visualizations(str(output_path))
        
        # 生成批量报告
        batch_processor.generate_batch_report(str(output_path))
        
        logger.info("=" * 60)
        logger.info("批量处理完成总结:")
        logger.info("=" * 60)
        logger.info(f"总文件数: {batch_stats.total_files}")
        logger.info(f"成功处理: {batch_stats.successful_files}")
        logger.info(f"处理失败: {batch_stats.failed_files}")
        logger.info(f"成功率: {batch_stats.successful_files/batch_stats.total_files*100:.1f}%")
        logger.info(f"总处理时间: {batch_stats.total_processing_time:.2f}秒")
        logger.info(f"平均相关性: {batch_stats.average_correlation:.3f}")
        logger.info(f"平均信噪比: {batch_stats.average_snr:.2f}dB")
        logger.info(f"批量报告: {output_path}/batch_processing_report.html")
        logger.info(f"批量可视化: {output_path}/batch_analysis/")
        logger.info("=" * 60)
        
        return batch_stats
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise

def print_config_info(config_manager: ConfigManager, args):
    """打印运行配置信息"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("运行配置信息")
    logger.info("=" * 60)
    
    # 获取配置
    processing_config = config_manager.get_processing_config()
    vmd_config = config_manager.get_vmd_config()
    current_policy = config_manager.config.get('current_policy', 'default')
    filter_policy = config_manager.get_filter_policy(current_policy)
    parallel_config = config_manager.get_parallel_config()
    
    # 基本信息
    logger.info(f"项目名称: {config_manager.config['project']['name']}")
    logger.info(f"项目版本: {config_manager.config['project']['version']}")
    
    # 数据配置
    logger.info(f"输入路径: {config_manager.get_path('input')}")
    logger.info(f"输出路径: {config_manager.get_path('../../output')}")
    logger.info(f"处理通道: {processing_config.channels}")
    logger.info(f"信号长度: {processing_config.sample_length} 点")
    logger.info(f"采样率: {processing_config.sampling_rate} Hz")
    logger.info(f"处理时长: {processing_config.sample_length/processing_config.sampling_rate:.2f} 秒")
    
    # VMD配置
    logger.info(f"VMD模态数: {vmd_config.K}")
    logger.info(f"Alpha参数: {vmd_config.alpha}")
    logger.info(f"Tau参数: {vmd_config.tau}")
    logger.info(f"DC模态: {'是' if vmd_config.DC else '否'}")
    logger.info(f"收敛容忍度: {vmd_config.tol}")
    
    # 滤波策略
    logger.info(f"当前策略: {current_policy} ({filter_policy.name})")
    logger.info(f"策略描述: {filter_policy.description}")
    
    # 并行配置
    if parallel_config.get('enabled', True):
        num_workers = parallel_config.get('num_workers', 0)
        if num_workers == 0:
            num_workers = multiprocessing.cpu_count()
        logger.info(f"并行处理: 已启用 ({num_workers} 个工作进程)")
    else:
        logger.info("并行处理: 已禁用")
    
    # 输出配置
    output_config = config_manager.get_output_config()
    formats = output_config.get('formats', [])
    logger.info(f"输出格式: {', '.join(formats)}")
    
    vis_enabled = output_config.get('visualization', {}).get('enabled', True)
    report_enabled = output_config.get('report', {}).get('enabled', True)
    logger.info(f"可视化: {'已启用' if vis_enabled else '已禁用'}")
    logger.info(f"报告生成: {'已启用' if report_enabled else '已禁用'}")
    
    # 命令行覆盖信息
    if hasattr(args, 'input') and args.input:
        logger.info(f"命令行覆盖 - 输入路径: {args.input}")
    if hasattr(args, 'channels') and args.channels:
        logger.info(f"命令行覆盖 - 处理通道: {args.channels}")
    if hasattr(args, 'policy') and args.policy:
        logger.info(f"命令行覆盖 - 滤波策略: {args.policy}")
    if hasattr(args, 'test') and args.test:
        logger.info("命令行覆盖 - 测试模式: 已启用")
    
    logger.info("=" * 60)

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    logger.info("=" * 50)
    logger.info("VMD 61通道数据处理工程启动")
    logger.info("=" * 50)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='VMD 61通道数据处理工程')
    parser.add_argument('--config', '-c', default='config.yaml', 
                       help='配置文件路径 (默认: config.yaml)')
    parser.add_argument('--input', '-i', 
                       help='输入文件或目录路径 (覆盖配置文件设置)')
    parser.add_argument('--output', '-o', 
                       help='输出目录路径 (覆盖配置文件设置)')
    parser.add_argument('--policy', '-p', 
                       help='滤波策略名称 (覆盖配置文件设置)')
    parser.add_argument('--channels', nargs='+', type=int,
                       help='处理通道列表 (覆盖配置文件设置)')
    parser.add_argument('--parallel', action='store_true',
                       help='强制启用并行处理')
    parser.add_argument('--no-parallel', action='store_true',
                       help='强制禁用并行处理')
    parser.add_argument('--no-vis', action='store_true',
                       help='不生成可视化图片')
    parser.add_argument('--no-report', action='store_true',
                       help='不生成报告')
    parser.add_argument('--test', action='store_true',
                       help='测试模式：处理前3秒数据')
    parser.add_argument('--batch', action='store_true',
                       help='批量处理模式：处理目录中的所有txt文件')
    parser.add_argument('--legacy', action='store_true',
                       help='使用传统方式逐个处理文件（不生成批量报告）')
    parser.add_argument('--filter', type=str,
                       help='文件名过滤器：只处理包含指定字符串的文件')
    parser.add_argument('--max-files', type=int,
                       help='限制处理的最大文件数量')
    parser.add_argument('--single', action='store_true',
                       help='强制单文件处理模式（即使输入是目录）')
    
    args = parser.parse_args()
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager(args.config)
        
        # 根据命令行参数覆盖配置
        if args.input:
            config_manager.update_config('data', 'input_path', args.input)
        
        if args.output:
            config_manager.update_config('data', 'output_path', args.output)
        
        if args.policy:
            if args.policy not in config_manager.get_policy_names():
                raise ValueError(f"策略 '{args.policy}' 不存在")
            config_manager.update_config('', 'current_policy', args.policy)
        
        if args.channels:
            config_manager.update_config('data', 'processing', 
                                       {**config_manager.config['data']['processing'], 
                                        'channels': args.channels})
        
        if args.parallel:
            config_manager.update_config('parallel', 'enabled', True)
        
        if args.no_parallel:
            config_manager.update_config('parallel', 'enabled', False)
        
        if args.no_vis:
            config_manager.update_config('output', 'visualization', 
                                       {**config_manager.config['output']['visualization'], 
                                        'enabled': False})
        
        if args.no_report:
            config_manager.update_config('output', 'report', 
                                       {**config_manager.config['output']['report'], 
                                        'enabled': False})
        
        if args.test:
            # 测试模式：处理前3秒数据
            config_manager.update_config('data', 'processing', 
                                       {**config_manager.config['data']['processing'], 
                                        'sample_length': 3000})
            logger.info("测试模式：处理前3秒数据")
        
        # 打印配置信息
        print_config_info(config_manager, args)
        
        # 初始化组件
        vmd_engine = VMDEngine(config_manager)
        vis_manager = VisualizationManager(config_manager)
        report_generator = ReportGenerator(config_manager)
        
        # 获取输入路径
        input_path = config_manager.get_path('input')
        
        # 判断处理模式
        if input_path.is_file():
            # 处理单个文件
            process_single_file(str(input_path), config_manager, vmd_engine, 
                              vis_manager, report_generator)
        elif input_path.is_dir():
            if args.single:
                # 强制单文件模式：报错
                raise ValueError("输入是目录但指定了单文件模式，请检查输入路径或移除--single参数")
            elif args.batch or (not args.legacy):
                # 批量处理模式（默认）
                logger.info("使用批量处理模式")
                process_directory_batch(str(input_path), config_manager, 
                                      args.filter, args.max_files)
            else:
                # 传统处理模式
                logger.info("使用传统逐个处理模式")
                process_directory_legacy(str(input_path), config_manager, vmd_engine, 
                                       vis_manager, report_generator)
        else:
            raise FileNotFoundError(f"输入路径不存在: {input_path}")
        
        logger.info("=" * 50)
        logger.info("VMD 61通道数据处理工程完成")
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()