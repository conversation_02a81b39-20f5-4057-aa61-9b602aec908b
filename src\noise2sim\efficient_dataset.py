"""
Efficient dataset implementation that loads data once and shares across all groups
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from typing import List, Tuple, Dict, Optional
import logging
import os
from pathlib import Path
from collections import OrderedDict
import gc

from .utils import (
    load_mcg_data, 
    create_patches, 
    group_channels, 
    create_mask_indices, 
    apply_mask,
    set_seed
)
from tqdm import tqdm
from .config import Config

logger = logging.getLogger(__name__)

class SharedDataCache:
    """
    Shared cache that loads data once and provides patches for all groups
    """
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.cache = OrderedDict()  # file_path -> {'patches': grouped_patches, 'access_time': timestamp}
        self.access_counter = 0
    
    def get_file_patches(self, file_path: str, config: Config) -> Optional[Dict[int, np.ndarray]]:
        """Get grouped patches for a file, loading if necessary"""
        if file_path in self.cache:
            # Update access time and return cached data
            self.cache[file_path]['access_time'] = self.access_counter
            self.access_counter += 1
            self.cache.move_to_end(file_path)
            return self.cache[file_path]['patches']
        
        # Load and process file
        try:
            # Load file data once
            signal_channels, _ = load_mcg_data(file_path)
            
            # Create patches once
            patches = create_patches(
                signal_channels, 
                config.patch_length, 
                config.overlap_step
            )
            
            # Group channels once - this creates all 4 groups
            grouped_patches_list = group_channels(patches, config.channel_groups)
            
            # Convert list to dictionary for easier access
            grouped_patches = {i: grouped_patches_list[i] for i in range(len(grouped_patches_list))}
            
            # Cache the grouped patches
            if len(self.cache) >= self.capacity:
                # Remove least recently used item
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                gc.collect()
            
            self.cache[file_path] = {
                'patches': grouped_patches,
                'access_time': self.access_counter
            }
            self.access_counter += 1
            
            # Only log loading progress occasionally to avoid spam
            if len(self.cache) <= 5:
                logger.info(f"Loading progress: {len(self.cache)}/{self.capacity} files cached")
            return grouped_patches
            
        except Exception as e:
            logger.error(f"Error loading file {file_path}: {e}")
            return None
    
    def clear(self):
        """Clear cache"""
        self.cache.clear()
        gc.collect()
    
    def get_stats(self):
        """Get cache statistics"""
        return {
            'cache_size': len(self.cache),
            'cache_capacity': self.capacity,
            'total_accesses': self.access_counter
        }

class EfficientMCGDataset(Dataset):
    """
    Efficient MCG Dataset that shares loaded data across all groups
    """
    
    def __init__(self, 
                 file_paths: List[str], 
                 config: Config, 
                 group_idx: int = 0,
                 shared_cache: Optional[SharedDataCache] = None,
                 transform: Optional[callable] = None):
        """
        Initialize Efficient MCG Dataset
        
        Args:
            file_paths: List of data file paths
            config: Configuration object
            group_idx: Channel group index (0-3)
            shared_cache: Shared cache instance
            transform: Optional transform function
        """
        self.file_paths = file_paths
        self.config = config
        self.group_idx = group_idx
        self.transform = transform
        
        # Validate group index
        if group_idx not in config.channel_groups:
            raise ValueError(f"Invalid group_idx: {group_idx}")
        
        self.channel_indices = config.channel_groups[group_idx]
        
        # Use shared cache or create new one
        self.shared_cache = shared_cache if shared_cache is not None else SharedDataCache(config.cache_size)
        
        # Pre-compute dataset structure without loading data
        self.sample_info = []  # List of (file_idx, patch_idx) tuples
        self.patches_per_file = []  # Number of patches per file
        
        self._compute_dataset_structure()
        
        logger.info(f"Efficient dataset initialized for group {group_idx}: {len(self.sample_info):,} samples")
    
    def _get_file_length(self, file_path: str) -> int:
        """Get file length without loading full data"""
        try:
            # Count lines in file efficiently
            with open(file_path, 'r') as f:
                # Skip header if present
                first_line = f.readline().strip()
                if not first_line or first_line.startswith('#') or 'time' in first_line.lower():
                    # Has header, start counting from next line
                    line_count = sum(1 for _ in f)
                else:
                    # No header, count this line too
                    line_count = 1 + sum(1 for _ in f)
            
            return line_count
            
        except Exception as e:
            logger.warning(f"Could not get length for {file_path}, falling back to full load: {e}")
            # Fallback to loading full file
            signal_channels, _ = load_mcg_data(file_path)
            return signal_channels.shape[0]
    
    def _compute_dataset_structure(self):
        """Compute dataset structure without loading full data"""
        for file_idx, file_path in enumerate(self.file_paths):
            try:
                # Get file shape without loading full data
                n_samples = self._get_file_length(file_path)
                
                # Calculate number of patches for this file
                n_patches = (n_samples - self.config.patch_length) // self.config.overlap_step + 1
                
                if n_patches > 0:
                    self.patches_per_file.append(n_patches)
                    
                    # Add sample info for each patch
                    for patch_idx in range(n_patches):
                        self.sample_info.append((file_idx, patch_idx))
                else:
                    self.patches_per_file.append(0)
                    logger.warning(f"No patches generated for file {file_path}")
                
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {e}")
                self.patches_per_file.append(0)
                continue
    
    def __len__(self) -> int:
        """Return total number of samples"""
        return len(self.sample_info)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample with masking
        
        Args:
            idx: Sample index
            
        Returns:
            sample: Dictionary containing masked patch data
        """
        if idx >= len(self.sample_info):
            raise IndexError(f"Index {idx} out of range for dataset of size {len(self.sample_info)}")
        
        file_idx, patch_idx = self.sample_info[idx]
        file_path = self.file_paths[file_idx]
        
        # Get grouped patches from shared cache
        grouped_patches = self.shared_cache.get_file_patches(file_path, self.config)
        
        if grouped_patches is None or self.group_idx not in grouped_patches:
            # Return a dummy sample if loading fails
            logger.warning(f"Failed to load sample {idx}, returning dummy data")
            dummy_patch = np.zeros((9, self.config.patch_length))
            dummy_target = np.zeros((9, self.config.n_mask_points))
            dummy_mask = np.arange(self.config.n_mask_points)
            
            return {
                'input': torch.FloatTensor(dummy_patch),
                'target': torch.FloatTensor(dummy_target),
                'mask_indices': torch.LongTensor(dummy_mask),
                'file_idx': torch.LongTensor([file_idx]),
                'patch_idx': torch.LongTensor([patch_idx])
            }
        
        # Get patches for this group
        group_patches = grouped_patches[self.group_idx]
        
        if patch_idx >= len(group_patches):
            logger.warning(f"Patch index {patch_idx} out of range for file {file_path}")
            # Return dummy data
            dummy_patch = np.zeros((9, self.config.patch_length))
            dummy_target = np.zeros((9, self.config.n_mask_points))
            dummy_mask = np.arange(self.config.n_mask_points)
            
            return {
                'input': torch.FloatTensor(dummy_patch),
                'target': torch.FloatTensor(dummy_target),
                'mask_indices': torch.LongTensor(dummy_mask),
                'file_idx': torch.LongTensor([file_idx]),
                'patch_idx': torch.LongTensor([patch_idx])
            }
        
        # Get the specific patch
        patch = group_patches[patch_idx].copy()  # Shape: (9, patch_length)
        
        # Create mask indices
        mask_config = self.config.get_mask_config()
        mask_indices = create_mask_indices(
            self.config.patch_length, 
            mask_config['n_mask_points'],
            mask_config['mask_type'],
            mask_config['block_size'],
            mask_config['n_blocks']
        )
        
        # Extract target values (original values at masked positions)
        target = patch[:, mask_indices]  # Shape: (9, n_mask_points)
        
        # Apply mask to create input
        input_patch = apply_mask(patch, mask_indices)
        
        # Apply transform if provided
        if self.transform:
            input_patch = self.transform(input_patch)
            target = self.transform(target)
        
        # Convert to tensors
        sample = {
            'input': torch.FloatTensor(input_patch),
            'target': torch.FloatTensor(target),
            'mask_indices': torch.LongTensor(mask_indices),
            'file_idx': torch.LongTensor([file_idx]),
            'patch_idx': torch.LongTensor([patch_idx])
        }
        
        return sample
    
    def clear_cache(self):
        """Clear the shared cache"""
        self.shared_cache.clear()
        logger.info("Shared cache cleared")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        return self.shared_cache.get_stats()

class EfficientMixedGroupDataset(Dataset):
    """
    Efficient mixed group dataset for single model training
    Uses shared cache to avoid loading data multiple times
    """
    
    def __init__(self, file_paths: List[str], config: Config, transform: Optional[callable] = None):
        """
        Initialize efficient mixed group dataset
        
        Args:
            file_paths: List of data file paths
            config: Configuration object
            transform: Optional transform function
        """
        self.file_paths = file_paths
        self.config = config
        self.transform = transform
        
        # Create shared cache
        self.shared_cache = SharedDataCache(config.cache_size)
        
        # Create individual group datasets that share the cache
        self.group_datasets = []
        for group_idx in range(4):
            dataset = EfficientMCGDataset(
                file_paths, config, group_idx, self.shared_cache, transform
            )
            self.group_datasets.append(dataset)
        
        # For single model training, we use the same number of samples as one group
        # But each sample can come from any of the 4 groups
        self.total_samples = len(self.group_datasets[0])  # All groups should have same length
        
        # Verify all groups have same length
        for i, dataset in enumerate(self.group_datasets):
            if len(dataset) != self.total_samples:
                logger.warning(f"Group {i} has {len(dataset)} samples, expected {self.total_samples}")
        
        # For mixed training, we don't need index mapping - we'll randomly select groups
        
        logger.info(f"Efficient mixed group dataset initialized: {self.total_samples:,} total samples")
    
    def __len__(self) -> int:
        """Return total number of samples across all groups"""
        return self.total_samples
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a sample from any of the 4 groups (randomly selected)
        
        Args:
            idx: Sample index
            
        Returns:
            sample: Dictionary containing 9-channel patch data
        """
        if idx >= self.total_samples:
            raise IndexError(f"Index {idx} out of range for dataset of size {self.total_samples}")
        
        # Randomly select a group for this sample
        group_idx = np.random.randint(0, 4)
        
        # Get sample from the selected group dataset
        sample = self.group_datasets[group_idx][idx]
        
        # Add group information for debugging/tracking
        sample['group_idx'] = torch.LongTensor([group_idx])
        
        return sample
    
    def clear_cache(self):
        """Clear the shared cache"""
        self.shared_cache.clear()
        logger.info("Efficient mixed dataset cache cleared")
    
    def get_cache_stats(self) -> Dict[str, any]:
        """Get cache statistics"""
        return self.shared_cache.get_stats()

class EfficientMCGDataModule:
    """
    Efficient data module that uses shared caching
    """
    
    def __init__(self, config: Config):
        """
        Initialize efficient data module
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.train_files, self.test_files = config.get_data_paths()
        
        logger.info(f"Efficient data module initialized")
        logger.info(f"Training files: {len(self.train_files)}")
        logger.info(f"Testing files: {len(self.test_files)}")
        
        if config.single_model:
            # Single model mode: create one mixed dataset with shared cache
            self.train_dataset = EfficientMixedGroupDataset(self.train_files, config)
            self.test_dataset = EfficientMixedGroupDataset(self.test_files, config)
            self.train_datasets = None
            self.test_datasets = None
        else:
            # Multi-model mode: create datasets with shared cache
            self.shared_train_cache = SharedDataCache(config.cache_size)
            self.shared_test_cache = SharedDataCache(config.cache_size)
            
            self.train_datasets = []
            self.test_datasets = []
            
            for group_idx in range(4):
                train_dataset = EfficientMCGDataset(
                    self.train_files, 
                    config, 
                    group_idx=group_idx,
                    shared_cache=self.shared_train_cache
                )
                test_dataset = EfficientMCGDataset(
                    self.test_files, 
                    config, 
                    group_idx=group_idx,
                    shared_cache=self.shared_test_cache
                )
                
                self.train_datasets.append(train_dataset)
                self.test_datasets.append(test_dataset)
    
    def get_train_loader(self, group_idx: int = 0) -> DataLoader:
        """
        Get training dataloader for specific channel group or mixed data
        
        Args:
            group_idx: Channel group index (0-3), ignored in single_model mode
            
        Returns:
            DataLoader for training
        """
        if self.config.single_model:
            return DataLoader(
                self.train_dataset,
                batch_size=self.config.batch_size,
                shuffle=True,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
        else:
            return DataLoader(
                self.train_datasets[group_idx],
                batch_size=self.config.batch_size,
                shuffle=True,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
    
    def get_test_loader(self, group_idx: int = 0) -> DataLoader:
        """
        Get test dataloader for specific channel group or mixed data
        
        Args:
            group_idx: Channel group index (0-3), ignored in single_model mode
            
        Returns:
            DataLoader for testing
        """
        if self.config.single_model:
            return DataLoader(
                self.test_dataset,
                batch_size=self.config.batch_size,
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=False,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
        else:
            return DataLoader(
                self.test_datasets[group_idx],
                batch_size=self.config.batch_size,
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=False,
                persistent_workers=True if self.config.num_workers > 0 else False
            )
    
    def get_dataset_stats(self) -> Dict:
        """Get dataset statistics"""
        stats = {
            'train_files': len(self.train_files),
            'test_files': len(self.test_files),
            'single_model': self.config.single_model
        }
        
        if self.config.single_model:
            # Single model statistics
            stats['mixed_data'] = {
                'train_samples': len(self.train_dataset),
                'test_samples': len(self.test_dataset),
                'train_cache_stats': self.train_dataset.get_cache_stats(),
                'test_cache_stats': self.test_dataset.get_cache_stats()
            }
        else:
            # Multi-model statistics
            stats['groups'] = {}
            for group_idx in range(4):
                train_cache_stats = self.train_datasets[group_idx].get_cache_stats()
                test_cache_stats = self.test_datasets[group_idx].get_cache_stats()
                
                stats['groups'][group_idx] = {
                    'train_samples': len(self.train_datasets[group_idx]),
                    'test_samples': len(self.test_datasets[group_idx]),
                    'channels': self.config.channel_groups[group_idx],
                    'train_cache_stats': train_cache_stats,
                    'test_cache_stats': test_cache_stats
                }
        
        return stats
    
    def clear_caches(self):
        """Clear all dataset caches"""
        if self.config.single_model:
            self.train_dataset.clear_cache()
            self.test_dataset.clear_cache()
        else:
            self.shared_train_cache.clear()
            self.shared_test_cache.clear()
        logger.info("All dataset caches cleared")