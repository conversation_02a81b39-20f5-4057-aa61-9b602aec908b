"""
N2SIM (Noise2Sim) utilities for similarity-based denoising
Extends the N2V approach by using similarity search for better targets
"""

import numpy as np
from typing import List, Tuple, Dict, Optional
import logging
from scipy.spatial.distance import cdist
from scipy.stats import pearsonr
import torch
from collections import OrderedDict
import time

logger = logging.getLogger(__name__)

class SimilarityCache:
    """
    Cache for storing computed similarities to avoid redundant calculations
    """
    
    def __init__(self, capacity: int = 1000):
        self.capacity = capacity
        self.cache = OrderedDict()
    
    def get_key(self, patch: np.ndarray, target_idx: int) -> str:
        """Generate cache key from patch and target index"""
        patch_hash = hash(patch.tobytes())
        return f"{patch_hash}_{target_idx}"
    
    def get(self, patch: np.ndarray, target_idx: int) -> Optional[np.ndarray]:
        """Get cached similarities"""
        key = self.get_key(patch, target_idx)
        if key in self.cache:
            self.cache.move_to_end(key)  # Mark as recently used
            return self.cache[key]
        return None
    
    def put(self, patch: np.ndarray, target_idx: int, similarities: np.ndarray):
        """Store similarities in cache"""
        key = self.get_key(patch, target_idx)
        
        if len(self.cache) >= self.capacity:
            # Remove least recently used
            self.cache.popitem(last=False)
        
        self.cache[key] = similarities.copy()
    
    def clear(self):
        """Clear cache"""
        self.cache.clear()

def compute_euclidean_distance(patch: np.ndarray, subsequences: np.ndarray) -> np.ndarray:
    """
    Compute Euclidean distance between patch and all subsequences
    
    Args:
        patch: Reference patch (window_size,)
        subsequences: All subsequences (n_subsequences, window_size)
        
    Returns:
        distances: Euclidean distances (n_subsequences,)
    """
    # Reshape if needed
    if patch.ndim == 1:
        patch = patch.reshape(1, -1)
    
    # Compute distances using scipy's cdist (optimized)
    distances = cdist(patch, subsequences, metric='euclidean').flatten()
    return distances

def compute_cosine_similarity(patch: np.ndarray, subsequences: np.ndarray) -> np.ndarray:
    """
    Compute cosine similarity between patch and all subsequences
    
    Args:
        patch: Reference patch (window_size,)
        subsequences: All subsequences (n_subsequences, window_size)
        
    Returns:
        similarities: Cosine similarities (n_subsequences,)
    """
    # Normalize patch
    patch_norm = patch / (np.linalg.norm(patch) + 1e-8)
    
    # Normalize subsequences
    subsequences_norm = subsequences / (np.linalg.norm(subsequences, axis=1, keepdims=True) + 1e-8)
    
    # Compute cosine similarity
    similarities = np.dot(subsequences_norm, patch_norm)
    return similarities

def compute_correlation_similarity(patch: np.ndarray, subsequences: np.ndarray) -> np.ndarray:
    """
    Compute correlation coefficient between patch and all subsequences
    Optimized vectorized version
    
    Args:
        patch: Reference patch (window_size,)
        subsequences: All subsequences (n_subsequences, window_size)
        
    Returns:
        correlations: Correlation coefficients (n_subsequences,)
    """
    # Vectorized correlation computation
    patch_mean = np.mean(patch)
    patch_centered = patch - patch_mean
    patch_std = np.std(patch)
    
    if patch_std == 0:
        return np.zeros(len(subsequences))
    
    subseq_means = np.mean(subsequences, axis=1)
    subseq_centered = subsequences - subseq_means[:, np.newaxis]
    subseq_stds = np.std(subsequences, axis=1)
    
    # Avoid division by zero
    valid_mask = subseq_stds > 0
    correlations = np.zeros(len(subsequences))
    
    if np.any(valid_mask):
        numerator = np.dot(subseq_centered[valid_mask], patch_centered)
        denominator = subseq_stds[valid_mask] * patch_std * len(patch)
        correlations[valid_mask] = numerator / denominator
    
    # Clamp to [-1, 1] range
    correlations = np.clip(correlations, -1, 1)
    
    return correlations

def extract_subsequences(signal: np.ndarray, window_size: int, target_idx: int, 
                        exclude_radius: int = 0) -> Tuple[np.ndarray, np.ndarray]:
    """
    Extract all possible subsequences from signal
    
    Args:
        signal: Input signal (patch_length,)
        window_size: Size of each subsequence
        target_idx: Index of target point (to exclude nearby subsequences)
        exclude_radius: Radius around target to exclude
        
    Returns:
        subsequences: Array of subsequences (n_subsequences, window_size)
        indices: Center indices of each subsequence
    """
    signal_length = len(signal)
    half_window = window_size // 2
    
    # Calculate valid start positions
    valid_starts = []
    valid_indices = []
    
    for start in range(signal_length - window_size + 1):
        center_idx = start + half_window
        
        # Exclude subsequences too close to target
        if exclude_radius > 0:
            if abs(center_idx - target_idx) <= exclude_radius:
                continue
        
        valid_starts.append(start)
        valid_indices.append(center_idx)
    
    # Extract subsequences
    subsequences = np.array([signal[start:start + window_size] for start in valid_starts])
    indices = np.array(valid_indices)
    
    return subsequences, indices

def find_similar_subsequences(signal: np.ndarray, target_idx: int, 
                            window_size: int, k_similar: int,
                            distance_metric: str = "euclidean",
                            exclude_radius: int = 256,
                            similarity_threshold: float = 0.3,  # Lower threshold for faster computation
                            cache: Optional[SimilarityCache] = None,
                            max_candidates: int = 500) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Find k most similar subsequences to the window around target_idx
    
    Args:
        signal: Input signal (patch_length,)
        target_idx: Index of target point
        window_size: Size of similarity search window
        k_similar: Number of similar subsequences to find
        distance_metric: Distance metric to use
        exclude_radius: Radius around target to exclude
        similarity_threshold: Minimum similarity threshold
        cache: Optional similarity cache
        
    Returns:
        similar_indices: Indices of center points of similar subsequences
        similar_values: Values at center points of similar subsequences  
        similarities: Similarity scores
    """
    signal_length = len(signal)
    half_window = window_size // 2
    
    # Check bounds
    if target_idx < half_window or target_idx >= signal_length - half_window:
        # Return dummy values if target is too close to edges
        return np.array([target_idx]), np.array([signal[target_idx]]), np.array([1.0])
    
    # Extract target patch
    target_start = target_idx - half_window
    target_patch = signal[target_start:target_start + window_size]
    
    # Check cache first
    if cache is not None:
        cached_similarities = cache.get(target_patch, target_idx)
        if cached_similarities is not None:
            similarities = cached_similarities
        else:
            # Extract subsequences with subsampling for speed
            subsequences, center_indices = extract_subsequences(
                signal, window_size, target_idx, exclude_radius
            )
            
            if len(subsequences) == 0:
                return np.array([target_idx]), np.array([signal[target_idx]]), np.array([1.0])
            
            # Limit number of candidates for speed
            if len(subsequences) > max_candidates:
                step = len(subsequences) // max_candidates
                subsequences = subsequences[::step]
                center_indices = center_indices[::step]
            
            # Compute similarities
            if distance_metric == "euclidean":
                distances = compute_euclidean_distance(target_patch, subsequences)
                # Convert distances to similarities (higher is more similar)
                max_dist = np.max(distances) + 1e-8
                similarities = 1.0 - (distances / max_dist)
            elif distance_metric == "cosine":
                similarities = compute_cosine_similarity(target_patch, subsequences)
            elif distance_metric == "correlation":
                similarities = compute_correlation_similarity(target_patch, subsequences)
            else:
                raise ValueError(f"Unknown distance metric: {distance_metric}")
            
            # Cache the similarities
            cache.put(target_patch, target_idx, similarities)
    else:
        # Extract subsequences with subsampling for speed
        subsequences, center_indices = extract_subsequences(
            signal, window_size, target_idx, exclude_radius
        )
        
        if len(subsequences) == 0:
            return np.array([target_idx]), np.array([signal[target_idx]]), np.array([1.0])
        
        # Limit number of candidates for speed
        if len(subsequences) > max_candidates:
            step = len(subsequences) // max_candidates
            subsequences = subsequences[::step]
            center_indices = center_indices[::step]
        
        # Compute similarities
        if distance_metric == "euclidean":
            distances = compute_euclidean_distance(target_patch, subsequences)
            max_dist = np.max(distances) + 1e-8
            similarities = 1.0 - (distances / max_dist)
        elif distance_metric == "cosine":
            similarities = compute_cosine_similarity(target_patch, subsequences)
        elif distance_metric == "correlation":
            similarities = compute_correlation_similarity(target_patch, subsequences)
        else:
            raise ValueError(f"Unknown distance metric: {distance_metric}")
    
    # Filter by similarity threshold
    valid_mask = similarities >= similarity_threshold
    if np.sum(valid_mask) == 0:
        # If no valid similarities, use target itself
        return np.array([target_idx]), np.array([signal[target_idx]]), np.array([1.0])
    
    valid_similarities = similarities[valid_mask]
    valid_indices = center_indices[valid_mask]
    
    # Find top k similar subsequences
    k_actual = min(k_similar, len(valid_similarities))
    top_k_idx = np.argsort(valid_similarities)[-k_actual:][::-1]  # Descending order
    
    similar_indices = valid_indices[top_k_idx]
    similar_values = signal[similar_indices]
    similar_scores = valid_similarities[top_k_idx]
    
    return similar_indices, similar_values, similar_scores

def create_n2sim_targets(patch: np.ndarray, mask_indices: np.ndarray, 
                        config) -> Tuple[np.ndarray, Dict]:
    """
    Create N2SIM targets using similarity search for each masked point
    
    Args:
        patch: Input patch (9, patch_length)
        mask_indices: Indices of masked points
        config: Configuration object with N2SIM parameters
        
    Returns:
        targets: N2SIM targets (9, n_mask_points)
        metadata: Dictionary with similarity metadata
    """
    n_channels, patch_length = patch.shape
    n_mask_points = len(mask_indices)
    targets = np.zeros((n_channels, n_mask_points))
    
    # Similarity cache if enabled
    cache = SimilarityCache() if config.n2sim_cache_similarities else None
    
    # Metadata for debugging/analysis
    metadata = {
        'avg_similarities': np.zeros(n_channels),
        'n_similar_found': np.zeros((n_channels, n_mask_points)),
        'computation_time': 0.0
    }
    
    start_time = time.time()
    
    # Process each channel
    for ch in range(n_channels):
        channel_signal = patch[ch]
        channel_similarities = []
        
        # Process each masked point
        for i, mask_idx in enumerate(mask_indices):
            # Find similar subsequences for this masked point
            similar_indices, similar_values, similarities = find_similar_subsequences(
                signal=channel_signal,
                target_idx=mask_idx,
                window_size=config.n2sim_window_size,
                k_similar=config.n2sim_k_similar,
                distance_metric=config.n2sim_distance_metric,
                exclude_radius=config.n2sim_exclude_radius,
                similarity_threshold=config.n2sim_similarity_threshold,
                cache=cache
            )
            
            # Create target as weighted average of similar values
            if len(similarities) > 1:
                # Weighted average using similarities as weights
                weights = similarities / np.sum(similarities)
                target_value = np.sum(similar_values * weights)
            else:
                # Single similar point or fallback to original
                target_value = similar_values[0] if len(similar_values) > 0 else channel_signal[mask_idx]
            
            targets[ch, i] = target_value
            
            # Store metadata
            metadata['n_similar_found'][ch, i] = len(similarities)
            channel_similarities.extend(similarities.tolist())
        
        # Store average similarity for this channel
        if channel_similarities:
            metadata['avg_similarities'][ch] = np.mean(channel_similarities)
    
    metadata['computation_time'] = time.time() - start_time
    
    return targets, metadata

class N2SIMDatasetMixin:
    """
    Mixin class to add N2SIM functionality to existing dataset classes
    """
    
    def __init__(self):
        self.n2sim_cache = SimilarityCache() if hasattr(self, 'config') and self.config.n2sim_cache_similarities else None
        self.n2sim_stats = {
            'total_calls': 0,
            'avg_computation_time': 0.0,
            'avg_similarities': []
        }
    
    def get_n2sim_targets(self, patch: np.ndarray, mask_indices: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        Get N2SIM targets instead of N2V targets
        
        Args:
            patch: Input patch (9, patch_length)
            mask_indices: Indices of masked points
            
        Returns:
            targets: N2SIM targets (9, n_mask_points)
            metadata: Dictionary with similarity metadata
        """
        if not hasattr(self, 'config') or not self.config.use_n2sim:
            # Fallback to N2V behavior
            return patch[:, mask_indices], {}
        
        targets, metadata = create_n2sim_targets(patch, mask_indices, self.config)
        
        # Update statistics
        self.n2sim_stats['total_calls'] += 1
        self.n2sim_stats['avg_computation_time'] = (
            (self.n2sim_stats['avg_computation_time'] * (self.n2sim_stats['total_calls'] - 1) + 
             metadata['computation_time']) / self.n2sim_stats['total_calls']
        )
        self.n2sim_stats['avg_similarities'].append(np.mean(metadata['avg_similarities']))
        
        return targets, metadata
    
    def get_n2sim_stats(self) -> Dict:
        """Get N2SIM performance statistics"""
        stats = self.n2sim_stats.copy()
        if stats['avg_similarities']:
            stats['overall_avg_similarity'] = np.mean(stats['avg_similarities'])
        else:
            stats['overall_avg_similarity'] = 0.0
        return stats

if __name__ == "__main__":
    # Test N2SIM functionality
    np.random.seed(42)
    
    # Create synthetic MCG-like signal
    patch_length = 4096
    n_channels = 9
    
    # Generate signal with repeating patterns (simulate heartbeats)
    t = np.linspace(0, 4, patch_length)
    base_signal = np.zeros(patch_length)
    
    # Add periodic patterns (heartbeat-like)
    for beat_time in np.arange(0.5, 4, 0.8):  # ~75 BPM
        beat_start = int(beat_time * 1000)
        if beat_start < patch_length - 200:
            # QRS complex simulation
            beat_pattern = np.exp(-(np.arange(200) - 50)**2 / 100) * np.sin(np.arange(200) * 0.3)
            base_signal[beat_start:beat_start + 200] += beat_pattern
    
    # Add noise
    signal = base_signal + np.random.normal(0, 0.1, patch_length)
    
    # Create 9-channel patch (with channel variations)
    patch = np.array([signal + np.random.normal(0, 0.05, patch_length) for _ in range(n_channels)])
    
    # Create mask indices
    mask_indices = np.random.choice(patch_length, 128, replace=False)
    mask_indices = mask_indices[mask_indices > 512]  # Avoid edges
    mask_indices = mask_indices[mask_indices < patch_length - 512]
    mask_indices = mask_indices[:64]  # Take first 64 valid indices
    
    print("Testing N2SIM functionality...")
    print(f"Patch shape: {patch.shape}")
    print(f"Mask indices: {len(mask_indices)} points")
    
    # Test with different metrics
    for metric in ["euclidean", "cosine", "correlation"]:
        print(f"\nTesting with {metric} metric:")
        
        # Mock config
        class MockConfig:
            use_n2sim = True
            n2sim_window_size = 256
            n2sim_k_similar = 5
            n2sim_distance_metric = metric
            n2sim_exclude_radius = 128
            n2sim_cache_similarities = True
            n2sim_similarity_threshold = 0.5
        
        config = MockConfig()
        
        # Create N2SIM targets
        start_time = time.time()
        targets, metadata = create_n2sim_targets(patch, mask_indices, config)
        end_time = time.time()
        
        print(f"  Computation time: {end_time - start_time:.4f}s")
        print(f"  Average similarities: {np.mean(metadata['avg_similarities']):.4f}")
        print(f"  Target shape: {targets.shape}")
        print(f"  Average similar points found: {np.mean(metadata['n_similar_found']):.2f}")
    
    print("\nN2SIM test completed!")