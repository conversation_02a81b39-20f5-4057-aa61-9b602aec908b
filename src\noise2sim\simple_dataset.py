"""
简化的数据集实现，避免过度设计
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from typing import List, Tuple, Dict, Optional
import logging
import os
from pathlib import Path
import random

from .utils import (
    load_mcg_data, 
    create_patches, 
    group_channels, 
    create_mask_indices, 
    apply_mask,
    set_seed
)
from .n2sim_utils import N2SIMDatasetMixin
from .fast_n2sim import FastN2SIMDatasetMixin
from .config import Config

logger = logging.getLogger(__name__)

class SimpleMCGDataset(Dataset, N2SIMDatasetMixin, FastN2SIMDatasetMixin):
    """
    简化的MCG数据集，预加载所有数据到内存
    """
    
    def __init__(self, 
                 file_paths: List[str], 
                 config: Config, 
                 group_idx: int = 0,
                 is_single_model: bool = False,
                 transform: Optional[callable] = None):
        """
        初始化数据集
        
        Args:
            file_paths: 数据文件路径列表
            config: 配置对象
            group_idx: 通道组索引 (0-3)，单模型模式下忽略
            is_single_model: 是否为单模型模式
            transform: 可选的变换函数
        """
        self.file_paths = file_paths
        self.config = config
        self.group_idx = group_idx
        self.is_single_model = is_single_model
        self.transform = transform
        
        # Initialize N2SIM mixins
        N2SIMDatasetMixin.__init__(self)
        FastN2SIMDatasetMixin.__init__(self)
        
        # 预加载所有patches
        self.patches = []
        self.group_info = []  # 存储每个patch来自哪个组
        
        self._load_all_data()
        
        logger.info(f"Simple dataset initialized: {len(self.patches):,} patches")
        if is_single_model:
            logger.info(f"Single model mode: mixed data from all 4 groups")
        else:
            logger.info(f"Multi model mode: group {group_idx} data only")
    
    def _load_all_data(self):
        """加载所有数据到内存"""
        logger.info(f"Loading data from {len(self.file_paths)} files...")
        
        successful_files = 0
        total_patches = 0
        
        for file_idx, file_path in enumerate(self.file_paths):
            try:
                # 加载文件
                signal_channels, _ = load_mcg_data(file_path)
                
                # 创建patches
                patches = create_patches(
                    signal_channels, 
                    self.config.patch_length, 
                    self.config.overlap_step
                )
                
                if patches.shape[0] == 0:
                    logger.warning(f"No patches generated for file {file_path}")
                    continue
                
                # 分组channels
                grouped_patches = group_channels(patches, self.config.channel_groups)
                
                if self.is_single_model:
                    # 单模型模式：添加所有组的patches
                    for group_idx in range(4):
                        group_patches = grouped_patches[group_idx]
                        for patch in group_patches:
                            self.patches.append(patch)
                            self.group_info.append(group_idx)
                else:
                    # 多模型模式：只添加指定组的patches
                    group_patches = grouped_patches[self.group_idx]
                    for patch in group_patches:
                        self.patches.append(patch)
                        self.group_info.append(self.group_idx)
                
                successful_files += 1
                file_patches = sum(len(g) for g in grouped_patches) if self.is_single_model else len(grouped_patches[self.group_idx])
                total_patches += file_patches
                
                if file_idx % 50 == 0:  # 每50个文件打印一次进度
                    logger.info(f"Loaded {file_idx+1}/{len(self.file_paths)} files, {total_patches} patches so far")
                
            except Exception as e:
                logger.error(f"Failed to load file {file_path}: {e}")
                continue
        
        logger.info(f"Successfully loaded {successful_files}/{len(self.file_paths)} files")
        logger.info(f"Total patches: {len(self.patches):,}")
        
        # 转换为numpy数组以提高访问效率
        self.patches = np.array(self.patches)
        self.group_info = np.array(self.group_info)
        
        if self.is_single_model:
            # 统计各组的数量
            group_counts = {i: np.sum(self.group_info == i) for i in range(4)}
            logger.info(f"Group distribution: {group_counts}")
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.patches)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取一个样本
        
        Args:
            idx: 样本索引
            
        Returns:
            sample: 包含掩码后的patch数据的字典
        """
        if idx >= len(self.patches):
            raise IndexError(f"Index {idx} out of range for dataset of size {len(self.patches)}")
        
        # 获取patch
        patch = self.patches[idx].copy()  # Shape: (9, patch_length)
        
        # 创建掩码索引
        mask_config = self.config.get_mask_config()
        mask_indices = create_mask_indices(
            self.config.patch_length, 
            mask_config['n_mask_points'],
            mask_config['mask_type'],
            mask_config['block_size'],
            mask_config['n_blocks']
        )
        
        # 提取目标值（使用FastN2SIM、N2SIM或N2V方法）
        if self.config.use_n2sim:
            # 根据配置选择N2SIM实现
            if self.config.n2sim_use_fast_mode and hasattr(self, 'fast_n2sim') and self.fast_n2sim is not None:
                # 使用FastN2SIM（训练时速度更快）
                target, n2sim_metadata = self.get_fast_n2sim_targets(patch, mask_indices)
            else:
                # 使用常规N2SIM（评估时质量更高）
                target, n2sim_metadata = self.get_n2sim_targets(patch, mask_indices)
        else:
            # 使用传统N2V方法（掩码位置的原始值）
            target = patch[:, mask_indices]  # Shape: (9, n_mask_points)
        
        # 应用掩码创建输入
        input_patch = apply_mask(patch, mask_indices)
        
        # 应用变换（如果有）
        if self.transform:
            input_patch = self.transform(input_patch)
            target = self.transform(target)
        
        # 转换为tensor
        sample = {
            'input': torch.FloatTensor(input_patch),
            'target': torch.FloatTensor(target),
            'mask_indices': torch.LongTensor(mask_indices),
            'group_idx': torch.LongTensor([self.group_info[idx]])
        }
        
        return sample

class SimpleMCGDataModule:
    """
    简化的数据模块
    """
    
    def __init__(self, config: Config):
        """
        初始化数据模块
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.train_files, self.test_files = config.get_data_paths()
        
        logger.info(f"Simple data module initialized")
        logger.info(f"Training files: {len(self.train_files)}")
        logger.info(f"Testing files: {len(self.test_files)}")
        
        if config.single_model:
            # 单模型模式：创建混合数据集
            self.train_dataset = SimpleMCGDataset(
                self.train_files, config, group_idx=0, is_single_model=True
            )
            self.test_dataset = SimpleMCGDataset(
                self.test_files, config, group_idx=0, is_single_model=True
            )
            self.train_datasets = None
            self.test_datasets = None
        else:
            # 多模型模式：为每个组创建数据集
            self.train_datasets = []
            self.test_datasets = []
            
            for group_idx in range(4):
                train_dataset = SimpleMCGDataset(
                    self.train_files, config, group_idx=group_idx, is_single_model=False
                )
                test_dataset = SimpleMCGDataset(
                    self.test_files, config, group_idx=group_idx, is_single_model=False
                )
                
                self.train_datasets.append(train_dataset)
                self.test_datasets.append(test_dataset)
    
    def get_train_loader(self, group_idx: int = 0) -> DataLoader:
        """获取训练数据加载器"""
        if self.config.single_model:
            dataset = self.train_dataset
        else:
            dataset = self.train_datasets[group_idx]
        
        return DataLoader(
            dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory,
            drop_last=True,
            persistent_workers=True if self.config.num_workers > 0 else False
        )
    
    def get_test_loader(self, group_idx: int = 0) -> DataLoader:
        """获取测试数据加载器"""
        if self.config.single_model:
            dataset = self.test_dataset
        else:
            dataset = self.test_datasets[group_idx]
        
        return DataLoader(
            dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory,
            drop_last=False,
            persistent_workers=True if self.config.num_workers > 0 else False
        )
    
    def get_dataset_stats(self) -> Dict:
        """获取数据集统计信息"""
        stats = {
            'train_files': len(self.train_files),
            'test_files': len(self.test_files),
            'single_model': self.config.single_model
        }
        
        if self.config.single_model:
            stats['mixed_data'] = {
                'train_samples': len(self.train_dataset),
                'test_samples': len(self.test_dataset)
            }
        else:
            stats['groups'] = {}
            for group_idx in range(4):
                stats['groups'][group_idx] = {
                    'train_samples': len(self.train_datasets[group_idx]),
                    'test_samples': len(self.test_datasets[group_idx]),
                    'channels': self.config.channel_groups[group_idx]
                }
        
        return stats