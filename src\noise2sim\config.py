"""
Configuration file for Noise 2 Sim training and model parameters
"""

import os
from dataclasses import dataclass
from typing import Tuple, List

@dataclass
class Config:
    # Data parameters
    data_dir: str = "files/降噪北京301的标准源2025-7-14/"
    sampling_rate: int = 1000
    n_signal_channels: int = 36
    n_reference_channels: int = 4
    total_channels: int = 41
    
    # Channel grouping (36 channels -> 4 groups of 9)
    channel_groups: dict = None
    
    # Patch parameters
    patch_length: int = 256  # 1024 or 2048 samples
    overlap_step: int = 64   # Overlap step for sliding window
    n_mask_points: int = 30   # Number of time points to mask
    
    # Mask configuration
    mask_type: str = "point"  # 'point', 'block', 'mixed'
    block_size: int = 8       # Length of continuous mask blocks
    n_blocks: int = 16        # Number of mask blocks (n_blocks * block_size = total mask points)
    
    # Training parameters
    batch_size: int = 128
    learning_rate: float = 0.0004
    num_epochs: int = 150
    train_ratio: float = 0.9
    
    # Model parameters
    input_channels: int = 9   # Channels per group
    hidden_channels: List[int] = None
    kernel_size: int = 3
    dropout_rate: float = 0.1
    
    # Training settings
    device: str = "cuda" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu"
    num_workers: int = 4
    pin_memory: bool = True
    
    # Multi-GPU training
    parallel_groups: bool = True      # Train 4 groups in parallel on different GPUs
    gpu_per_group: int = 1           # Number of GPUs per group
    sync_training: bool = True       # Synchronize training across groups
    single_model: bool = False       # Train single shared model using data from all 4 groups (still 9ch→9ch)
    
    # Data loading optimization
    lazy_loading: bool = False        # Enable lazy loading to save memory
    cache_size: int = 10             # Number of files to cache in memory
    use_memory_mapping: bool = True  # Use memory mapping for large files
    prefetch_batches: int = 2        # Number of batches to prefetch
    
    # Visualization configuration
    vis_signal_duration: int = 10    # Duration of signal to visualize (seconds)
    vis_show_all_channels: bool = True  # Show all 9 channels in visualization
    vis_show_train_samples: bool = True # Show training samples in visualization
    vis_update_frequency: int = 3    # Visualization update frequency (epochs)
    vis_enable_long_signal: bool = True  # Enable long signal visualization (using inference)
    vis_long_signal_segments: int = 3    # Number of segments to visualize for long signals
    vis_show_signal_overview: bool = True # Show overview of entire signal (downsampled)
    
    # Loss function configuration
    use_wavelet_loss: bool = False          # Enable wavelet coefficient loss
    use_amplitude_constraint: bool = True  # Enable amplitude constraint loss
    amplitude_scale_factor: float = 1.2     # Amplitude constraint scale factor
    wavelet_name: str = 'db4'              # Wavelet basis function
    wavelet_levels: int = 4                # Number of wavelet decomposition levels
    
    # Loss weights
    loss_weights: dict = None  # Will be initialized in __post_init__
    
    # N2SIM configuration (扩展N2V方法)
    use_n2sim: bool = False              # Enable N2SIM instead of N2V
    n2sim_window_size: int = 256         # Window size for similarity search (smaller for speed)
    n2sim_k_similar: int = 3             # Number of similar subsequences to use
    n2sim_distance_metric: str = "cosine"  # Distance metric: "euclidean", "cosine", "correlation"
    n2sim_exclude_radius: int = 128      # Exclude radius around target to avoid trivial matches
    n2sim_cache_similarities: bool = True  # Cache similarity computations
    n2sim_similarity_threshold: float = 0.3  # Minimum similarity threshold (0-1, lower for speed)
    n2sim_use_fast_mode: bool = True     # Use FastN2SIM for training (much faster but slightly lower quality)
    
    # Checkpointing
    checkpoint_dir: str = "checkpoints/noise2sim"
    save_every: int = 5
    
    # Logging
    log_dir: str = "logs/noise2sim"
    
    # Evaluation
    eval_every: int = 5
    n_eval_samples: int = 10
    
    def __post_init__(self):
        """Initialize computed fields"""
        if self.channel_groups is None:
            self.channel_groups = {
                0: list(range(0, 9)),    # Group 1: channels 1-9 (0-indexed: 0-8)
                1: list(range(9, 18)),   # Group 2: channels 10-18 (0-indexed: 9-17)
                2: list(range(18, 27)),  # Group 3: channels 19-27 (0-indexed: 18-26)
                3: list(range(27, 36))   # Group 4: channels 28-36 (0-indexed: 27-35)
            }
        
        if self.hidden_channels is None:
            self.hidden_channels = [16, 32, 64, 128, 256]
        
        # Initialize loss weights
        if self.loss_weights is None:
            self.loss_weights = {
                'mse': 1.0,
                'wavelet': 0.1,
                'amplitude': 0.05,
                'frequency': 0.1,
                'r_wave': 0.1
            }
        
        # Create directories
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Validate patch parameters
        # if self.patch_length not in [1024, 2048, 4096]:
        #     raise ValueError("patch_length must be 1024, 2048, or 4096")
        
        if self.overlap_step >= self.patch_length:
            raise ValueError("overlap_step must be less than patch_length")
            
        # Validate mask parameters
        if self.mask_type not in ['point', 'block', 'mixed']:
            raise ValueError("mask_type must be 'point', 'block', or 'mixed'")
            
        if self.mask_type == 'block' and self.n_blocks * self.block_size != self.n_mask_points:
            raise ValueError("n_blocks * block_size must equal n_mask_points for block masking")
        
        # Validate N2SIM parameters
        if self.use_n2sim:
            if self.n2sim_distance_metric not in ['euclidean', 'cosine', 'correlation']:
                raise ValueError("n2sim_distance_metric must be 'euclidean', 'cosine', or 'correlation'")
            
            if self.n2sim_window_size > self.patch_length:
                raise ValueError("n2sim_window_size must be <= patch_length")
            
            if self.n2sim_k_similar < 1:
                raise ValueError("n2sim_k_similar must be >= 1")
            
            if not (0.0 <= self.n2sim_similarity_threshold <= 1.0):
                raise ValueError("n2sim_similarity_threshold must be between 0.0 and 1.0")
            
            print(f"N2SIM mode enabled: window_size={self.n2sim_window_size}, k_similar={self.n2sim_k_similar}, metric={self.n2sim_distance_metric}")
        
        # Validate GPU configuration
        if self.parallel_groups and not self.single_model:
            import torch
            if not torch.cuda.is_available():
                self.parallel_groups = False
                print("Warning: CUDA not available, disabling parallel group training")
            elif torch.cuda.device_count() < 4:
                self.parallel_groups = False
                print(f"Warning: Only {torch.cuda.device_count()} GPUs available, disabling parallel group training")
        
        # Configure single model mode
        if self.single_model:
            self.input_channels = 9  # Still 9 channels input/output
            self.parallel_groups = False  # Single model doesn't use parallel groups
            print("Single model mode enabled: training one shared model using data from all 4 groups")
            
    def get_data_paths(self) -> Tuple[List[str], List[str]]:
        """Get training and testing data file paths"""
        import glob
        
        # Get all txt files
        pattern = os.path.join(self.data_dir, "**/*.txt")
        all_files = glob.glob(pattern, recursive=True)
        all_files.sort()
        
        # Split by files (ensuring different patients/cases)
        n_train = int(len(all_files) * self.train_ratio)
        train_files = all_files[:n_train]
        test_files = all_files[n_train:]
        
        return train_files, test_files
    
    def get_model_config(self) -> dict:
        """Get model configuration dictionary"""
        return {
            'input_channels': self.input_channels,
            'hidden_channels': self.hidden_channels,
            'kernel_size': self.kernel_size,
            'patch_length': self.patch_length,
            'dropout_rate': self.dropout_rate
        }
    
    def get_training_config(self) -> dict:
        """Get training configuration dictionary"""
        return {
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate,
            'num_epochs': self.num_epochs,
            'device': self.device,
            'num_workers': self.num_workers,
            'pin_memory': self.pin_memory
        }
    
    def get_mask_config(self) -> dict:
        """Get mask configuration dictionary"""
        return {
            'mask_type': self.mask_type,
            'n_mask_points': self.n_mask_points,
            'block_size': self.block_size,
            'n_blocks': self.n_blocks
        }
    
    def get_loss_config(self) -> dict:
        """Get loss configuration dictionary"""
        return {
            'use_wavelet_loss': self.use_wavelet_loss,
            'use_amplitude_constraint': self.use_amplitude_constraint,
            'amplitude_scale_factor': self.amplitude_scale_factor,
            'wavelet_name': self.wavelet_name,
            'wavelet_levels': self.wavelet_levels,
            'loss_weights': self.loss_weights
        }
    
    def get_visualization_config(self) -> dict:
        """Get visualization configuration dictionary"""
        return {
            'signal_duration': self.vis_signal_duration,
            'show_all_channels': self.vis_show_all_channels,
            'show_train_samples': self.vis_show_train_samples,
            'update_frequency': self.vis_update_frequency,
            'enable_long_signal': self.vis_enable_long_signal,
            'long_signal_segments': self.vis_long_signal_segments,
            'show_signal_overview': self.vis_show_signal_overview
        }
    
    def summary(self) -> str:
        """Get configuration summary"""
        return f"""
Noise 2 Sim Configuration:
========================
Data:
- Data directory: {self.data_dir}
- Sampling rate: {self.sampling_rate} Hz
- Signal channels: {self.n_signal_channels}
- Reference channels: {self.n_reference_channels}

Patch Processing:
- Patch length: {self.patch_length} samples ({self.patch_length/self.sampling_rate:.1f}s)
- Overlap step: {self.overlap_step} samples
- Mask points: {self.n_mask_points}
- Mask type: {self.mask_type}

Training:
- Batch size: {self.batch_size}
- Learning rate: {self.learning_rate}
- Epochs: {self.num_epochs}
- Device: {self.device}
- Train ratio: {self.train_ratio}
- Single model: {self.single_model}
- Parallel groups: {self.parallel_groups}

Model:
- Input channels: {self.input_channels}
- Hidden channels: {self.hidden_channels}
- Kernel size: {self.kernel_size}
- Dropout rate: {self.dropout_rate}

Loss Functions:
- Wavelet loss: {self.use_wavelet_loss}
- Amplitude constraint: {self.use_amplitude_constraint}
- Loss weights: {self.loss_weights}

Data Loading:
- Lazy loading: {self.lazy_loading}
- Cache size: {self.cache_size}
- Memory mapping: {self.use_memory_mapping}

Visualization:
- Signal duration: {self.vis_signal_duration}s
- Show all channels: {self.vis_show_all_channels}
- Show training samples: {self.vis_show_train_samples}
- Enable long signal: {self.vis_enable_long_signal}
- Long signal segments: {self.vis_long_signal_segments}
- Show signal overview: {self.vis_show_signal_overview}

Directories:
- Checkpoints: {self.checkpoint_dir}
- Logs: {self.log_dir}
        """