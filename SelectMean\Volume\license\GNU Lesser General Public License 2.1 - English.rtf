{\rtf1\ansi\ansicpg1252\uc1\deff0\stshfdbch0\stshfloch0\stshfhich0\stshfbi0\deflang1033\deflangfe1033{\fonttbl{\f0\froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}
{\f2\fmodern\fcharset0\fprq1{\*\panose 02070309020205020404}Courier New;}{\f36\froman\fcharset238\fprq2 Times New Roman CE;}{\f37\froman\fcharset204\fprq2 Times New Roman Cyr;}{\f39\froman\fcharset161\fprq2 Times New Roman Greek;}
{\f40\froman\fcharset162\fprq2 Times New Roman Tur;}{\f41\froman\fcharset177\fprq2 Times New Roman (Hebrew);}{\f42\froman\fcharset178\fprq2 Times New Roman (Arabic);}{\f43\froman\fcharset186\fprq2 Times New Roman Baltic;}
{\f44\froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\f56\fmodern\fcharset238\fprq1 Courier New CE;}{\f57\fmodern\fcharset204\fprq1 Courier New Cyr;}{\f59\fmodern\fcharset161\fprq1 Courier New Greek;}
{\f60\fmodern\fcharset162\fprq1 Courier New Tur;}{\f61\fmodern\fcharset177\fprq1 Courier New (Hebrew);}{\f62\fmodern\fcharset178\fprq1 Courier New (Arabic);}{\f63\fmodern\fcharset186\fprq1 Courier New Baltic;}
{\f64\fmodern\fcharset163\fprq1 Courier New (Vietnamese);}}{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green255\blue255;\red0\green255\blue0;\red255\green0\blue255;\red255\green0\blue0;\red255\green255\blue0;\red255\green255\blue255;
\red0\green0\blue128;\red0\green128\blue128;\red0\green128\blue0;\red128\green0\blue128;\red128\green0\blue0;\red128\green128\blue0;\red128\green128\blue128;\red192\green192\blue192;}{\stylesheet{
\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \fs24\lang1033\langfe1033\cgrid\langnp1033\langfenp1033 \snext0 Normal;}{\*\cs10 \additive \ssemihidden Default Paragraph Font;}{\*
\ts11\tsrowd\trftsWidthB3\trpaddl108\trpaddr108\trpaddfl3\trpaddft3\trpaddfb3\trpaddfr3\trcbpat1\trcfpat1\tscellwidthfts0\tsvertalt\tsbrdrt\tsbrdrl\tsbrdrb\tsbrdrr\tsbrdrdgl\tsbrdrdgr\tsbrdrh\tsbrdrv 
\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \fs20\lang1024\langfe1024\cgrid\langnp1024\langfenp1024 \snext11 \ssemihidden Normal Table;}{\s15\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 
\f2\fs20\lang1033\langfe1033\cgrid\langnp1033\langfenp1033 \sbasedon0 \snext15 \styrsid472287 Plain Text;}}{\*\rsidtbl \rsid85446\rsid472287\rsid3428490\rsid5640592\rsid11612617}{\*\generator Microsoft Word 10.0.6612;}{\info{\author Justin Britten}
{\operator Justin Britten}{\creatim\yr2005\mo5\dy17\hr13\min48}{\revtim\yr2005\mo5\dy17\hr13\min48}{\version2}{\edmins1}{\nofpages11}{\nofwords3708}{\nofchars21139}{\*\company National Instruments}{\nofcharsws24798}{\vern16389}}\margl1319\margr1319 
\widowctrl\ftnbj\aenddoc\noxlattoyen\expshrtn\noultrlspc\dntblnsbdb\nospaceforul\formshade\horzdoc\dgmargin\dghspace180\dgvspace180\dghorigin1319\dgvorigin1440\dghshow1\dgvshow1
\jexpand\viewkind1\viewscale100\pgbrdrhead\pgbrdrfoot\splytwnine\ftnlytwnine\htmautsp\nolnhtadjtbl\useltbaln\alntblind\lytcalctblwd\lyttblrtgr\lnbrkrule\nobrkwrptbl\snaptogridincell\allowfieldendsel\wrppunct\asianbrkrule\rsidroot11612617 \fet0\sectd 
\linex0\endnhere\sectlinegrid360\sectdefaultcl\sectrsid472287\sftnbj {\*\pnseclvl1\pnucrm\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl2\pnucltr\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl3\pndec\pnstart1\pnindent720\pnhang {\pntxta .}}
{\*\pnseclvl4\pnlcltr\pnstart1\pnindent720\pnhang {\pntxta )}}{\*\pnseclvl5\pndec\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl6\pnlcltr\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl7\pnlcrm\pnstart1\pnindent720\pnhang 
{\pntxtb (}{\pntxta )}}{\*\pnseclvl8\pnlcltr\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl9\pnlcrm\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}\pard\plain 
\s15\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid472287 \f2\fs20\lang1033\langfe1033\cgrid\langnp1033\langfenp1033 {\insrsid5640592\charrsid472287 \tab \tab   GNU LESSER GENERAL PUBLIC LICENSE
\par \tab \tab        Version 2.1, February 1999
\par 
\par  Copyright (C) 1991, 1999 Free Software Foundation, Inc.
\par      51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
\par  Everyone is permitted to copy and distribute verbatim copies
\par  of this license document, but changing it is not allowed.
\par 
\par [This is the first released version of the Lesser GPL.  It also counts
\par  as the successor of the GNU Library Public License, version 2, hence
\par  the version number 2.1.]
\par 
\par \tab \tab \tab     Preamble
\par 
\par   The licenses for most software are designed to take away your
\par freedom to share and change it.  By contrast, the GNU General Public
\par Licenses are intended to guarantee your freedom to share and change
\par free software--to make sure the software is free for all its users.
\par 
\par   This license, the Lesser General Public License, applies to some
\par specially designated software packages--typically libraries--of the
\par Free Software Foundation and other authors who decide to use it.  You
\par can use it too, but we suggest you first think carefully about whether
\par this license or the ordinary General Public License is the better
\par strategy to use in any particular case, based on the explanations below.
\par 
\par   When we speak of free software, we are referring to freedom of use,
\par not price.  Our General Public Licenses are designed to make sure that
\par you have the freedom to distribute copies of free software (and charge
\par for this service if you wish); that you receive source code or can get
\par it if you want it; that you can change the software and use pieces of
\par it in new free programs; and that you are informed that you can do
\par these things.
\par 
\par   To protect your rights, we need to make restrictions that forbid
\par distributors to deny you these rights or to ask you to surrender these
\par rights.  These restrictions translate to certain responsibilities for
\par you if you distribute copies of the library or if you modify it.
\par 
\par   For example, if you distribute copies of the library, whether gratis
\par or for a fee, you must give the recipients all the rights that we gave
\par you.  You must make sure that they, too, receive or can get the source
\par code.  If you link other code with the library, you must provide
\par complete object files to the recipients, so that they can relink them
\par with the library after making changes to the library and recompiling
\par it.  And you must show them these terms so they know their rights.
\par 
\par   We protect your rights with a two-step method: (1) we copyright the
\par library, and (2) we offer you this license, which gives you legal
\par permission to copy, distribute and/or modify the library.
\par 
\par   To protect each distributor, we want to make it very clear that
\par there is no warranty for the free library.  Also, if the library is
\par modified by someone else and passed on, the recipients should know
\par that what they have is not the original version, so that the original
\par author's reputation will not be affected by problems that might be
\par introduced by others.
\par \page 
\par   Finally, software patents pose a constant threat to the existence of
\par any free program.  We wish to make sure that a company cannot
\par effectively restrict the users of a free program by obtaining a
\par restrictive license from a patent holder.  Therefore, we insist that
\par any patent license obtained for a version of the library must be
\par consistent with the full freedom of use specified in this license.
\par 
\par   Most GNU software, including some libraries, is covered by the
\par ordinary GNU General Public License.  This license, the GNU Lesser
\par General Public License, applies to certain designated libraries, and
\par is quite different from the ordinary General Public License.  We use
\par this license for certain libraries in order to permit linking those
\par libraries into non-free programs.
\par 
\par   When a program is linked with a library, whether statically or using
\par a shared library, the combination of the two is legally speaking a
\par combined work, a derivative of the original library.  The ordinary
\par General Public License therefore permits such linking only if the
\par entire combination fits its criteria of freedom.  The Lesser General
\par Public License permits more lax criteria for linking other code with
\par the library.
\par 
\par   We call this license the "Lesser" General Public License because it
\par does Less to protect the user's freedom than the ordinary General
\par Public License.  It also provides other free software developers Less
\par of an advantage over competing non-free programs.  These disadvantages
\par are the reason we use the ordinary General Public License for many
\par libraries.  However, the Lesser license provides advantages in certain
\par special circumstances.
\par 
\par   For example, on rare occasions, there may be a special need to
\par encourage the widest possible use of a certain library, so that it becomes
\par a de-facto standard.  To achieve this, non-free programs must be
\par allowed to use the library.  A more frequent case is that a free
\par library does the same job as widely used non-free libraries.  In this
\par case, there is little to gain by limiting the free library to free
\par software only, so we use the Lesser General Public License.
\par 
\par   In other cases, permission to use a particular library in non-free
\par programs enables a greater number of people to use a large body of
\par free software.  For example, permission to use the GNU C Library in
\par non-free programs enables many more people to use the whole GNU
\par operating system, as well as its variant, the GNU/Linux operating
\par system.
\par 
\par   Although the Lesser General Public License is Less protective of the
\par users' freedom, it does ensure that the user of a program that is
\par linked with the Library has the freedom and the wherewithal to run
\par that program using a modified version of the Library.
\par 
\par   The precise terms and conditions for copying, distribution and
\par modification follow.  Pay close attention to the difference between a
\par "work based on the library" and a "work that uses the library".  The
\par former contains code derived from the library, whereas the latter must
\par be combined with the library in order to run.
\par \page 
\par \tab \tab   GNU LESSER GENERAL PUBLIC LICENSE
\par    TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION
\par 
\par   0. This License Agreement applies to any software library or other
\par program which contains a notice placed by the copyright holder or
\par other authorized party saying it may be distributed under the terms of
\par this Lesser General Public License (also called "this License").
\par Each licensee is addressed as "you".
\par 
\par   A "library" means a collection of software functions and/or data
\par prepared so as to be conveniently linked with application programs
\par (which use some of those functions and data) to form executables.
\par 
\par   The "Library", below, refers to any such software library or work
\par which has been distributed under these terms.  A "work based on the
\par Library" means either the Library or any derivative work under
\par copyright law: that is to say, a work containing the Library or a
\par portion of it, either verbatim or with modifications and/or translated
\par straightforwardly into another language.  (Hereinafter, translation is
\par included without limitation in the term "modification".)
\par 
\par   "Source code" for a work means the preferred form of the work for
\par making modifications to it.  For a library, complete source code means
\par all the source code for all modules it contains, plus any associated
\par interface definition files, plus the scripts used to control compilation
\par and installation of the library.
\par 
\par   Activities other than copying, distribution and modification are not
\par covered by this License; they are outside its scope.  The act of
\par running a program using the Library is not restricted, and output from
\par such a program is covered only if its contents constitute a work based
\par on the Library (independent of the use of the Library in a tool for
\par writing it).  Whether that is true depends on what the Library does
\par and what the program that uses the Library does.
\par   
\par   1. You may copy and distribute verbatim copies of the Library's
\par complete source code as you receive it, in any medium, provided that
\par you conspicuously and appropriately publish on each copy an
\par appropriate copyright notice and disclaimer of warranty; keep intact
\par all the notices that refer to this License and to the absence of any
\par warranty; and distribute a copy of this License along with the
\par Library.
\par 
\par   You may charge a fee for the physical act of transferring a copy,
\par and you may at your option offer warranty protection in exchange for a
\par fee.
\par \page 
\par   2. You may modify your copy or copies of the Library or any portion
\par of it, thus forming a work based on the Library, and copy and
\par distribute such modifications or work under the terms of Section 1
\par above, provided that you also meet all of these conditions:
\par 
\par     a) The modified work must itself be a software library.
\par 
\par     b) You must cause the files modified to carry prominent notices
\par     stating that you changed the files and the date of any change.
\par 
\par     c) You must cause the whole of the work to be licensed at no
\par     charge to all third parties under the terms of this License.
\par 
\par     d) If a facility in the modified Library refers to a function or a
\par     table of data to be supplied by an application program that uses
\par     the facility, other than as an argument passed when the facility
\par     is invoked, then you must make a good faith effort to ensure that,
\par     in the event an application does not supply such function or
\par     table, the facility still operates, and performs whatever part of
\par     its purpose remains meaningful.
\par 
\par     (For example, a function in a library to compute square roots has
\par     a purpose that is entirely well-defined independent of the
\par     application.  Therefore, Subsection 2d requires that any
\par     application-supplied function or table used by this function must
\par     be optional: if the application does not supply it, the square
\par     root function must still compute square roots.)
\par 
\par These requirements apply to the modified work as a whole.  If
\par identifiable sections of that work are not derived from the Library,
\par and can be reasonably considered independent and separate works in
\par themselves, then this License, and its terms, do not apply to those
\par sections when you distribute them as separate works.  But when you
\par distribute the same sections as part of a whole which is a work based
\par on the Library, the distribution of the whole must be on the terms of
\par this License, whose permissions for other licensees extend to the
\par entire whole, and thus to each and every part regardless of who wrote
\par it.
\par 
\par Thus, it is not the intent of this section to claim rights or contest
\par your rights to work written entirely by you; rather, the intent is to
\par exercise the right to control the distribution of derivative or
\par collective works based on the Library.
\par 
\par In addition, mere aggregation of another work not based on the Library
\par with the Library (or with a work based on the Library) on a volume of
\par a storage or distribution medium does not bring the other work under
\par the scope of this License.
\par 
\par   3. You may opt to apply the terms of the ordinary GNU General Public
\par License instead of this License to a given copy of the Library.  To do
\par this, you must alter all the notices that refer to this License, so
\par that they refer to the ordinary GNU General Public License, version 2,
\par instead of to this License.  (If a newer version than version 2 of the
\par ordinary GNU General Public License has appeared, then you can specify
\par that version instead if you wish.)  Do not make any other change in
\par these notices.
\par \page 
\par   Once this change is made in a given copy, it is irreversible for
\par that copy, so the ordinary GNU General Public License applies to all
\par subsequent copies and derivative works made from that copy.
\par 
\par   This option is useful when you wish to copy part of the code of
\par the Library into a program that is not a library.
\par 
\par   4. You may copy and distribute the Library (or a portion or
\par derivative of it, under Section 2) in object code or executable form
\par under the terms of Sections 1 and 2 above provided that you accompany
\par it with the complete corresponding machine-readable source code, which
\par must be distributed under the terms of Sections 1 and 2 above on a
\par medium customarily used for software interchange.
\par 
\par   If distribution of object code is made by offering access to copy
\par from a designated place, then offering equivalent access to copy the
\par source code from the same place satisfies the requirement to
\par distribute the source code, even though third parties are not
\par compelled to copy the source along with the object code.
\par 
\par   5. A program that contains no derivative of any portion of the
\par Library, but is designed to work with the Library by being compiled or
\par linked with it, is called a "work that uses the Library".  Such a
\par work, in isolation, is not a derivative work of the Library, and
\par therefore falls outside the scope of this License.
\par 
\par   However, linking a "work that uses the Library" with the Library
\par creates an executable that is a derivative of the Library (because it
\par contains portions of the Library), rather than a "work that uses the
\par library".  The executable is therefore covered by this License.
\par Section 6 states terms for distribution of such executables.
\par 
\par   When a "work that uses the Library" uses material from a header file
\par that is part of the Library, the object code for the work may be a
\par derivative work of the Library even though the source code is not.
\par Whether this is true is especially significant if the work can be
\par linked without the Library, or if the work is itself a library.  The
\par threshold for this to be true is not precisely defined by law.
\par 
\par   If such an object file uses only numerical parameters, data
\par structure layouts and accessors, and small macros and small inline
\par functions (ten lines or less in length), then the use of the object
\par file is unrestricted, regardless of whether it is legally a derivative
\par work.  (Executables containing this object code plus portions of the
\par Library will still fall under Section 6.)
\par 
\par   Otherwise, if the work is a derivative of the Library, you may
\par distribute the object code for the work under the terms of Section 6.
\par Any executables containing that work also fall under Section 6,
\par whether or not they are linked directly with the Library itself.
\par \page 
\par   6. As an exception to the Sections above, you may also combine or
\par link a "work that uses the Library" with the Library to produce a
\par work containing portions of the Library, and distribute that work
\par under terms of your choice, provided that the terms permit
\par modification of the work for the customer's own use and reverse
\par engineering for debugging such modifications.
\par 
\par   You must give prominent notice with each copy of the work that the
\par Library is used in it and that the Library and its use are covered by
\par this License.  You must supply a copy of this License.  If the work
\par during execution displays copyright notices, you must include the
\par copyright notice for the Library among them, as well as a reference
\par directing the user to the copy of this License.  Also, you must do one
\par of these things:
\par 
\par     a) Accompany the work with the complete corresponding
\par     machine-readable source code for the Library including whatever
\par     changes were used in the work (which must be distributed under
\par     Sections 1 and 2 above); and, if the work is an executable linked
\par     with the Library, with the complete machine-readable "work that
\par     uses the Library", as object code and/or source code, so that the
\par     user can modify the Library and then relink to produce a modified
\par     executable containing the modified Library.  (It is understood
\par     that the user who changes the contents of definitions files in the
\par     Library will not necessarily be able to recompile the application
\par     to use the modified definitions.)
\par 
\par     b) Use a suitable shared library mechanism for linking with the
\par     Library.  A suitable mechanism is one that (1) uses at run time a
\par     copy of the library already present on the user's computer system,
\par     rather than copying library functions into the executable, and (2)
\par     will operate properly with a modified version of the library, if
\par     the user installs one, as long as the modified version is
\par     interface-compatible with the version that the work was made with.
\par 
\par     c) Accompany the work with a written offer, valid for at
\par     least three years, to give the same user the materials
\par     specified in Subsection 6a, above, for a charge no more
\par     than the cost of performing this distribution.
\par 
\par     d) If distribution of the work is made by offering access to copy
\par     from a designated place, offer equivalent access to copy the above
\par     specified materials from the same place.
\par 
\par     e) Verify that the user has already received a copy of these
\par     materials or that you have already sent this user a copy.
\par 
\par   For an executable, the required form of the "work that uses the
\par Library" must include any data and utility programs needed for
\par reproducing the executable from it.  However, as a special exception,
\par the materials to be distributed need not include anything that is
\par normally distributed (in either source or binary form) with the major
\par components (compiler, kernel, and so on) of the operating system on
\par which the executable runs, unless that component itself accompanies
\par the executable.
\par 
\par   It may happen that this requirement contradicts the license
\par restrictions of other proprietary libraries that do not normally
\par accompany the operating system.  Such a contradiction means you cannot
\par use both them and the Library together in an executable that you
\par distribute.
\par \page 
\par   7. You may place library facilities that are a work based on the
\par Library side-by-side in a single library together with other library
\par facilities not covered by this License, and distribute such a combined
\par library, provided that the separate distribution of the work based on
\par the Library and of the other library facilities is otherwise
\par permitted, and provided that you do these two things:
\par 
\par     a) Accompany the combined library with a copy of the same work
\par     based on the Library, uncombined with any other library
\par     facilities.  This must be distributed under the terms of the
\par     Sections above.
\par 
\par     b) Give prominent notice with the combined library of the fact
\par     that part of it is a work based on the Library, and explaining
\par     where to find the accompanying uncombined form of the same work.
\par 
\par   8. You may not copy, modify, sublicense, link with, or distribute
\par the Library except as expressly provided under this License.  Any
\par attempt otherwise to copy, modify, sublicense, link with, or
\par distribute the Library is void, and will automatically terminate your
\par rights under this License.  However, parties who have received copies,
\par or rights, from you under this License will not have their licenses
\par terminated so long as such parties remain in full compliance.
\par 
\par   9. You are not required to accept this License, since you have not
\par signed it.  However, nothing else grants you permission to modify or
\par distribute the Library or its derivative works.  These actions are
\par prohibited by law if you do not accept this License.  Therefore, by
\par modifying or distributing the Library (or any work based on the
\par Library), you indicate your acceptance of this License to do so, and
\par all its terms and conditions for copying, distributing or modifying
\par the Library or works based on it.
\par 
\par   10. Each time you redistribute the Library (or any work based on the
\par Library), the recipient automatically receives a license from the
\par original licensor to copy, distribute, link with or modify the Library
\par subject to these terms and conditions.  You may not impose any further
\par restrictions on the recipients' exercise of the rights granted herein.
\par You are not responsible for enforcing compliance by third parties with
\par this License.
\par \page 
\par   11. If, as a consequence of a court judgment or allegation of patent
\par infringement or for any other reason (not limited to patent issues),
\par conditions are imposed on you (whether by court order, agreement or
\par otherwise) that contradict the conditions of this License, they do not
\par excuse you from the conditions of this License.  If you cannot
\par distribute so as to satisfy simultaneously your obligations under this
\par License and any other pertinent obligations, then as a consequence you
\par may not distribute the Library at all.  For example, if a patent
\par license would not permit royalty-free redistribution of the Library by
\par all those who receive copies directly or indirectly through you, then
\par the only way you could satisfy both it and this License would be to
\par refrain entirely from distribution of the Library.
\par 
\par If any portion of this section is held invalid or unenforceable under any
\par particular circumstance, the balance of the section is intended to apply,
\par and the section as a whole is intended to apply in other circumstances.
\par 
\par It is not the purpose of this section to induce you to infringe any
\par patents or other property right claims or to contest validity of any
\par such claims; this section has the sole purpose of protecting the
\par integrity of the free software distribution system which is
\par implemented by public license practices.  Many people have made
\par generous contributions to the wide range of software distributed
\par through that system in reliance on consistent application of that
\par system; it is up to the author/donor to decide if he or she is willing
\par to distribute software through any other system and a licensee cannot
\par impose that choice.
\par 
\par This section is intended to make thoroughly clear what is believed to
\par be a consequence of the rest of this License.
\par 
\par   12. If the distribution and/or use of the Library is restricted in
\par certain countries either by patents or by copyrighted interfaces, the
\par original copyright holder who places the Library under this License may add
\par an explicit geographical distribution limitation excluding those countries,
\par so that distribution is permitted only in or among countries not thus
\par excluded.  In such case, this License incorporates the limitation as if
\par written in the body of this License.
\par 
\par   13. The Free Software Foundation may publish revised and/or new
\par versions of the Lesser General Public License from time to time.
\par Such new versions will be similar in spirit to the present version,
\par but may differ in detail to address new problems or concerns.
\par 
\par Each version is given a distinguishing version number.  If the Library
\par specifies a version number of this License which applies to it and
\par "any later version", you have the option of following the terms and
\par conditions either of that version or of any later version published by
\par the Free Software Foundation.  If the Library does not specify a
\par license version number, you may choose any version ever published by
\par the Free Software Foundation.
\par \page 
\par   14. If you wish to incorporate parts of the Library into other free
\par programs whose distribution conditions are incompatible with these,
\par write to the author to ask for permission.  For software which is
\par copyrighted by the Free Software Foundation, write to the Free
\par Software Foundation; we sometimes make exceptions for this.  Our
\par decision will be guided by the two goals of preserving the free status
\par of all derivatives of our free software and of promoting the sharing
\par and reuse of software generally.
\par 
\par \tab \tab \tab     NO WARRANTY
\par 
\par   15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO
\par WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW.
\par EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
\par OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY
\par KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE
\par IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
\par PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE
\par LIBRARY IS WITH YOU.  SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME
\par THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.
\par 
\par   16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
\par WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
\par AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU
\par FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR
\par CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE
\par LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING
\par RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A
\par FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF
\par SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
\par DAMAGES.
\par 
\par }\pard \s15\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid472287 {\insrsid5640592\charrsid472287 \tab \tab      END OF TERMS AND CONDITIONS
\par }}