<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="wangmeng@192.168.100.83:22 password (5)" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="quduangang@192.168.100.100:22 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="wangmeng@192.168.100.100:22">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="wangmeng@192.168.100.83:22 password">
        <serverdata>
          <mappings>
            <mapping deploy="/Denoise_61chan" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="wangmeng@192.168.100.83:22 password (2)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="wangmeng@192.168.100.83:22 password (3)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="wangmeng@192.168.100.83:22 password (4)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="wangmeng@192.168.100.83:22 password (5)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/projects/Denoise_61chan" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="wangmeng_dlA6000:83">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>