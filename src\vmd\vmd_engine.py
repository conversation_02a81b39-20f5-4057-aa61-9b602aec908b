"""
VMD处理引擎
Author: Assistant
Date: 2025-07-15
Description: 核心VMD处理引擎，支持并行处理和结果管理
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing
import os
from dataclasses import dataclass
from scipy import signal
from scipy.fft import fft, fftfreq
import matplotlib.pyplot as plt
import time

# 导入项目模块
from scripts.vmdpy import VMD
from config_manager import ConfigManager, FilterRule, ProcessingConfig, VMDConfig

@dataclass
class ProcessingResult:
    """处理结果数据类"""
    channel: int
    original_signal: np.ndarray
    vmd_modes: np.ndarray
    vmd_omega: np.ndarray
    reconstructed_signal: np.ndarray
    processing_time: float
    filter_rules: List[FilterRule]
    similarity_metrics: Dict[str, float] = None
    reference_channel: int = None
    reference_signal: np.ndarray = None

class VMDEngine:
    """VMD处理引擎"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self.results = {}
        
        # 创建必要目录
        self.config_manager.create_directories()
    
    def load_data(self, file_path: str) -> np.ndarray:
        """加载数据文件"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"数据文件不存在: {file_path}")
            
            # 尝试不同的加载方式
            try:
                data = np.loadtxt(file_path, encoding='utf-8')
            except ValueError:
                data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')
            
            self.logger.info(f"成功加载数据文件: {file_path}, 形状: {data.shape}")
            return data
            
        except Exception as e:
            self.logger.error(f"加载数据文件失败: {e}")
            raise
    
    def extract_signal_segment(self, data: np.ndarray, channel: int, 
                             processing_config: ProcessingConfig) -> np.ndarray:
        """提取信号段"""
        try:
            # 使用样本配置提取信号段
            start_idx = processing_config.start_sample
            end_idx = start_idx + processing_config.sample_length
            
            # 确保不超出数据范围
            if end_idx > data.shape[0]:
                end_idx = data.shape[0]
                self.logger.warning(f"信号段超出数据范围，已调整至: {end_idx}")
            
            # 提取信号段（注意：配置中的通道是1-based，数组是0-based）
            signal_segment = data[start_idx:end_idx, channel - 1]
            
            return signal_segment
            
        except Exception as e:
            self.logger.error(f"提取信号段失败 - 通道{channel}: {e}")
            raise
    
    def apply_vmd(self, signal: np.ndarray, vmd_config: VMDConfig) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """应用VMD分解"""
        try:
            start_time = time.time()
            
            # 执行VMD分解
            u, u_hat, omega = VMD(
                signal, 
                vmd_config.alpha, 
                vmd_config.tau, 
                vmd_config.K, 
                vmd_config.DC, 
                vmd_config.init, 
                vmd_config.tol
            )
            
            processing_time = time.time() - start_time
            self.logger.info(f"VMD分解完成，耗时: {processing_time:.2f}秒")
            
            return u, u_hat, omega
            
        except Exception as e:
            self.logger.error(f"VMD分解失败: {e}")
            raise
    
    def apply_filter(self, data: np.ndarray, fs: int, filter_rule: FilterRule) -> np.ndarray:
        """应用滤波器"""
        if filter_rule.filter_type == 'none':
            return data
        
        try:
            nyquist = 0.5 * fs
            order = 4
            
            if filter_rule.filter_type == 'low' and filter_rule.low_cutoff:
                if 0 < filter_rule.low_cutoff < nyquist:
                    normal_cutoff = filter_rule.low_cutoff / nyquist
                    b, a = signal.butter(order, normal_cutoff, btype='low', analog=False)
                    return signal.filtfilt(b, a, data)
                    
            elif filter_rule.filter_type == 'high' and filter_rule.high_cutoff:
                if 0 < filter_rule.high_cutoff < nyquist:
                    normal_cutoff = filter_rule.high_cutoff / nyquist
                    b, a = signal.butter(order, normal_cutoff, btype='high', analog=False)
                    return signal.filtfilt(b, a, data)
                    
            elif filter_rule.filter_type == 'band' and filter_rule.low_cutoff and filter_rule.high_cutoff:
                if 0 < filter_rule.low_cutoff < filter_rule.high_cutoff < nyquist:
                    low = filter_rule.low_cutoff / nyquist
                    high = filter_rule.high_cutoff / nyquist
                    b, a = signal.butter(order, [low, high], btype='band', analog=False)
                    return signal.filtfilt(b, a, data)
            
            # 如果滤波参数不合理，返回原始数据
            self.logger.warning(f"滤波参数不合理，返回原始数据: {filter_rule}")
            return data
            
        except Exception as e:
            self.logger.error(f"应用滤波器失败: {e}")
            return data
    
    def reconstruct_signal(self, vmd_modes: np.ndarray, vmd_omega: np.ndarray, 
                          fs: int, policy_name: Optional[str] = None) -> Tuple[np.ndarray, List[FilterRule]]:
        """重构信号"""
        try:
            # 获取滤波规则
            filter_rules = self.config_manager.apply_strategy_to_modes(vmd_omega, fs, policy_name)
            
            # 重构信号
            reconstructed = np.zeros_like(vmd_modes[0])
            
            for i, (mode, rule) in enumerate(zip(vmd_modes, filter_rules)):
                if rule.keep:
                    # 应用滤波
                    filtered_mode = self.apply_filter(mode, fs, rule)
                    reconstructed += filtered_mode
            
            return reconstructed, filter_rules
            
        except Exception as e:
            self.logger.error(f"重构信号失败: {e}")
            raise
    
    def calculate_similarity_metrics(self, original: np.ndarray, processed: np.ndarray, 
                                   reference: Optional[np.ndarray] = None) -> Dict[str, float]:
        """计算相似性指标"""
        try:
            metrics = {}
            
            # 与原始信号的相似性
            if len(original) == len(processed):
                # 相关系数
                correlation = np.corrcoef(original, processed)[0, 1]
                metrics['correlation_to_original'] = correlation
                
                # 均方误差
                mse = np.mean((original - processed) ** 2)
                metrics['mse_to_original'] = mse
                
                # 均方根误差
                rmse = np.sqrt(mse)
                metrics['rmse_to_original'] = rmse
                
                # 信噪比
                signal_power = np.mean(original ** 2)
                noise_power = np.mean((original - processed) ** 2)
                if noise_power > 0:
                    snr = 10 * np.log10(signal_power / noise_power)
                    metrics['snr_to_original'] = snr
            
            # 与参考信号的相似性
            if reference is not None and len(reference) == len(processed):
                correlation_ref = np.corrcoef(reference, processed)[0, 1]
                metrics['correlation_to_reference'] = correlation_ref
                
                mse_ref = np.mean((reference - processed) ** 2)
                metrics['mse_to_reference'] = mse_ref
                
                rmse_ref = np.sqrt(mse_ref)
                metrics['rmse_to_reference'] = rmse_ref
                
                signal_power_ref = np.mean(reference ** 2)
                noise_power_ref = np.mean((reference - processed) ** 2)
                if noise_power_ref > 0:
                    snr_ref = 10 * np.log10(signal_power_ref / noise_power_ref)
                    metrics['snr_to_reference'] = snr_ref
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算相似性指标失败: {e}")
            return {}
    
    def get_reference_channel_for_data_channel(self, channel: int) -> Optional[int]:
        """根据数据通道获取对应的参考通道"""
        try:
            # 直接使用硬编码的61通道数据结构映射
            # 避免依赖可能有问题的配置文件解析
            
            # 61通道数据结构：
            # 第1通道：idx
            # 第2-10通道：第1组 → 参考通道38
            # 第11-19通道：第2组 → 参考通道39  
            # 第20-28通道：第3组 → 参考通道40
            # 第29-37通道：第4组 → 参考通道41
            
            if 2 <= channel <= 10:
                return 38
            elif 11 <= channel <= 19:
                return 39
            elif 20 <= channel <= 28:
                return 40
            elif 29 <= channel <= 37:
                return 41
            else:
                # 对于其他通道（如idx通道或参考通道本身），返回None
                return None
            
        except Exception as e:
            self.logger.warning(f"获取通道{channel}的参考通道失败: {e}")
            return None
    
    def process_single_channel(self, data: np.ndarray, channel: int, 
                             reference_data: Optional[np.ndarray] = None) -> ProcessingResult:
        """处理单个通道"""
        try:
            start_time = time.time()
            
            # 获取配置
            processing_config = self.config_manager.get_processing_config()
            vmd_config = self.config_manager.get_vmd_config()
            
            # 提取信号段
            signal_segment = self.extract_signal_segment(data, channel, processing_config)
            
            # VMD分解
            vmd_modes, vmd_u_hat, vmd_omega = self.apply_vmd(signal_segment, vmd_config)
            
            # 重构信号
            reconstructed, filter_rules = self.reconstruct_signal(
                vmd_modes, vmd_omega, processing_config.sampling_rate
            )
            
            # 获取参考信号
            reference_channel = self.get_reference_channel_for_data_channel(channel)
            reference_signal = None
            
            if reference_channel and reference_channel <= data.shape[1]:
                try:
                    reference_signal = self.extract_signal_segment(data, reference_channel, processing_config)
                    self.logger.info(f"通道{channel}使用参考通道{reference_channel}")
                except Exception as e:
                    self.logger.warning(f"提取参考信号失败: {e}")
            
            # 计算相似性指标
            similarity_metrics = self.calculate_similarity_metrics(
                signal_segment, reconstructed, reference_signal
            )
            
            processing_time = time.time() - start_time
            
            # 创建结果对象
            result = ProcessingResult(
                channel=channel,
                original_signal=signal_segment,
                vmd_modes=vmd_modes,
                vmd_omega=vmd_omega,
                reconstructed_signal=reconstructed,
                processing_time=processing_time,
                filter_rules=filter_rules,
                similarity_metrics=similarity_metrics,
                reference_channel=reference_channel
            )
            
            # 添加参考信号到结果中
            if reference_signal is not None:
                result.reference_signal = reference_signal
            
            self.logger.info(f"通道{channel}处理完成，耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"处理通道{channel}失败: {e}")
            raise
    
    def process_channels_parallel(self, data: np.ndarray, channels: List[int]) -> Dict[int, ProcessingResult]:
        """并行处理多个通道"""
        try:
            parallel_config = self.config_manager.get_parallel_config()
            
            if not parallel_config.get('enabled', True):
                # 串行处理
                self.logger.info(f"开始串行处理{len(channels)}个通道")
                results = {}
                for i, channel in enumerate(channels):
                    self.logger.info(f"处理通道 {channel} ({i+1}/{len(channels)})")
                    results[channel] = self.process_single_channel(data, channel)
                return results
            
            # 判断是否值得并行处理
            if len(channels) <= 2:
                # 通道数较少时，串行处理更快（避免进程创建开销）
                self.logger.info(f"通道数较少({len(channels)})，使用串行处理")
                results = {}
                for channel in channels:
                    results[channel] = self.process_single_channel(data, channel)
                return results
            
            # 并行处理
            num_workers = parallel_config.get('num_workers', 0)
            if num_workers == 0:
                # 保守估计：CPU核心数的一半，最多不超过通道数
                num_workers = min(max(1, multiprocessing.cpu_count() // 2), len(channels))
            
            # 限制最大并行数，避免过多进程导致性能下降
            num_workers = min(num_workers, 8)  # 最多8个进程
            
            self.logger.info(f"开始并行处理{len(channels)}个通道，使用{num_workers}个进程")
            
            results = {}
            
            # 使用线程池而不是进程池来避免大量数据序列化开销
            from concurrent.futures import ThreadPoolExecutor
            
            # 对于VMD这种CPU密集型任务，线程池在Python中受GIL限制
            # 但由于VMD的主要计算在numpy/scipy的C扩展中进行，GIL会被释放
            # 因此线程池可能比进程池更高效（避免了数据序列化开销）
            
            try:
                with ThreadPoolExecutor(max_workers=num_workers) as executor:
                    # 提交任务
                    future_to_channel = {
                        executor.submit(self.process_single_channel, data, channel): channel
                        for channel in channels
                    }
                    
                    # 收集结果
                    completed_count = 0
                    for future in as_completed(future_to_channel):
                        channel = future_to_channel[future]
                        try:
                            result = future.result()
                            results[channel] = result
                            completed_count += 1
                            self.logger.info(f"通道{channel}处理完成 ({completed_count}/{len(channels)})")
                        except Exception as e:
                            self.logger.error(f"处理通道{channel}时发生错误: {e}")
                            raise
                
            except Exception as e:
                # 如果并行处理失败，回退到串行处理
                self.logger.warning(f"并行处理失败，回退到串行处理: {e}")
                results = {}
                for channel in channels:
                    results[channel] = self.process_single_channel(data, channel)
            
            self.logger.info(f"并行处理完成，共处理{len(results)}个通道")
            return results
            
        except Exception as e:
            self.logger.error(f"处理失败: {e}")
            raise
    
    def process_file(self, file_path: str) -> Dict[int, ProcessingResult]:
        """处理单个文件"""
        try:
            # 加载数据
            data = self.load_data(file_path)
            
            # 获取处理配置
            processing_config = self.config_manager.get_processing_config()
            
            # 处理指定通道
            results = self.process_channels_parallel(data, processing_config.channels)
            
            # 保存结果
            self.results[file_path] = results
            
            return results
            
        except Exception as e:
            self.logger.error(f"处理文件{file_path}失败: {e}")
            raise
    
    def save_results(self, file_path: str, results: Dict[int, ProcessingResult]):
        """保存处理结果"""
        try:
            output_config = self.config_manager.get_output_config()
            output_path = self.config_manager.get_path('../../output')
            
            # 创建输出文件名
            input_name = Path(file_path).stem
            
            # 保存为不同格式
            for format_type in output_config.get('formats', ['txt']):
                if format_type == 'txt':
                    self._save_as_txt(output_path, input_name, results)
                elif format_type == 'csv':
                    self._save_as_csv(output_path, input_name, results)
                elif format_type == 'mat':
                    self._save_as_mat(output_path, input_name, results)
            
            self.logger.info(f"结果已保存至: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
            raise
    
    def _save_as_txt(self, output_path: Path, input_name: str, results: Dict[int, ProcessingResult]):
        """保存为txt格式"""
        try:
            if not results:
                self.logger.warning("没有结果数据可保存")
                return
            
            # 获取实际的信号长度（使用第一个结果的长度）
            first_result = next(iter(results.values()))
            actual_length = len(first_result.reconstructed_signal)
            
            # 创建输出数组，第一列为idx
            output_data = np.zeros((actual_length, len(results) + 1))
            output_data[:, 0] = np.arange(actual_length)  # idx列
            
            # 填充处理后的数据
            for i, (channel, result) in enumerate(sorted(results.items())):
                if len(result.reconstructed_signal) == actual_length:
                    output_data[:, i + 1] = result.reconstructed_signal
                else:
                    self.logger.warning(f"通道{channel}信号长度不匹配，跳过保存")
                    continue
            
            # 保存文件
            output_file = output_path / f"{input_name}_processed.txt"
            np.savetxt(output_file, output_data, fmt='%.6f')
            
            self.logger.info(f"TXT格式结果已保存: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存TXT格式失败: {e}")
            raise
    
    def _save_as_csv(self, output_path: Path, input_name: str, results: Dict[int, ProcessingResult]):
        """保存为CSV格式"""
        try:
            # 构造DataFrame
            data_dict = {'idx': np.arange(len(next(iter(results.values())).reconstructed_signal))}
            
            for channel, result in sorted(results.items()):
                data_dict[f'channel_{channel}'] = result.reconstructed_signal
            
            df = pd.DataFrame(data_dict)
            
            # 保存文件
            output_file = output_path / f"{input_name}_processed.csv"
            df.to_csv(output_file, index=False)
            
            self.logger.info(f"CSV格式结果已保存: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存CSV格式失败: {e}")
            raise
    
    def _save_as_mat(self, output_path: Path, input_name: str, results: Dict[int, ProcessingResult]):
        """保存为MAT格式"""
        try:
            from scipy.io import savemat
            
            # 构造数据字典
            mat_data = {}
            for channel, result in results.items():
                mat_data[f'channel_{channel}'] = result.reconstructed_signal
                mat_data[f'channel_{channel}_original'] = result.original_signal
                mat_data[f'channel_{channel}_modes'] = result.vmd_modes
            
            # 保存文件
            output_file = output_path / f"{input_name}_processed.mat"
            savemat(output_file, mat_data)
            
            self.logger.info(f"MAT格式结果已保存: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存MAT格式失败: {e}")
            raise