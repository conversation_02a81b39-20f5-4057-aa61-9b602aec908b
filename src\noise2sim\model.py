"""
1D U-Net model for Noise2Sim MCG denoising
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class Conv1dBlock(nn.Module):
    """
    1D Convolution block with normalization and activation
    """
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 stride: int = 1, padding: int = 1, dropout_rate: float = 0.1):
        super().__init__()
        
        self.conv = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding)
        self.norm = nn.BatchNorm1d(out_channels)
        self.activation = nn.ReLU(inplace=True)
        self.dropout = nn.Dropout1d(dropout_rate)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.conv(x)
        x = self.norm(x)
        x = self.activation(x)
        x = self.dropout(x)
        return x

class DoubleConv1d(nn.Modu<PERSON>):
    """
    Two consecutive 1D convolution blocks
    """
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 dropout_rate: float = 0.1):
        super().__init__()
        
        self.conv1 = Conv1dBlock(in_channels, out_channels, kernel_size, dropout_rate=dropout_rate)
        self.conv2 = Conv1dBlock(out_channels, out_channels, kernel_size, dropout_rate=dropout_rate)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.conv1(x)
        x = self.conv2(x)
        return x

class Down1d(nn.Module):
    """
    Downsampling block with max pooling and double conv
    """
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 pool_size: int = 2, dropout_rate: float = 0.1):
        super().__init__()
        
        self.pool = nn.MaxPool1d(pool_size)
        self.conv = DoubleConv1d(in_channels, out_channels, kernel_size, dropout_rate)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.pool(x)
        x = self.conv(x)
        return x

class Up1d(nn.Module):
    """
    Upsampling block with transpose convolution and double conv
    """
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 stride: int = 2, dropout_rate: float = 0.1):
        super().__init__()
        
        self.up = nn.ConvTranspose1d(in_channels, in_channels // 2, kernel_size=stride, stride=stride)
        self.conv = DoubleConv1d(in_channels, out_channels, kernel_size, dropout_rate)
    
    def forward(self, x1: torch.Tensor, x2: torch.Tensor) -> torch.Tensor:
        x1 = self.up(x1)
        
        # Ensure same size for concatenation
        diff = x2.size()[2] - x1.size()[2]
        if diff > 0:
            x1 = F.pad(x1, [diff // 2, diff - diff // 2])
        elif diff < 0:
            x2 = F.pad(x2, [-diff // 2, -diff - (-diff // 2)])
        
        x = torch.cat([x2, x1], dim=1)
        x = self.conv(x)
        return x

class UNet1D(nn.Module):
    """
    1D U-Net for MCG signal denoising
    
    Architecture:
    - Input: (batch_size, 9, patch_length)
    - Output: (batch_size, 9, patch_length)
    - Uses skip connections for better gradient flow
    - Designed for Noise2Sim blind spot training
    """
    
    def __init__(self, 
                 input_channels: int = 9,
                 hidden_channels: List[int] = [16, 32, 64, 128, 256],
                 kernel_size: int = 3,
                 dropout_rate: float = 0.1,
                 patch_length: int = 1024):
        super().__init__()
        
        self.input_channels = input_channels
        self.hidden_channels = hidden_channels
        self.kernel_size = kernel_size
        self.dropout_rate = dropout_rate
        self.patch_length = patch_length
        
        # Initial convolution
        self.inc = DoubleConv1d(input_channels, hidden_channels[0], kernel_size, dropout_rate)
        
        # Encoder (downsampling path)
        self.down_layers = nn.ModuleList()
        for i in range(len(hidden_channels) - 1):
            self.down_layers.append(
                Down1d(hidden_channels[i], hidden_channels[i + 1], kernel_size, dropout_rate=dropout_rate)
            )
        
        # Decoder (upsampling path)
        self.up_layers = nn.ModuleList()
        for i in range(len(hidden_channels) - 1, 0, -1):
            self.up_layers.append(
                Up1d(hidden_channels[i], hidden_channels[i - 1], kernel_size, dropout_rate=dropout_rate)
            )
        
        # Final convolution
        self.outc = nn.Conv1d(hidden_channels[0], input_channels, kernel_size=1)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.ConvTranspose1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass
        
        Args:
            x: Input tensor (batch_size, 9, patch_length)
            
        Returns:
            output: Denoised tensor (batch_size, 9, patch_length)
        """
        # Store skip connections
        skip_connections = []
        
        # Initial convolution
        x = self.inc(x)
        skip_connections.append(x)
        
        # Encoder path
        for down_layer in self.down_layers:
            x = down_layer(x)
            skip_connections.append(x)
        
        # Remove the last skip connection (it's the bottleneck)
        skip_connections = skip_connections[:-1]
        
        # Decoder path
        for i, up_layer in enumerate(self.up_layers):
            skip_connection = skip_connections[-(i + 1)]
            x = up_layer(x, skip_connection)
        
        # Final convolution
        x = self.outc(x)
        
        return x
    
    def get_model_info(self) -> dict:
        """Get model information"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'input_channels': self.input_channels,
            'hidden_channels': self.hidden_channels,
            'kernel_size': self.kernel_size,
            'dropout_rate': self.dropout_rate,
            'patch_length': self.patch_length
        }

class Noise2SimLoss(nn.Module):
    """
    Loss function for Noise2Sim training
    Only computes MSE loss on masked positions
    """
    
    def __init__(self, reduction: str = 'mean'):
        super().__init__()
        self.reduction = reduction
        self.mse_loss = nn.MSELoss(reduction='none')
    
    def forward(self, 
                predictions: torch.Tensor,
                targets: torch.Tensor,
                mask_indices: torch.Tensor) -> torch.Tensor:
        """
        Compute loss only on masked positions
        
        Args:
            predictions: Model predictions (batch_size, 9, patch_length)
            targets: Target values at masked positions (batch_size, 9, n_mask_points)
            mask_indices: Mask indices (batch_size, n_mask_points)
            
        Returns:
            loss: Computed loss
        """
        batch_size, n_channels, _ = predictions.shape
        n_mask_points = mask_indices.shape[1]
        
        # Extract predictions at masked positions
        pred_at_mask = torch.zeros(batch_size, n_channels, n_mask_points, device=predictions.device)
        
        for b in range(batch_size):
            for c in range(n_channels):
                pred_at_mask[b, c, :] = predictions[b, c, mask_indices[b]]
        
        # Compute MSE loss
        loss = self.mse_loss(pred_at_mask, targets)
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss

def create_model(config) -> Tuple[UNet1D, 'EnhancedNoise2SimLoss']:
    """
    Create model and loss function from config
    
    Args:
        config: Configuration object
        
    Returns:
        model: UNet1D model
        loss_fn: Enhanced Noise2SimLoss function
    """
    model_config = config.get_model_config()
    
    model = UNet1D(
        input_channels=model_config['input_channels'],
        hidden_channels=model_config['hidden_channels'],
        kernel_size=model_config['kernel_size'],
        dropout_rate=model_config['dropout_rate'],
        patch_length=model_config['patch_length']
    )
    
    # Use enhanced loss function if any advanced features are enabled
    loss_config = config.get_loss_config()
    use_enhanced = any([
        loss_config.get('use_wavelet_loss', False),
        loss_config.get('use_amplitude_constraint', False),
        loss_config.get('use_frequency_loss', False),
        loss_config.get('use_r_wave_preservation', False)
    ])
    
    if use_enhanced:
        from .loss_functions import create_enhanced_loss
        loss_fn = create_enhanced_loss(config)
    else:
        loss_fn = Noise2SimLoss(reduction='mean')
    
    return model, loss_fn

def test_model(patch_length: int = 1024, batch_size: int = 4):
    """
    Test model functionality
    
    Args:
        patch_length: Length of input patches
        batch_size: Batch size for testing
    """
    import numpy as np
    
    print(f"Testing UNet1D with patch_length={patch_length}, batch_size={batch_size}")
    
    # Create model
    model = UNet1D(
        input_channels=9,
        hidden_channels=[16, 32, 64, 128, 256],
        kernel_size=3,
        dropout_rate=0.1,
        patch_length=patch_length
    )
    
    # Create dummy input
    x = torch.randn(batch_size, 9, patch_length)
    
    # Test forward pass
    model.eval()
    with torch.no_grad():
        output = model(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    
    # Test loss function
    loss_fn = Noise2SimLoss()
    
    # Create dummy targets and mask indices
    n_mask_points = 64
    targets = torch.randn(batch_size, 9, n_mask_points)
    mask_indices = torch.randint(0, patch_length, (batch_size, n_mask_points))
    
    # Compute loss
    loss = loss_fn(output, targets, mask_indices)
    print(f"Loss: {loss.item():.6f}")
    
    # Model info
    model_info = model.get_model_info()
    print(f"Model parameters: {model_info['total_parameters']:,}")
    print(f"Trainable parameters: {model_info['trainable_parameters']:,}")
    
    return model, loss_fn

if __name__ == "__main__":
    # Test the model
    print("Testing UNet1D Model for Noise2Sim")
    print("=" * 50)
    
    # Test with different patch lengths
    for patch_length in [1024, 2048]:
        print(f"\nTesting with patch_length={patch_length}")
        model, loss_fn = test_model(patch_length=patch_length)
        print("-" * 30)