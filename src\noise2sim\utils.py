"""
Utility functions for Noise 2 Sim implementation
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional, Dict
import logging
from scipy.signal import welch
import os
from pathlib import Path

# Import from existing data loader
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'wavelet_denoising'))
from data_loader import load_data, extract_channels

logger = logging.getLogger(__name__)

def set_seed(seed: int = 42):
    """Set random seeds for reproducibility"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def load_mcg_data(file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """
    Load MCG data from txt file and extract signal channels
    
    Args:
        file_path: Path to data file
        
    Returns:
        signal_channels: Shape (samples, 36)
        reference_channels: Shape (samples, 4)
    """
    try:
        data = load_data(file_path)
        if data is None:
            raise ValueError(f"Failed to load data from {file_path}")
        
        idx, signal_channels, reference_channels = extract_channels(data)
        return signal_channels, reference_channels
    
    except Exception as e:
        logger.error(f"Error loading MCG data from {file_path}: {e}")
        raise

def create_patches(signal: np.ndarray, patch_length: int, overlap_step: int) -> np.ndarray:
    """
    Create overlapping patches from signal
    
    Args:
        signal: Input signal (samples, channels)
        patch_length: Length of each patch
        overlap_step: Step size between patches
        
    Returns:
        patches: Shape (n_patches, channels, patch_length)
    """
    n_samples, n_channels = signal.shape
    
    # Calculate number of patches
    n_patches = (n_samples - patch_length) // overlap_step + 1
    
    patches = np.zeros((n_patches, n_channels, patch_length))
    
    for i in range(n_patches):
        start_idx = i * overlap_step
        end_idx = start_idx + patch_length
        
        if end_idx <= n_samples:
            patches[i] = signal[start_idx:end_idx].T
    
    return patches

def group_channels(signal: np.ndarray, channel_groups: Dict[int, List[int]]) -> List[np.ndarray]:
    """
    Group 36 channels into 4 groups of 9 channels each
    
    Args:
        signal: Input signal (samples, 36) or (n_patches, 36, patch_length)
        channel_groups: Dictionary mapping group index to channel indices
        
    Returns:
        grouped_signals: List of 4 arrays, each with shape (..., 9, ...)
    """
    grouped_signals = []
    
    for group_idx in sorted(channel_groups.keys()):
        channel_indices = channel_groups[group_idx]
        
        if len(signal.shape) == 2:  # (samples, channels)
            group_signal = signal[:, channel_indices]
        elif len(signal.shape) == 3:  # (n_patches, channels, patch_length)
            group_signal = signal[:, channel_indices, :]
        else:
            raise ValueError(f"Unsupported signal shape: {signal.shape}")
        
        grouped_signals.append(group_signal)
    
    return grouped_signals

def reconstruct_from_patches(patches: np.ndarray, original_length: int, 
                           overlap_step: int, patch_length: int) -> np.ndarray:
    """
    Reconstruct signal from overlapping patches using weighted averaging
    
    Args:
        patches: Shape (n_patches, channels, patch_length)
        original_length: Original signal length
        overlap_step: Step size used for patch creation
        patch_length: Length of each patch
        
    Returns:
        reconstructed: Shape (original_length, channels)
    """
    n_patches, n_channels, _ = patches.shape
    
    # Initialize output arrays
    reconstructed = np.zeros((original_length, n_channels))
    weights = np.zeros((original_length, n_channels))
    
    # Triangular weighting function
    weight_func = np.ones(patch_length)
    # Create triangular weights for overlap regions
    if overlap_step < patch_length:
        overlap_samples = patch_length - overlap_step
        weight_func[:overlap_samples//2] = np.linspace(0, 1, overlap_samples//2)
        weight_func[-overlap_samples//2:] = np.linspace(1, 0, overlap_samples//2)
    
    # Reconstruct with weighted averaging
    for i in range(n_patches):
        start_idx = i * overlap_step
        end_idx = start_idx + patch_length
        
        if end_idx <= original_length:
            reconstructed[start_idx:end_idx] += patches[i].T * weight_func[:, np.newaxis]
            weights[start_idx:end_idx] += weight_func[:, np.newaxis]
    
    # Normalize by weights
    weights[weights == 0] = 1  # Avoid division by zero
    reconstructed = reconstructed / weights
    
    return reconstructed

def create_mask_indices(patch_length: int, n_mask_points: int, 
                       mask_type: str = 'point', block_size: int = 8, 
                       n_blocks: int = 16) -> np.ndarray:
    """
    Create mask indices for blind spot training
    
    Args:
        patch_length: Length of each patch
        n_mask_points: Number of points to mask
        mask_type: Type of mask ('point', 'block', 'mixed')
        block_size: Size of continuous mask blocks
        n_blocks: Number of mask blocks
        
    Returns:
        mask_indices: Array of indices to mask
    """
    if mask_type == 'point':
        # Original random point masking
        mask_indices = np.random.choice(patch_length, n_mask_points, replace=False)
        return np.sort(mask_indices)
    
    elif mask_type == 'block':
        # Continuous block masking
        return create_block_mask_indices(patch_length, n_blocks, block_size)
    
    elif mask_type == 'mixed':
        # Mixed point and block masking
        half_points = n_mask_points // 2
        
        # Half random points
        point_indices = np.random.choice(patch_length, half_points, replace=False)
        
        # Half block indices
        n_blocks_mixed = max(1, (n_mask_points - half_points) // block_size)
        block_indices = create_block_mask_indices(patch_length, n_blocks_mixed, block_size)
        
        # Combine and ensure no duplicates
        all_indices = np.concatenate([point_indices, block_indices])
        unique_indices = np.unique(all_indices)
        
        # If we have too many, randomly select n_mask_points
        if len(unique_indices) > n_mask_points:
            unique_indices = np.random.choice(unique_indices, n_mask_points, replace=False)
        
        return np.sort(unique_indices)
    
    else:
        raise ValueError(f"Unknown mask_type: {mask_type}")

def create_block_mask_indices(patch_length: int, n_blocks: int, block_size: int) -> np.ndarray:
    """
    Create continuous block mask indices
    
    Args:
        patch_length: Length of each patch
        n_blocks: Number of blocks to create
        block_size: Size of each block
        
    Returns:
        mask_indices: Array of block mask indices
    """
    # Ensure blocks don't exceed patch length
    max_start = patch_length - block_size
    if max_start < 0:
        raise ValueError(f"Block size {block_size} is larger than patch length {patch_length}")
    
    # Generate random start positions for blocks
    if n_blocks > max_start + 1:
        # If too many blocks requested, reduce the number
        n_blocks = max_start + 1
    
    block_starts = np.random.choice(max_start + 1, n_blocks, replace=False)
    
    # Create mask indices for all blocks
    mask_indices = []
    for start in block_starts:
        block_indices = np.arange(start, start + block_size)
        mask_indices.extend(block_indices)
    
    return np.sort(np.array(mask_indices))

def apply_mask(patch: np.ndarray, mask_indices: np.ndarray) -> np.ndarray:
    """
    Apply mask to patch by setting masked values to zero
    
    Args:
        patch: Shape (channels, patch_length)
        mask_indices: Indices to mask
        
    Returns:
        masked_patch: Patch with masked values set to zero
    """
    masked_patch = patch.copy()
    masked_patch[:, mask_indices] = 0
    return masked_patch

def compute_psd(signal: np.ndarray, sampling_rate: int, nperseg: int = 1024) -> Tuple[np.ndarray, np.ndarray]:
    """
    Compute Power Spectral Density
    
    Args:
        signal: Input signal (samples,) or (samples, channels)
        sampling_rate: Sampling rate in Hz
        nperseg: Length of each segment for PSD computation
        
    Returns:
        freqs: Frequency array
        psd: Power spectral density
    """
    if len(signal.shape) == 1:
        freqs, psd = welch(signal, sampling_rate, nperseg=nperseg)
    else:
        freqs, psd = welch(signal[:, 0], sampling_rate, nperseg=nperseg)
    
    return freqs, psd

def plot_signal_comparison(original: np.ndarray, denoised: np.ndarray, 
                         sampling_rate: int = 1000, channel_idx: int = 0,
                         time_range: Optional[Tuple[float, float]] = None,
                         save_path: Optional[str] = None):
    """
    Plot comparison between original and denoised signals
    
    Args:
        original: Original signal
        denoised: Denoised signal
        sampling_rate: Sampling rate
        channel_idx: Channel index to plot
        time_range: Time range to plot (start, end) in seconds
        save_path: Path to save the plot
    """
    # Create time axis
    time_axis = np.arange(len(original)) / sampling_rate
    
    # Apply time range filter if specified
    if time_range:
        start_idx = int(time_range[0] * sampling_rate)
        end_idx = int(time_range[1] * sampling_rate)
        time_axis = time_axis[start_idx:end_idx]
        original = original[start_idx:end_idx]
        denoised = denoised[start_idx:end_idx]
    
    # Select channel
    if len(original.shape) > 1:
        original = original[:, channel_idx]
        denoised = denoised[:, channel_idx]
    
    fig, axes = plt.subplots(3, 1, figsize=(12, 10))
    
    # Original signal
    axes[0].plot(time_axis, original, 'b-', alpha=0.7, label='Original')
    axes[0].set_title(f'Original Signal (Channel {channel_idx + 1})')
    axes[0].set_ylabel('Amplitude')
    axes[0].grid(True)
    axes[0].legend()
    
    # Denoised signal
    axes[1].plot(time_axis, denoised, 'r-', alpha=0.7, label='Denoised')
    axes[1].set_title(f'Denoised Signal (Channel {channel_idx + 1})')
    axes[1].set_ylabel('Amplitude')
    axes[1].grid(True)
    axes[1].legend()
    
    # Residual
    residual = original - denoised
    axes[2].plot(time_axis, residual, 'g-', alpha=0.7, label='Residual')
    axes[2].set_title('Residual (Original - Denoised)')
    axes[2].set_xlabel('Time (s)')
    axes[2].set_ylabel('Amplitude')
    axes[2].grid(True)
    axes[2].legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Signal comparison saved to {save_path}")
    
    plt.show()

def plot_psd_comparison(original: np.ndarray, denoised: np.ndarray,
                       sampling_rate: int = 1000, channel_idx: int = 0,
                       save_path: Optional[str] = None):
    """
    Plot PSD comparison between original and denoised signals
    
    Args:
        original: Original signal
        denoised: Denoised signal
        sampling_rate: Sampling rate
        channel_idx: Channel index to analyze
        save_path: Path to save the plot
    """
    # Select channel
    if len(original.shape) > 1:
        original = original[:, channel_idx]
        denoised = denoised[:, channel_idx]
    
    # Compute PSDs
    freqs_orig, psd_orig = compute_psd(original, sampling_rate)
    freqs_denoised, psd_denoised = compute_psd(denoised, sampling_rate)
    
    plt.figure(figsize=(10, 6))
    
    plt.semilogy(freqs_orig, psd_orig, 'b-', alpha=0.7, label='Original')
    plt.semilogy(freqs_denoised, psd_denoised, 'r-', alpha=0.7, label='Denoised')
    
    plt.xlabel('Frequency (Hz)')
    plt.ylabel('Power Spectral Density')
    plt.title(f'PSD Comparison (Channel {channel_idx + 1})')
    plt.legend()
    plt.grid(True)
    plt.xlim(0, 100)  # Focus on relevant frequency range
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"PSD comparison saved to {save_path}")
    
    plt.show()

def save_checkpoint(model: torch.nn.Module, optimizer: torch.optim.Optimizer,
                   epoch: int, loss: float, path: str):
    """
    Save model checkpoint
    
    Args:
        model: PyTorch model
        optimizer: Optimizer
        epoch: Current epoch
        loss: Current loss
        path: Path to save checkpoint
    """
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
    }
    
    torch.save(checkpoint, path)
    logger.info(f"Checkpoint saved to {path}")

def load_checkpoint(model: torch.nn.Module, optimizer: torch.optim.Optimizer,
                   path: str) -> Tuple[int, float]:
    """
    Load model checkpoint
    
    Args:
        model: PyTorch model
        optimizer: Optimizer
        path: Path to checkpoint
        
    Returns:
        epoch: Last epoch
        loss: Last loss
    """
    checkpoint = torch.load(path, map_location='cpu')
    
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    epoch = checkpoint['epoch']
    loss = checkpoint['loss']
    
    logger.info(f"Checkpoint loaded from {path}, epoch: {epoch}, loss: {loss:.6f}")
    return epoch, loss

def count_parameters(model: torch.nn.Module) -> int:
    """Count total number of trainable parameters"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def setup_logging(log_dir: str, log_level: int = logging.INFO):
    """Setup logging configuration"""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger.info(f"Logging initialized. Log file: {log_file}")

def get_device() -> torch.device:
    """Get available device (CUDA or CPU)"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        logger.info(f"Using CUDA device: {torch.cuda.get_device_name()}")
    else:
        device = torch.device('cpu')
        logger.info("Using CPU device")
    
    return device