"""
可视化和报告生成模块
Author: Assistant
Date: 2025-07-15
Description: 负责生成可视化图片和处理报告
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from scipy.fft import fft, fftfreq
from datetime import datetime
import seaborn as sns

from vmd_engine import ProcessingResult
from config_manager import ConfigManager

# 设置中文字体
def setup_chinese_font():
    """设置中文字体"""
    import matplotlib.font_manager as fm
    import warnings
    
    # 抑制字体警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
    
    # 获取系统所有可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 定义中文字体候选列表（按优先级排序）
    chinese_fonts = [
        'SimHei', 'Microsoft YaHei', 'PingFang SC', 'STHeiti', 
        'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 
        'Noto Sans CJK SC', 'Source Han Sans CN', 'Arial Unicode MS',
    ]
    
    # 找到第一个可用的中文字体
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        # 设置字体
        plt.rcParams['font.sans-serif'] = [selected_font, 'Arial', 'sans-serif']
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['axes.unicode_minus'] = False
        print(f"成功设置中文字体: {selected_font}")
    else:
        # 如果没有找到中文字体，使用默认设置但禁用中文
        # plt.rcParams['font.family'] = 'Arial'
        plt.rcParams['axes.unicode_minus'] = False
        print("警告: 未找到中文字体，将使用英文标签")
        return False
    
    return True

# 调用字体设置并记录结果
CHINESE_FONT_AVAILABLE = setup_chinese_font()

def get_label(chinese_text, english_text=""):
    """根据字体可用性返回相应的标签"""
    if CHINESE_FONT_AVAILABLE:
        return chinese_text
    else:
        return english_text if english_text else chinese_text.encode('ascii', 'ignore').decode('ascii')

class VisualizationManager:
    """可视化管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 设置绘图样式
        # plt.style.use('seaborn-v0_8')
        # sns.set_palette("husl")
    
    def compute_spectrum(self, signal: np.ndarray, fs: int, max_freq: float = 50) -> Tuple[np.ndarray, np.ndarray]:
        """计算信号频谱"""
        try:
            n = len(signal)
            frequencies = fftfreq(n, 1/fs)
            spectrum = np.abs(fft(signal))
            
            # 只保留正频率部分且在max_freq范围内
            positive_mask = (frequencies >= 0) & (frequencies <= max_freq)
            return frequencies[positive_mask], spectrum[positive_mask]
            
        except Exception as e:
            self.logger.error(f"计算频谱失败: {e}")
            return np.array([]), np.array([])
    
    def plot_signal_comparison(self, result: ProcessingResult, fs: int, 
                             save_path: Optional[Path] = None) -> plt.Figure:
        """绘制信号对比图"""
        try:
            # 创建2x3的子图布局
            fig, axes = plt.subplots(2, 3, figsize=(20, 12))
            fig.suptitle(f'{get_label("通道", "Channel")} {result.channel} {get_label("处理结果对比", "Processing Results")}', fontsize=16)
            
            # 时间轴
            time_axis = np.arange(len(result.original_signal)) / fs
            
            # 时域对比
            axes[0, 0].plot(time_axis, result.original_signal, 'k-', alpha=0.7, linewidth=1, label=get_label('原始信号', 'Original'))
            axes[0, 0].plot(time_axis, result.reconstructed_signal, 'r-', linewidth=2, label=get_label('VMD重构', 'VMD Recon'))
            if hasattr(result, 'reference_signal') and result.reference_signal is not None:
                axes[0, 0].plot(time_axis, result.reference_signal, 'g--', alpha=0.8, linewidth=1.5, label=get_label('参考信号', 'Reference'))
            axes[0, 0].set_xlabel(get_label('时间 (s)', 'Time (s)'))
            axes[0, 0].set_ylabel(get_label('幅值', 'Amplitude'))
            axes[0, 0].set_title(get_label('时域信号对比', 'Time Domain Comparison'))
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            
            # 频域对比
            freq_orig, spec_orig = self.compute_spectrum(result.original_signal, fs)
            freq_recon, spec_recon = self.compute_spectrum(result.reconstructed_signal, fs)
            
            axes[0, 1].plot(freq_orig, spec_orig, 'k-', alpha=0.7, linewidth=1, label=get_label('原始信号', 'Original'))
            axes[0, 1].plot(freq_recon, spec_recon, 'r-', linewidth=2, label=get_label('VMD重构', 'VMD Recon'))
            if hasattr(result, 'reference_signal') and result.reference_signal is not None:
                freq_ref, spec_ref = self.compute_spectrum(result.reference_signal, fs)
                axes[0, 1].plot(freq_ref, spec_ref, 'g--', alpha=0.8, linewidth=1.5, label=get_label('参考信号', 'Reference'))
            axes[0, 1].set_xlabel(get_label('频率 (Hz)', 'Frequency (Hz)'))
            axes[0, 1].set_ylabel(get_label('幅值', 'Amplitude'))
            axes[0, 1].set_title(get_label('频域对比 (≤50Hz)', 'Frequency Domain (≤50Hz)'))
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
            
            # 参与重建的模态展示
            if result.filter_rules:
                kept_modes = []
                kept_labels = []
                for i, (mode, rule) in enumerate(zip(result.vmd_modes, result.filter_rules)):
                    if rule.keep:
                        center_freq = result.vmd_omega[-1, i] * fs / (2 * np.pi)
                        kept_modes.append(mode)
                        filter_info = f"({rule.filter_type})" if rule.filter_type != 'none' else ""
                        kept_labels.append(f'M{i+1} {filter_info} ({center_freq:.1f}Hz)')
                
                colors = plt.cm.tab10(np.linspace(0, 1, len(kept_modes)))
                for mode, label, color in zip(kept_modes, kept_labels, colors):
                    axes[0, 2].plot(time_axis, mode, color=color, alpha=0.8, linewidth=1, label=label)
                
                axes[0, 2].set_xlabel(get_label('时间 (s)', 'Time (s)'))
                axes[0, 2].set_ylabel(get_label('幅值', 'Amplitude'))
                axes[0, 2].set_title(get_label('参与重建的模态', 'Reconstruction Modes'))
                axes[0, 2].legend(fontsize=8)
                axes[0, 2].grid(True, alpha=0.3)
            
            # VMD所有模态分解
            colors = plt.cm.tab10(np.linspace(0, 1, result.vmd_modes.shape[0]))
            for i, mode in enumerate(result.vmd_modes):
                center_freq = result.vmd_omega[-1, i] * fs / (2 * np.pi)
                # 判断是否参与重建
                kept = result.filter_rules[i].keep if i < len(result.filter_rules) else True
                alpha = 1.0 if kept else 0.3
                linestyle = '-' if kept else '--'
                label = f'M{i+1} ({center_freq:.1f}Hz)' + (' ✓' if kept else ' ✗')
                axes[1, 0].plot(time_axis, mode, color=colors[i], alpha=alpha, 
                               linestyle=linestyle, linewidth=1, label=label)
            axes[1, 0].set_xlabel(get_label('时间 (s)', 'Time (s)'))
            axes[1, 0].set_ylabel(get_label('幅值', 'Amplitude'))
            axes[1, 0].set_title(get_label('VMD模态分解 (✓参与重建 ✗丢弃)', 'Reconstruction Modes'))
            axes[1, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
            axes[1, 0].grid(True, alpha=0.3)
            
            # 滤波前后频谱对比
            # 分别计算原始信号、重构信号和参考信号在不同频段的能量
            freq_bands = [(0.1, 1.0), (1.0, 8.0), (8.0, 30.0), (30.0, 100.0)]
            band_names = [
                get_label('超低频\n(0.1-1Hz)', 'Ultra Low\n(0.1-1Hz)'),
                get_label('低频\n(1-8Hz)', 'Low\n(1-8Hz)'),  
                get_label('中频\n(8-30Hz)', 'Mid\n(8-30Hz)'),
                get_label('高频\n(30-100Hz)', 'High\n(30-100Hz)')
            ]
            
            orig_powers = []
            recon_powers = []
            ref_powers = []
            
            for low, high in freq_bands:
                # 原始信号能量
                mask_orig = (freq_orig >= low) & (freq_orig <= high)
                orig_powers.append(np.sum(spec_orig[mask_orig] ** 2) if np.any(mask_orig) else 0)
                
                # 重构信号能量
                mask_recon = (freq_recon >= low) & (freq_recon <= high)
                recon_powers.append(np.sum(spec_recon[mask_recon] ** 2) if np.any(mask_recon) else 0)
                
                # 参考信号能量
                if hasattr(result, 'reference_signal') and result.reference_signal is not None:
                    mask_ref = (freq_ref >= low) & (freq_ref <= high)
                    ref_powers.append(np.sum(spec_ref[mask_ref] ** 2) if np.any(mask_ref) else 0)
            
            x = np.arange(len(band_names))
            width = 0.25
            
            axes[1, 1].bar(x - width, orig_powers, width, label=get_label('原始信号', 'Original'), alpha=0.7, color='black')
            axes[1, 1].bar(x, recon_powers, width, label=get_label('VMD重构', 'VMD Recon'), alpha=0.9, color='red')
            if ref_powers:
                axes[1, 1].bar(x + width, ref_powers, width, label=get_label('参考信号', 'Reference'), alpha=0.8, color='green')
            
            axes[1, 1].set_xlabel(get_label('频率段', 'Frequency Band'))
            axes[1, 1].set_ylabel(get_label('功率', 'Power'))
            axes[1, 1].set_title(get_label('频段能量对比', 'Frequency Band Power'))
            axes[1, 1].set_xticks(x)
            axes[1, 1].set_xticklabels(band_names, fontsize=8)
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
            
            # 相似性指标
            if result.similarity_metrics:
                # 分离原始信号和参考信号的指标
                orig_metrics = {}
                ref_metrics = {}
                
                for key, value in result.similarity_metrics.items():
                    if 'to_original' in key:
                        metric_name = key.replace('_to_original', '')
                        orig_metrics[metric_name] = value
                    elif 'to_reference' in key:
                        metric_name = key.replace('_to_reference', '')
                        ref_metrics[metric_name] = value
                
                # 绘制指标对比
                metrics_names = list(orig_metrics.keys())
                if metrics_names:
                    x_pos = np.arange(len(metrics_names))
                    width = 0.35
                    
                    orig_values = list(orig_metrics.values())
                    ref_values = [ref_metrics.get(name, 0) for name in metrics_names]
                    
                    bars1 = axes[1, 2].bar(x_pos - width/2, orig_values, width, 
                                          label=get_label('vs 原始信号','vs origin signals'), alpha=0.8, color='blue')
                    if any(ref_values):
                        bars2 = axes[1, 2].bar(x_pos + width/2, ref_values, width, 
                                              label=get_label('vs 参考信号','vs refer signals'), alpha=0.8, color='orange')
                    
                    axes[1, 2].set_xticks(x_pos)
                    axes[1, 2].set_xticklabels([name.replace('_', '\n') for name in metrics_names], fontsize=8)
                    axes[1, 2].set_ylabel(get_label('数值','values'))
                    axes[1, 2].set_title(get_label('相似性指标对比','similarity'))
                    axes[1, 2].legend()
                    axes[1, 2].grid(True, alpha=0.3)
                    
                    # 添加数值标签
                    for bar, value in zip(bars1, orig_values):
                        if value != 0:
                            axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                                           f'{value:.3f}', ha='center', va='bottom', fontsize=8)
                    
                    if any(ref_values):
                        for bar, value in zip(bars2, ref_values):
                            if value != 0:
                                axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                                               f'{value:.3f}', ha='center', va='bottom', fontsize=8)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"信号对比图已保存: {save_path}")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"绘制信号对比图失败: {e}")
            raise
    
    def plot_mode_analysis(self, result: ProcessingResult, fs: int,
                          save_path: Optional[Path] = None) -> plt.Figure:
        """绘制模态分析图"""
        try:
            K = result.vmd_modes.shape[0]
            fig, axes = plt.subplots(K, 2, figsize=(15, 3*K))
            fig.suptitle(f'Channel {result.channel} VMD Modes analysis', fontsize=16)
            
            time_axis = np.arange(len(result.original_signal)) / fs
            
            for i in range(K):
                mode = result.vmd_modes[i]
                center_freq = result.vmd_omega[-1, i] * fs / (2 * np.pi)
                
                # 时域
                axes[i, 0].plot(time_axis, mode, color=f'C{i}', linewidth=1.5)
                axes[i, 0].set_ylabel(get_label('幅值', 'Amplitude'))
                axes[i, 0].set_title(f'Modes {i+1} - time ({center_freq:.1f} Hz)')
                axes[i, 0].grid(True, alpha=0.3)
                
                # 频域
                freq, spectrum = self.compute_spectrum(mode, fs)
                axes[i, 1].plot(freq, spectrum, color=f'C{i}', linewidth=1.5)
                axes[i, 1].set_ylabel(get_label('幅值', 'Amplitude'))
                axes[i, 1].set_title(f'Modes {i+1} - freq')
                axes[i, 1].grid(True, alpha=0.3)
                
                if i == K-1:  # 最后一行添加x轴标签
                    axes[i, 0].set_xlabel('Time (s)')
                    axes[i, 1].set_xlabel('Freq (Hz)')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"模态分析图已保存: {save_path}")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"绘制模态分析图失败: {e}")
            raise
    
    def plot_channel_overview(self, results: Dict[int, ProcessingResult], fs: int,
                             save_path: Optional[Path] = None) -> plt.Figure:
        """绘制多通道概览图"""
        try:
            n_channels = len(results)
            fig, axes = plt.subplots(n_channels, 2, figsize=(15, 3*n_channels))
            fig.suptitle('多通道处理结果概览', fontsize=16)
            
            if n_channels == 1:
                axes = axes.reshape(1, -1)
            
            for i, (channel, result) in enumerate(sorted(results.items())):
                time_axis = np.arange(len(result.original_signal)) / fs
                
                # 时域对比
                axes[i, 0].plot(time_axis, result.original_signal, 'k-', alpha=0.5, label='origin')
                axes[i, 0].plot(time_axis, result.reconstructed_signal, 'r-', linewidth=2, label='重构')
                axes[i, 0].set_ylabel('Amplitude')
                axes[i, 0].set_title(f'Channel {channel} - time')
                axes[i, 0].legend()
                axes[i, 0].grid(True, alpha=0.3)
                
                # 频域对比
                freq_orig, spec_orig = self.compute_spectrum(result.original_signal, fs)
                freq_recon, spec_recon = self.compute_spectrum(result.reconstructed_signal, fs)
                
                axes[i, 1].plot(freq_orig, spec_orig, 'k-', alpha=0.5, label='origin')
                axes[i, 1].plot(freq_recon, spec_recon, 'r-', linewidth=2, label='reconstruction')
                axes[i, 1].set_ylabel('Amplitude')
                axes[i, 1].set_title(f'Channel {channel} - freq')
                axes[i, 1].legend()
                axes[i, 1].grid(True, alpha=0.3)
                
                if i == n_channels-1:  # 最后一行添加x轴标签
                    axes[i, 0].set_xlabel('time (s)')
                    axes[i, 1].set_xlabel('freq (Hz)')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"多通道概览图已保存: {save_path}")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"绘制多通道概览图失败: {e}")
            raise
    
    def plot_similarity_matrix(self, results: Dict[int, ProcessingResult],
                              save_path: Optional[Path] = None) -> plt.Figure:
        """绘制相似性矩阵"""
        try:
            channels = sorted(results.keys())
            n_channels = len(channels)
            
            # 提取相关性数据
            correlation_matrix = np.zeros((n_channels, n_channels))
            
            for i, ch1 in enumerate(channels):
                for j, ch2 in enumerate(channels):
                    if i == j:
                        correlation_matrix[i, j] = 1.0
                    else:
                        # 计算两个通道重构信号的相关性
                        signal1 = results[ch1].reconstructed_signal
                        signal2 = results[ch2].reconstructed_signal
                        
                        if len(signal1) == len(signal2):
                            corr = np.corrcoef(signal1, signal2)[0, 1]
                            correlation_matrix[i, j] = corr
            
            # 绘制热图
            fig, ax = plt.subplots(figsize=(10, 8))
            im = ax.imshow(correlation_matrix, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)
            
            # 设置标签
            ax.set_xticks(range(n_channels))
            ax.set_yticks(range(n_channels))
            ax.set_xticklabels([f'CH{ch}' for ch in channels])
            ax.set_yticklabels([f'CH{ch}' for ch in channels])
            
            # 添加数值标签
            for i in range(n_channels):
                for j in range(n_channels):
                    text = ax.text(j, i, f'{correlation_matrix[i, j]:.2f}',
                                 ha="center", va="center", color="black")
            
            ax.set_title(get_label('通道间相关性矩阵','corr matrix'))
            plt.colorbar(im, ax=ax, label='corr')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"相似性矩阵已保存: {save_path}")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"绘制相似性矩阵失败: {e}")
            raise
    
    def generate_all_visualizations(self, results: Dict[int, ProcessingResult], 
                                  file_name: str, fs: int,
                                  reference_data: Optional[Dict[int, np.ndarray]] = None):
        """生成所有可视化图片"""
        try:
            output_path = self.config_manager.get_path('../../output')
            vis_config = self.config_manager.get_output_config().get('visualization', {})
            
            if not vis_config.get('enabled', True):
                return
            
            # 创建可视化目录
            vis_dir = output_path / f"{file_name}_visualizations"
            vis_dir.mkdir(exist_ok=True)
            
            # 1. 生成单通道详细分析图
            for channel, result in results.items():
                # 信号对比图
                if vis_config.get('comparison_plots', True):
                    save_path = vis_dir / f"channel_{channel}_comparison.png"
                    fig = self.plot_signal_comparison(result, fs, save_path)
                    plt.close(fig)
                
                # 模态分析图
                if vis_config.get('mode_plots', True):
                    save_path = vis_dir / f"channel_{channel}_modes.png"
                    fig = self.plot_mode_analysis(result, fs, save_path)
                    plt.close(fig)
            
            # 2. 生成多通道概览图
            if vis_config.get('comparison_plots', True):
                save_path = vis_dir / "channels_overview.png"
                fig = self.plot_channel_overview(results, fs, save_path)
                plt.close(fig)
            
            # 3. 生成相似性矩阵
            if len(results) > 1:
                save_path = vis_dir / "similarity_matrix.png"
                fig = self.plot_similarity_matrix(results, save_path)
                plt.close(fig)
            
            self.logger.info(f"所有可视化图片已生成至: {vis_dir}")
            
        except Exception as e:
            self.logger.error(f"生成可视化图片失败: {e}")
            raise

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
    
    def generate_html_report(self, results: Dict[int, ProcessingResult], 
                           file_name: str, processing_time: float) -> str:
        """生成HTML报告"""
        try:
            # 获取配置
            processing_config = self.config_manager.get_processing_config()
            vmd_config = self.config_manager.get_vmd_config()
            
            # 计算统计信息
            total_channels = len(results)
            avg_correlation = np.mean([r.similarity_metrics.get('correlation_to_original', 0) 
                                     for r in results.values()])
            
            # 生成HTML内容
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>VMD处理报告 - {file_name}</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 10px; }}
                    .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                    .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #f9f9f9; }}
                    table {{ border-collapse: collapse; width: 100%; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                    .good {{ color: green; }}
                    .warning {{ color: orange; }}
                    .error {{ color: red; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>VMD处理报告</h1>
                    <p><strong>文件名:</strong> {file_name}</p>
                    <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>处理总时间:</strong> {processing_time:.2f} 秒</p>
                </div>
                
                <div class="section">
                    <h2>处理配置</h2>
                    <div class="metric"><strong>处理通道:</strong> {processing_config.channels}</div>
                    <div class="metric"><strong>采样率:</strong> {processing_config.sampling_rate} Hz</div>
                    <div class="metric"><strong>信号长度:</strong> {processing_config.sample_length} 点</div>
                    <div class="metric"><strong>VMD模态数:</strong> {vmd_config.K}</div>
                    <div class="metric"><strong>Alpha参数:</strong> {vmd_config.alpha}</div>
                </div>
                
                <div class="section">
                    <h2>处理结果统计</h2>
                    <div class="metric"><strong>处理通道数:</strong> {total_channels}</div>
                    <div class="metric"><strong>平均相关性:</strong> {avg_correlation:.3f}</div>
                    <div class="metric"><strong>成功率:</strong> 100%</div>
                </div>
                
                <div class="section">
                    <h2>通道详细结果</h2>
                    <table>
                        <tr>
                            <th>通道</th>
                            <th>处理时间(s)</th>
                            <th>原始信号相关性</th>
                            <th>参考信号相关性</th>
                            <th>信噪比(dB)</th>
                            <th>均方根误差</th>
                        </tr>
            """
            
            for channel, result in sorted(results.items()):
                metrics = result.similarity_metrics
                correlation_orig = metrics.get('correlation_to_original', 0)
                correlation_ref = metrics.get('correlation_to_reference', 0)
                snr = metrics.get('snr_to_original', 0)
                rmse = metrics.get('rmse_to_original', 0)
                
                # 根据相关性设置颜色
                corr_class = 'good' if correlation_orig > 0.8 else 'warning' if correlation_orig > 0.6 else 'error'
                
                html_content += f"""
                        <tr>
                            <td>{channel}</td>
                            <td>{result.processing_time:.2f}</td>
                            <td class="{corr_class}">{correlation_orig:.3f}</td>
                            <td>{correlation_ref:.3f}</td>
                            <td>{snr:.1f}</td>
                            <td>{rmse:.6f}</td>
                        </tr>
                """
            
            html_content += """
                    </table>
                </div>
                
                <div class="section">
                    <h2>数据结构说明</h2>
                    <p>61通道数据结构：第1通道为idx，后36通道为9个通道一组的采集数据，共4组。</p>
                    <ul>
                        <li>第1组 (通道2-10): 参考信号通道38</li>
                        <li>第2组 (通道11-19): 参考信号通道39</li>
                        <li>第3组 (通道20-28): 参考信号通道40</li>
                        <li>第4组 (通道29-37): 参考信号通道41</li>
                    </ul>
                </div>
                
                <div class="section">
                    <h2>处理说明</h2>
                    <p>本次处理采用VMD（变分模态分解）方法，对信号进行模态分解后，根据预设策略进行滤波重构。</p>
                    <p>滤波策略：最低频中心分量执行0.6-30Hz带通滤波，其他模态执行20Hz低通滤波。</p>
                </div>
            </body>
            </html>
            """
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {e}")
            raise
    
    def save_report(self, results: Dict[int, ProcessingResult], 
                   file_name: str, processing_time: float):
        """保存报告"""
        try:
            report_config = self.config_manager.get_output_config().get('report', {})
            
            if not report_config.get('enabled', True):
                return
            
            output_path = self.config_manager.get_path('../../output')
            
            # 生成HTML报告
            html_content = self.generate_html_report(results, file_name, processing_time)
            
            # 保存报告
            report_file = output_path / f"{file_name}_report.html"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"处理报告已保存: {report_file}")
            
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
            raise