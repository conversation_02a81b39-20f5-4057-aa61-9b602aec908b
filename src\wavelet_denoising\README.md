# 小波降噪实验框架

一个全面的小波降噪实验平台，支持多种降噪算法和多维度评估。

## 功能特性

### 📊 数据处理模块
- **多格式数据加载**：支持txt、csv等格式，自动识别分隔符
- **通道智能分离**：自动分离36个信号通道和4个标准源通道
- **灵活数据分段**：可指定时间段进行处理
- **多种预处理**：标准化、归一化、去均值等

### 🔧 降噪算法库
1. **基准小波降噪**：标准的小波阈值降噪（Symlet、Daubechies等）
2. **SVD多通道降噪**：利用奇异值分解的多通道联合降噪
3. **双变量收缩降噪**：基于局部方差的自适应阈值收缩
4. **双树复小波降噪**：近似平移不变的复小波变换
5. **小波+残差VMD降噪**：小波降噪后对残差进行VMD处理
6. **迭代小波降噪**：多次迭代优化的小波降噪

### 📈 评估体系
- **与标准源相关性**：计算与4个标准源的绝对值相关系数
- **信噪比改善**：估算SNR提升量
- **残差分析**：残差能量和白噪声特性分析
- **综合得分**：多指标加权评分

### 🎨 可视化功能
- **过程可视化**：每种方法的详细处理过程图
- **波形对比图**：叠加显示、单独信号对比
- **频谱分析图**：功率谱密度对比
- **方法性能雷达图**：多维度性能对比
- **评估结果报告**：详细的量化评估报告

## 文件结构

```
wavelet_denoising/
├── __init__.py                  # 包初始化文件
├── main.py                      # 主执行脚本（配置和运行）
├── data_loader.py               # 数据加载和预处理模块
├── denoising_algorithms.py      # 六种降噪算法实现
├── evaluation.py                # 多维度评估模块
├── test_framework.py            # 框架测试脚本
└── README.md                    # 本文档
```

## 快速开始

### 1. 基本配置

编辑 `main.py` 中的配置区域：

```python
# 数据配置
FILE_PATH = "your_data_file.txt"
START_TIME = 0.0
DURATION = 10.0
SAMPLING_RATE = 1000

# 通道选择
SELECTED_CHANNELS = [0, 1, 2, 8, 9]  # 选择要处理的通道
USE_ALL_CHANNELS = False

# 方法选择
METHODS_TO_TEST = {
    'baseline_wavelet': True,
    'svd': True,
    'bivariate_shrinkage': True,
    'dtcwt': True,
    'residual_vmd': True,
    'iterative_wavelet': True
}
```

### 2. 运行实验

```bash
cd src/wavelet_denoising
python main.py
```

### 3. 快速测试

```bash
# 测试框架功能
python test_framework.py

# 快速测试单个方法
python -c "
from main import quick_test_single_method
quick_test_single_method('baseline_wavelet', 0)
"

# 对比最佳方法
python -c "
from main import compare_top_methods
compare_top_methods()
"
```

## 详细使用说明

### 数据格式要求

- **文件格式**：txt或csv文件
- **数据结构**：40000行 × 41列
  - 第1列：索引列
  - 第2-37列：36个信号通道
  - 第38-41列：4个标准源通道
- **采样率**：1000 Hz（可配置）

### 算法参数配置

```python
ALGORITHM_PARAMS = {
    'baseline_wavelet': {
        'wavelet_type': 'sym8',      # 小波类型
        'level': 6,                  # 分解层数
        'threshold_method': 'soft',  # 阈值方法
        'threshold_mode': 'sure'     # 阈值选择模式
    },
    'svd': {
        'k': None                    # 保留奇异值数量（None=自动）
    },
    'residual_vmd': {
        'vmd_k': 5,                  # VMD模态数
        'vmd_alpha': 2000            # VMD平衡参数
    },
    # ... 其他算法参数
}
```

### 评估指标说明

1. **相关性指标**
   - `overall_mean_correlation`：与标准源的平均相关性
   - `overall_max_correlation`：最大相关性
   - `ref_X_mean_correlation`：与第X个标准源的平均相关性

2. **SNR指标**
   - `mean_snr_improvement`：平均SNR改善量（dB）
   - `mean_snr_original`：估算的原始SNR
   - `mean_snr_denoised`：降噪后SNR

3. **残差指标**
   - `mean_relative_residual_energy`：平均相对残差能量
   - `mean_whiteness_score`：白噪声特性得分
   - `mean_kurtosis`：残差峰度值

4. **综合得分**
   - 相关性（权重40%）+ SNR改善（权重30%）+ 残差质量（权重30%）
   - 范围：0-1，越高越好

## 高级功能

### 自定义降噪算法

在 `denoising_algorithms.py` 中添加新算法：

```python
def denoise_custom_method(signal, param1, param2, visualize=False):
    """自定义降噪方法"""
    # 实现您的算法
    denoised_signal = your_algorithm(signal, param1, param2)
    
    if visualize:
        # 可选：添加可视化
        plot_your_results()
    
    return denoised_signal
```

### 批量处理多文件

```python
import os
from main import *

# 修改配置为批量模式
file_list = [f for f in os.listdir("data_dir") if f.endswith('.txt')]

for file_path in file_list:
    FILE_PATH = file_path
    print(f"处理文件: {file_path}")
    main()
```

### 结果导出

设置 `SAVE_RESULTS = True` 并指定输出目录：

```python
SAVE_RESULTS = True
OUTPUT_DIR = "../../output/wavelet_denoising"
```

## 性能优化建议

### 通道选择策略
- **探索阶段**：选择3-5个代表性通道快速测试
- **精细分析**：选择高相关性通道进行详细分析
- **全通道处理**：最终验证时处理所有36个通道

### 时间段选择
- **信号稳定段**：选择信号质量较好的时间段
- **典型噪声段**：包含典型噪声特征的片段
- **长度平衡**：10-30秒通常是合适的分析长度

### 算法组合策略
1. **初步筛选**：基准小波 + 双变量收缩
2. **进阶对比**：+ 迭代小波 + DTCWT
3. **高级方案**：+ 残差VMD + SVD多通道

## 常见问题

### Q: 内存使用过多怎么办？
A: 
- 减少同时处理的通道数
- 缩短处理时间段
- 关闭不必要的可视化（`SHOW_INDIVIDUAL_PROCESS = False`）

### Q: 某个算法报错怎么办？
A:
- 检查输入信号长度（建议>1000点）
- 确认算法参数合理性
- 查看日志获取详细错误信息

### Q: 如何选择最佳算法？
A:
- 查看综合得分排序
- 重点关注与标准源的相关性
- 考虑计算复杂度和实时性需求

### Q: 评估得分偏低怎么办？
A:
- 检查数据质量和预处理方法
- 尝试不同的算法参数
- 确认通道分组配置正确

## 技术依赖

- Python 3.7+
- NumPy：数值计算
- SciPy：信号处理和小波变换
- matplotlib：可视化
- PyWavelets：小波变换库
- logging：日志记录

## 扩展开发

框架采用模块化设计，易于扩展：

1. **新增算法**：在 `denoising_algorithms.py` 中实现
2. **新增评估指标**：在 `evaluation.py` 中添加
3. **新增数据格式**：在 `data_loader.py` 中支持
4. **新增可视化**：各模块都支持自定义可视化

## 版本信息

- **版本**：1.0.0
- **作者**：Youhao & Gemini & Claude Code
- **更新日期**：2025-07-17
- **兼容性**：适用于61通道SQUID数据格式

---

## 使用示例

```python
# 完整使用示例
from wavelet_denoising import *

# 1. 加载数据
data = load_data("your_file.txt")
idx, signals, references = extract_channels(data)

# 2. 预处理
processed = preprocess_signals(signals[:5000, :3], 'standardize')

# 3. 降噪
denoised = denoise_baseline_wavelet(processed[:, 0], visualize=True)

# 4. 评估
evaluator = DenoiseEvaluator()
result = evaluator.comprehensive_evaluation(
    processed, denoised.reshape(-1, 1), references[:5000, :],
    {0: [0]}, "My Method"
)

print(f"综合得分: {result['overall_score']:.3f}")
```

享受您的降噪实验之旅！🚀