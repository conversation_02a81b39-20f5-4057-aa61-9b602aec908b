## 项目摘要

本项目是一个降噪测试试验，旨在开发特定数据的降噪算法。

- E:\个人临时文件夹\王友好\61通道\第三代测试数据\第三代测试数据 地址下有若干文件夹，有些文件夹下有61通道.txt数据，要读取所有这种数据，数据可以读取成60000*61格式，其中61是通道。
- 初期 我们先可视化看看这些数据长什么样子。以及做一些基础的频谱分析。 

## 运行说明

### 环境准备
1. 安装Python 3.7或更高版本
2. 安装所需依赖包：
   ```
   pip install -r requirements.txt
   ```

### 运行分析脚本
基本运行命令：
```
python analyze_61chan.py
```

### 命令行参数
脚本支持以下命令行参数：

- `--data_path`: 指定61通道数据文件的根目录路径（默认为"E:\个人临时文件夹\王友好\61通道\第三代测试数据\第三代测试数据"）
- `--output_dir`: 指定输出结果的基础保存目录（默认为"output"）
- `--sampling_rate`: 指定数据采样率，单位Hz（默认为1000Hz）
- `--max_files`: 最多处理的文件数量，不指定则处理全部文件

示例：
```
# 使用指定的数据路径和输出目录
python analyze_61chan.py --data_path "D:\数据\61通道数据" --output_dir "结果"

# 设置采样率为2000Hz并只处理前5个文件（推荐首次运行时使用该选项进行测试）
python analyze_61chan.py --sampling_rate 2000 --max_files 5
```

### 输出结果
脚本执行后，将在基础输出目录下创建以下内容：
- `data_summary.txt`：总体数据摘要，包含所有处理文件的索引
- 为每个处理的数据文件创建一个单独的子目录，子目录名称基于原文件的目录名和文件名
- 在每个子目录中保存：
  - `summary.txt`：该数据文件的详细统计信息
  - `raw_data.png`：原始波形图
  - `spectrum.png`：频谱分析图
  - `spectrogram.png`：时频分析图
  - `correlation.png`：通道相关性矩阵

### 脚本功能说明

#### 数据文件处理
- 支持自动检测分隔符（空格、制表符或逗号）
- 指定使用UTF-8编码读取文件，解决中文路径问题
- 自动处理文件名中包含的中文字符
- 为每个数据文件创建独立的子目录，避免输出文件混淆

#### 数据分析内容
- 原始波形可视化（显示前10个通道）
- 功率谱密度分析（使用Welch方法）
- 时频图分析（使用短时傅里叶变换）
- 通道间相关性矩阵分析

### 注意事项
- 默认采样率设置为1000Hz，如需调整请修改代码中的`fs`参数
- 如需修改数据源路径，请编辑`analyze_61chan.py`中的`data_root`变量 

# 运行Streamlit应用

要运行Streamlit应用程序，请执行以下命令：
```
streamlit run vmd_analyzer.py
```

该应用程序将打开一个浏览器窗口，您可以在其中：
- 选择要分析的数据文件
- 选择要分析的通道
- 查看频谱
- 调整VMD参数并应用分解
- 下载分解结果

这个应用程序提供了丰富的交互功能，如：
- 波形可视化（可选择完整或部分显示）
- 频谱分析
- VMD参数调整
- 分段选择（可以选择分析特定长度和起始位置的数据段）
- VMD结果可视化和下载



## 简介
VMD（变分模态分解）分析工具是一个交互式应用程序，用于对61通道数据进行可视化分析和VMD分解。通过该工具，您可以：
- 浏览并选择61通道数据文件
- 查看指定通道的原始波形和频谱
- 应用VMD分解并调整各种参数
- 下载VMD分解结果

   
## 运行应用
在安装完所有依赖后，使用以下命令启动应用程序：
```
streamlit run vmd_analyzer.py
```

这将启动一个本地Web服务器，并自动在您的默认浏览器中打开应用界面。

## 使用方法

### 1. 数据选择
- 在左侧边栏中，输入或选择包含61通道数据的目录路径
- 从下拉菜单中选择子目录
- 选择包含"61通道.txt"的数据文件

### 2. 数据可视化
- 使用"通道选择"滑块选择要分析的通道
- 查看所选通道的原始波形
- 可以选择显示完整波形或部分波形

### 3. VMD分解
- 在侧边栏中勾选"应用VMD分解"选项
- 设置VMD参数：
  - Alpha：平衡参数，控制带宽
  - Tau：双重上升时间步长
  - K：要恢复的模态数量
  - DC：是否将第一个模态保持在DC（0频率）
  - 初始化方式：模态初始化策略
  - 收敛容忍度：收敛标准的容忍度
- 使用滑块选择要分析的数据段长度和起始位置
- 查看VMD分解结果和各个模态波形
- 可以下载VMD分解结果为CSV文件

### 4. 参数说明
点击"显示VMD参数说明"可以查看各个参数的详细说明。

## VMD参数建议值

| 参数 | 建议值范围 | 说明 |
|------|------------|------|
| Alpha | 500-5000 | 较大的Alpha会导致更窄的频带 |
| Tau | 0 | 一般为0(噪声容忍度) |
| K | 3-7 | 根据信号复杂度选择 |
| DC | True/False | 当信号有明显直流分量时选True |
| Init | 1 | 均匀分布通常效果最佳 |
| Tol | 1e-6 | 收敛准则的容忍度 |

## 故障排除
- 如果应用无法加载数据，请检查文件格式和路径
- VMD分解可能需要一些时间，特别是对大型数据集
- 如遇内存错误，请减少分析数据段的长度 

## 其它

如果要直接使用vmd ，则参考使用 u, u_hat, omega = VMD(signal_1d, alpha, tau, K, DC, init, tol)

