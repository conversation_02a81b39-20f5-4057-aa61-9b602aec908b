"""
Inference pipeline for Noise2Sim MCG denoising
"""

import torch
import numpy as np
from typing import List, Tuple, Optional, Dict
import logging
import os
from pathlib import Path

from .config import Config
from .model import UNet1D, create_model
from .utils import (
    load_mcg_data,
    create_patches,
    group_channels,
    reconstruct_from_patches,
    get_device,
    load_checkpoint
)

logger = logging.getLogger(__name__)

class Noise2SimInference:
    """
    Inference pipeline for Noise2Sim model
    """
    
    def __init__(self, config: Config, model_paths: Optional[Dict[int, str]] = None):
        """
        Initialize inference pipeline
        
        Args:
            config: Configuration object
            model_paths: Dictionary mapping group_idx to model checkpoint path
        """
        self.config = config
        self.device = get_device()
        
        # Load models for each channel group
        self.models = {}
        
        for group_idx in range(4):
            # Create model
            model, _ = create_model(config)
            model = model.to(self.device)
            
            # Load checkpoint if provided
            if model_paths and group_idx in model_paths:
                model_path = model_paths[group_idx]
                if os.path.exists(model_path):
                    # Create dummy optimizer for loading
                    optimizer = torch.optim.Adam(model.parameters())
                    
                    try:
                        load_checkpoint(model, optimizer, model_path)
                        logger.info(f"Loaded model for group {group_idx} from {model_path}")
                    except Exception as e:
                        logger.error(f"Failed to load model for group {group_idx}: {e}")
                        raise
                else:
                    logger.warning(f"Model path not found for group {group_idx}: {model_path}")
            
            model.eval()
            self.models[group_idx] = model
        
        logger.info(f"Inference pipeline initialized for {len(self.models)} groups")
    
    def denoise_signal(self, signal: np.ndarray, 
                      reference: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Denoise a complete MCG signal
        
        Args:
            signal: Input signal (samples, 36)
            reference: Reference channels (samples, 4) - optional
            
        Returns:
            denoised_signal: Denoised signal (samples, 36)
        """
        if signal.shape[1] != 36:
            raise ValueError(f"Expected 36 channels, got {signal.shape[1]}")
        
        logger.info(f"Denoising signal with shape {signal.shape}")
        
        # Create patches
        patches = create_patches(
            signal, 
            self.config.patch_length, 
            self.config.overlap_step
        )
        
        # Group channels
        grouped_patches = group_channels(patches, self.config.channel_groups)
        
        # Denoise each group
        denoised_groups = []
        
        for group_idx in range(4):
            group_patches = grouped_patches[group_idx]
            model = self.models[group_idx]
            
            # Denoise patches in batches
            denoised_patches = self._denoise_patches(group_patches, model)
            denoised_groups.append(denoised_patches)
        
        # Reconstruct signal from patches
        denoised_signal = self._reconstruct_signal(
            denoised_groups, 
            signal.shape[0]
        )
        
        logger.info(f"Denoising completed. Output shape: {denoised_signal.shape}")
        return denoised_signal
    
    def _denoise_patches(self, patches: np.ndarray, model: UNet1D) -> np.ndarray:
        """
        Denoise patches using trained model
        
        Args:
            patches: Input patches (n_patches, 9, patch_length)
            model: Trained model
            
        Returns:
            denoised_patches: Denoised patches (n_patches, 9, patch_length)
        """
        n_patches = patches.shape[0]
        denoised_patches = np.zeros_like(patches)
        
        # Process in batches to avoid memory issues
        batch_size = self.config.batch_size
        
        with torch.no_grad():
            for i in range(0, n_patches, batch_size):
                end_idx = min(i + batch_size, n_patches)
                batch_patches = patches[i:end_idx]
                
                # Convert to tensor and move to device
                batch_tensor = torch.FloatTensor(batch_patches).to(self.device)
                
                # Forward pass
                batch_output = model(batch_tensor)
                
                # Move back to CPU and convert to numpy
                batch_output = batch_output.cpu().numpy()
                
                # Store results
                denoised_patches[i:end_idx] = batch_output
        
        return denoised_patches
    
    def _reconstruct_signal(self, denoised_groups: List[np.ndarray], 
                           original_length: int) -> np.ndarray:
        """
        Reconstruct full signal from denoised group patches
        
        Args:
            denoised_groups: List of denoised patches for each group
            original_length: Original signal length
            
        Returns:
            reconstructed_signal: Full reconstructed signal (samples, 36)
        """
        reconstructed_signal = np.zeros((original_length, 36))
        
        for group_idx in range(4):
            group_patches = denoised_groups[group_idx]
            channel_indices = self.config.channel_groups[group_idx]
            
            # Reconstruct from patches
            group_signal = reconstruct_from_patches(
                group_patches,
                original_length,
                self.config.overlap_step,
                self.config.patch_length
            )
            
            # Place in correct channels
            reconstructed_signal[:, channel_indices] = group_signal
        
        return reconstructed_signal
    
    def denoise_file(self, file_path: str, output_path: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Denoise a complete MCG file
        
        Args:
            file_path: Path to input file
            output_path: Path to save denoised signal (optional)
            
        Returns:
            original_signal: Original signal (samples, 36)
            denoised_signal: Denoised signal (samples, 36)
        """
        # Load data
        signal_channels, reference_channels = load_mcg_data(file_path)
        
        # Denoise signal
        denoised_signal = self.denoise_signal(signal_channels, reference_channels)
        
        # Save if output path provided
        if output_path:
            # Create combined output with time index
            time_index = np.arange(len(denoised_signal)).reshape(-1, 1)
            output_data = np.concatenate([
                time_index,
                denoised_signal,
                reference_channels
            ], axis=1)
            
            # Save to file
            np.savetxt(output_path, output_data, delimiter='\t', fmt='%.6f')
            logger.info(f"Denoised signal saved to {output_path}")
        
        return signal_channels, denoised_signal
    
    def batch_denoise(self, input_dir: str, output_dir: str, 
                     file_pattern: str = "*.txt") -> Dict[str, str]:
        """
        Batch denoise multiple files
        
        Args:
            input_dir: Directory containing input files
            output_dir: Directory to save denoised files
            file_pattern: File pattern to match
            
        Returns:
            results: Dictionary mapping input files to output files
        """
        import glob
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Find input files
        input_files = glob.glob(os.path.join(input_dir, file_pattern))
        input_files.sort()
        
        results = {}
        
        logger.info(f"Starting batch denoising of {len(input_files)} files")
        
        for i, input_file in enumerate(input_files):
            try:
                # Generate output filename
                input_name = os.path.basename(input_file)
                name_without_ext = os.path.splitext(input_name)[0]
                output_file = os.path.join(output_dir, f"{name_without_ext}_denoised.txt")
                
                logger.info(f"Processing {i+1}/{len(input_files)}: {input_name}")
                
                # Denoise file
                original, denoised = self.denoise_file(input_file, output_file)
                
                results[input_file] = output_file
                
                logger.info(f"Completed: {input_name}")
                
            except Exception as e:
                logger.error(f"Error processing {input_file}: {e}")
                continue
        
        logger.info(f"Batch denoising completed. Processed {len(results)} files")
        return results

def denoise_signal(signal: np.ndarray, 
                  config: Config, 
                  model_paths: Dict[int, str]) -> np.ndarray:
    """
    Convenience function to denoise a signal
    
    Args:
        signal: Input signal (samples, 36)
        config: Configuration object
        model_paths: Dictionary mapping group_idx to model checkpoint path
        
    Returns:
        denoised_signal: Denoised signal (samples, 36)
    """
    inference = Noise2SimInference(config, model_paths)
    return inference.denoise_signal(signal)

def load_best_models(config: Config) -> Dict[int, str]:
    """
    Load paths to best models for each group
    
    Args:
        config: Configuration object
        
    Returns:
        model_paths: Dictionary mapping group_idx to best model path
    """
    model_paths = {}
    
    for group_idx in range(4):
        best_model_path = os.path.join(
            config.checkpoint_dir,
            f'best_model_group_{group_idx}.pth'
        )
        
        if os.path.exists(best_model_path):
            model_paths[group_idx] = best_model_path
        else:
            logger.warning(f"Best model not found for group {group_idx}: {best_model_path}")
    
    return model_paths

def test_inference(config: Config, test_file: str):
    """
    Test inference pipeline
    
    Args:
        config: Configuration object
        test_file: Path to test file
    """
    import matplotlib.pyplot as plt
    from .utils import plot_signal_comparison, plot_psd_comparison
    
    # Load best models
    model_paths = load_best_models(config)
    
    if not model_paths:
        logger.error("No trained models found. Please train models first.")
        return
    
    # Create inference pipeline
    inference = Noise2SimInference(config, model_paths)
    
    # Test with a single file
    logger.info(f"Testing inference with {test_file}")
    
    try:
        # Denoise file
        original, denoised = inference.denoise_file(test_file)
        
        # Plot comparison
        plot_signal_comparison(
            original, 
            denoised, 
            sampling_rate=config.sampling_rate,
            channel_idx=0,
            time_range=(0, 10)  # First 10 seconds
        )
        
        # Plot PSD comparison
        plot_psd_comparison(
            original,
            denoised,
            sampling_rate=config.sampling_rate,
            channel_idx=0
        )
        
        # Print statistics
        residual = original - denoised
        logger.info(f"Residual statistics:")
        logger.info(f"  Mean: {np.mean(residual):.6f}")
        logger.info(f"  Std: {np.std(residual):.6f}")
        logger.info(f"  Max: {np.max(np.abs(residual)):.6f}")
        
    except Exception as e:
        logger.error(f"Inference test failed: {e}")

if __name__ == "__main__":
    # Test inference
    config = Config()
    
    # Test file path
    test_file = os.path.join(
        config.data_dir,
        "20250711标准源/PLAG_2025_000147.tdms_L.txt"
    )
    
    if os.path.exists(test_file):
        test_inference(config, test_file)
    else:
        logger.error(f"Test file not found: {test_file}")
        logger.info("Please train the model first or provide a valid test file path")