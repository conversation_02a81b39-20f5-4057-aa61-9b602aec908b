"""
PyTorch Dataset for MCG data with Noise2Sim masking strategy
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from typing import List, Tuple, Dict, Optional
import logging
import os
from pathlib import Path

from .utils import (
    load_mcg_data, 
    create_patches, 
    group_channels, 
    create_mask_indices, 
    apply_mask,
    set_seed
)
from .config import Config

logger = logging.getLogger(__name__)

class MCGDataset(Dataset):
    """
    MCG Dataset with Noise2Sim masking strategy
    
    This dataset:
    1. Loads MCG data files
    2. Creates overlapping patches
    3. Groups 36 channels into 4 groups of 9 channels each
    4. Applies random masking for self-supervised learning
    """
    
    def __init__(self, 
                 file_paths: List[str], 
                 config: Config, 
                 group_idx: int = 0,
                 transform: Optional[callable] = None):
        """
        Initialize MCG Dataset
        
        Args:
            file_paths: List of data file paths
            config: Configuration object
            group_idx: Channel group index (0-3)
            transform: Optional transform function
        """
        self.file_paths = file_paths
        self.config = config
        self.group_idx = group_idx
        self.transform = transform
        
        # Validate group index
        if group_idx not in config.channel_groups:
            raise ValueError(f"Invalid group_idx: {group_idx}")
        
        self.channel_indices = config.channel_groups[group_idx]
        
        # Load and process all data
        self.patches = []
        self.file_indices = []
        
        self._load_all_data()
        
        logger.info(f"Dataset initialized for group {group_idx}")
        logger.info(f"Total patches: {len(self.patches)}")
        logger.info(f"Channels in group: {self.channel_indices}")
    
    def _load_all_data(self):
        """Load all data files and create patches"""
        for file_idx, file_path in enumerate(self.file_paths):
            try:
                # Load MCG data
                signal_channels, reference_channels = load_mcg_data(file_path)
                
                # Create patches
                patches = create_patches(
                    signal_channels, 
                    self.config.patch_length, 
                    self.config.overlap_step
                )
                
                # Group channels and select current group
                grouped_patches = group_channels(patches, self.config.channel_groups)
                group_patches = grouped_patches[self.group_idx]
                
                # Store patches
                for patch in group_patches:
                    self.patches.append(patch)
                    self.file_indices.append(file_idx)
                
                logger.info(f"Loaded {len(group_patches)} patches from {file_path}")
                
            except Exception as e:
                logger.error(f"Error loading file {file_path}: {e}")
                continue
    
    def __len__(self) -> int:
        """Return total number of patches"""
        return len(self.patches)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample with masking
        
        Args:
            idx: Sample index
            
        Returns:
            sample: Dictionary containing:
                - input: Masked patch (9, patch_length)
                - target: Original values at masked positions (n_mask_points, 9)
                - mask_indices: Indices of masked positions
                - file_idx: Source file index
        """
        # Get original patch
        patch = self.patches[idx].copy()  # Shape: (9, patch_length)
        
        # Create mask indices with new configuration
        mask_config = self.config.get_mask_config()
        mask_indices = create_mask_indices(
            self.config.patch_length, 
            mask_config['n_mask_points'],
            mask_config['mask_type'],
            mask_config['block_size'],
            mask_config['n_blocks']
        )
        
        # Extract target values (original values at masked positions)
        target = patch[:, mask_indices]  # Shape: (9, n_mask_points)
        
        # Apply mask to create input
        input_patch = apply_mask(patch, mask_indices)
        
        # Apply transform if provided
        if self.transform:
            input_patch = self.transform(input_patch)
            target = self.transform(target)
        
        # Convert to tensors
        sample = {
            'input': torch.FloatTensor(input_patch),
            'target': torch.FloatTensor(target),
            'mask_indices': torch.LongTensor(mask_indices),
            'file_idx': torch.LongTensor([self.file_indices[idx]])
        }
        
        return sample

class MCGDataModule:
    """
    Data module for managing MCG datasets and dataloaders
    """
    
    def __init__(self, config: Config):
        """
        Initialize data module
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.train_files, self.test_files = config.get_data_paths()
        
        logger.info(f"Training files: {len(self.train_files)}")
        logger.info(f"Testing files: {len(self.test_files)}")
        
        # Create datasets for each channel group
        self.train_datasets = []
        self.test_datasets = []
        
        for group_idx in range(4):
            train_dataset = MCGDataset(
                self.train_files, 
                config, 
                group_idx=group_idx
            )
            test_dataset = MCGDataset(
                self.test_files, 
                config, 
                group_idx=group_idx
            )
            
            self.train_datasets.append(train_dataset)
            self.test_datasets.append(test_dataset)
    
    def get_train_loader(self, group_idx: int = 0) -> DataLoader:
        """
        Get training dataloader for specific channel group
        
        Args:
            group_idx: Channel group index (0-3)
            
        Returns:
            DataLoader for training
        """
        return DataLoader(
            self.train_datasets[group_idx],
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory,
            drop_last=True
        )
    
    def get_test_loader(self, group_idx: int = 0) -> DataLoader:
        """
        Get test dataloader for specific channel group
        
        Args:
            group_idx: Channel group index (0-3)
            
        Returns:
            DataLoader for testing
        """
        return DataLoader(
            self.test_datasets[group_idx],
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory,
            drop_last=False
        )
    
    def get_dataset_stats(self) -> Dict:
        """Get dataset statistics"""
        stats = {
            'train_files': len(self.train_files),
            'test_files': len(self.test_files),
            'groups': {}
        }
        
        for group_idx in range(4):
            stats['groups'][group_idx] = {
                'train_patches': len(self.train_datasets[group_idx]),
                'test_patches': len(self.test_datasets[group_idx]),
                'channels': self.config.channel_groups[group_idx]
            }
        
        return stats

def create_data_transforms() -> Dict[str, callable]:
    """
    Create data transforms for augmentation (optional)
    
    Returns:
        Dictionary of transform functions
    """
    def normalize_transform(patch: np.ndarray) -> np.ndarray:
        """Normalize patch to zero mean and unit variance"""
        return (patch - np.mean(patch, axis=1, keepdims=True)) / (np.std(patch, axis=1, keepdims=True) + 1e-8)
    
    def add_noise_transform(patch: np.ndarray, noise_level: float = 0.01) -> np.ndarray:
        """Add small amount of Gaussian noise"""
        noise = np.random.normal(0, noise_level, patch.shape)
        return patch + noise
    
    return {
        'normalize': normalize_transform,
        'add_noise': add_noise_transform
    }

def test_dataset(config: Config, group_idx: int = 0, n_samples: int = 5):
    """
    Test dataset functionality
    
    Args:
        config: Configuration object
        group_idx: Channel group to test
        n_samples: Number of samples to test
    """
    import matplotlib.pyplot as plt
    
    # Create data module
    data_module = MCGDataModule(config)
    
    # Get training loader
    train_loader = data_module.get_train_loader(group_idx)
    
    # Test a few samples
    print(f"\nTesting Dataset for Group {group_idx}")
    print(f"Dataset size: {len(data_module.train_datasets[group_idx])}")
    
    # Get first batch
    batch = next(iter(train_loader))
    
    print(f"Batch input shape: {batch['input'].shape}")
    print(f"Batch target shape: {batch['target'].shape}")
    print(f"Batch mask_indices shape: {batch['mask_indices'].shape}")
    
    # Visualize first few samples
    fig, axes = plt.subplots(n_samples, 2, figsize=(12, 2*n_samples))
    
    for i in range(min(n_samples, batch['input'].shape[0])):
        # Original patch (reconstruct from input and target)
        input_patch = batch['input'][i].numpy()
        target_values = batch['target'][i].numpy()
        mask_indices = batch['mask_indices'][i].numpy()
        
        # Reconstruct original
        original_patch = input_patch.copy()
        original_patch[:, mask_indices] = target_values
        
        # Plot first channel
        axes[i, 0].plot(original_patch[0], 'b-', label='Original')
        axes[i, 0].plot(input_patch[0], 'r--', label='Masked')
        axes[i, 0].set_title(f'Sample {i+1} - Channel 1')
        axes[i, 0].legend()
        
        # Plot mask positions
        axes[i, 1].plot(original_patch[0], 'b-', alpha=0.5)
        axes[i, 1].scatter(mask_indices, original_patch[0, mask_indices], 
                          c='red', s=20, label='Masked points')
        axes[i, 1].set_title(f'Sample {i+1} - Mask positions')
        axes[i, 1].legend()
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    stats = data_module.get_dataset_stats()
    print("\nDataset Statistics:")
    for group_idx, group_stats in stats['groups'].items():
        print(f"Group {group_idx}: {group_stats['train_patches']} train, {group_stats['test_patches']} test patches")

if __name__ == "__main__":
    # Test the dataset
    set_seed(42)
    
    # Create config
    config = Config()
    config.patch_length = 1024
    config.batch_size = 4
    
    # Test dataset
    test_dataset(config, group_idx=0, n_samples=3)