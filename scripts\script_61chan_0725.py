"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: script_61chan_0725
date: 2025/7/25 16:17
desc: 
"""

import numpy as np
import matplotlib.pyplot as plt
from vmdpy import VMD  # 核心 VMD库
import os
import glob
import pandas as pd
from scipy import signal

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题


def load_data(file_path):
    try:
        try:
            # 首先尝试使用默认分隔符
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            # 如果失败，尝试使用逗号分隔符
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')

        # 检查是否需要调整形状
        if data.shape[0] != 60000 or data.shape[1] != 61:
            if data.size == 60000 * 61:
                data = data.T

        return data
    except Exception as e:
        return None


def plot_data(data, channels, time_length, title):
    plt.figure(figsize=(10, 6))
    for channel in channels:
        plt.plot(data[:time_length, channel], label=f'Channel {channel}')
    plt.title(title)
    plt.xlabel('Time')
    plt.ylabel('Amplitude')
    plt.legend()
    plt.show()


if __name__ == "__main__":
    file_path = 'files/第三代数据61channel/970静息_原始数据.txt'
    data1 = load_data(file_path).T  # shape (120000，70)   采样率

    # 前8个是参考 中61通道 9-69  后1心电无    # 可视化数据函数 可配置可视化 通道list 以及 时间长度 在一个长图中
    plot_data( data=data1, channels=[5, 6, 7, 8, 9], time_length=time_length = 15000)
