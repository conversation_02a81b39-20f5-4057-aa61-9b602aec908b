"""
Training script for Noise2Sim MCG denoising
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import os
from typing import Tuple, List, Dict, Optional
import logging

from .config import Config
from .dataset import MCGDataModule
from .lazy_dataset import create_mcg_data_module
from .model import create_model, UNet1D, Noise2SimLoss
from .utils import (
    set_seed, 
    save_checkpoint, 
    load_checkpoint, 
    count_parameters,
    get_device,
    setup_logging
)
from .visualization import Noise2SimVisualizer, create_enhanced_evaluation_plots
from .parallel_trainer import train_model_parallel
from .single_trainer import train_single_model

logger = logging.getLogger(__name__)

class Trainer:
    """
    Trainer class for Noise2Sim model
    """
    
    def __init__(self, config: Config):
        """
        Initialize trainer
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.device = get_device()
        
        # Setup logging
        setup_logging(config.log_dir)
        
        # Set random seed
        set_seed(42)
        
        # Initialize data module (lazy or regular based on config)
        self.data_module = create_mcg_data_module(config)
        
        # Initialize models for each channel group
        self.models = {}
        self.optimizers = {}
        self.schedulers = {}
        self.loss_functions = {}
        
        for group_idx in range(4):
            model, loss_fn = create_model(config)
            model = model.to(self.device)
            
            optimizer = optim.Adam(
                model.parameters(),
                lr=config.learning_rate,
                weight_decay=1e-5
            )
            
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                patience=10,
                factor=0.5,
                verbose=True
            )
            
            self.models[group_idx] = model
            self.optimizers[group_idx] = optimizer
            self.schedulers[group_idx] = scheduler
            self.loss_functions[group_idx] = loss_fn
        
        # Initialize tensorboard writer
        self.writer = SummaryWriter(config.log_dir)
        
        # Initialize visualizer
        self.visualizer = Noise2SimVisualizer(config.sampling_rate)
        
        # Training statistics
        self.train_losses = {i: [] for i in range(4)}
        self.val_losses = {i: [] for i in range(4)}
        self.best_losses = {i: float('inf') for i in range(4)}
        
        logger.info(f"Trainer initialized with {len(self.models)} models")
        logger.info(f"Device: {self.device}")
        
        # Log model info
        model_info = self.models[0].get_model_info()
        logger.info(f"Model parameters: {model_info['total_parameters']:,}")
    
    def train_epoch(self, group_idx: int, epoch: int) -> float:
        """
        Train one epoch for a specific channel group
        
        Args:
            group_idx: Channel group index
            epoch: Current epoch number
            
        Returns:
            Average training loss
        """
        model = self.models[group_idx]
        optimizer = self.optimizers[group_idx]
        loss_fn = self.loss_functions[group_idx]
        
        model.train()
        train_loader = self.data_module.get_train_loader(group_idx)
        
        running_loss = 0.0
        num_batches = 0
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1} - Group {group_idx+1}")
        
        for batch_idx, batch in enumerate(pbar):
            # Move to device
            inputs = batch['input'].to(self.device)
            targets = batch['target'].to(self.device)
            mask_indices = batch['mask_indices'].to(self.device)
            
            # Zero gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            
            # Compute loss
            loss = loss_fn(outputs, targets, mask_indices)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            # Update parameters
            optimizer.step()
            
            # Update statistics
            running_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f'{loss.item():.6f}',
                'avg_loss': f'{running_loss/num_batches:.6f}'
            })
            
            # Log to tensorboard
            global_step = epoch * len(train_loader) + batch_idx
            self.writer.add_scalar(f'Loss/Train_Group_{group_idx}', loss.item(), global_step)
        
        avg_loss = running_loss / num_batches
        return avg_loss
    
    def validate_epoch(self, group_idx: int, epoch: int) -> float:
        """
        Validate one epoch for a specific channel group
        
        Args:
            group_idx: Channel group index
            epoch: Current epoch number
            
        Returns:
            Average validation loss
        """
        model = self.models[group_idx]
        loss_fn = self.loss_functions[group_idx]
        
        model.eval()
        test_loader = self.data_module.get_test_loader(group_idx)
        
        running_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in test_loader:
                # Move to device
                inputs = batch['input'].to(self.device)
                targets = batch['target'].to(self.device)
                mask_indices = batch['mask_indices'].to(self.device)
                
                # Forward pass
                outputs = model(inputs)
                
                # Compute loss
                loss = loss_fn(outputs, targets, mask_indices)
                
                # Update statistics
                running_loss += loss.item()
                num_batches += 1
        
        avg_loss = running_loss / num_batches
        
        # Log to tensorboard
        self.writer.add_scalar(f'Loss/Val_Group_{group_idx}', avg_loss, epoch)
        
        return avg_loss
    
    def save_model(self, group_idx: int, epoch: int, loss: float, is_best: bool = False):
        """
        Save model checkpoint
        
        Args:
            group_idx: Channel group index
            epoch: Current epoch
            loss: Current loss
            is_best: Whether this is the best model so far
        """
        checkpoint_path = os.path.join(
            self.config.checkpoint_dir,
            f'model_group_{group_idx}_epoch_{epoch}.pth'
        )
        
        save_checkpoint(
            self.models[group_idx],
            self.optimizers[group_idx],
            epoch,
            loss,
            checkpoint_path
        )
        
        if is_best:
            best_path = os.path.join(
                self.config.checkpoint_dir,
                f'best_model_group_{group_idx}.pth'
            )
            save_checkpoint(
                self.models[group_idx],
                self.optimizers[group_idx],
                epoch,
                loss,
                best_path
            )
    
    def generate_evaluation_samples(self, group_idx: int, epoch: int, n_samples: int = 5):
        """
        Generate enhanced evaluation samples for visual inspection
        
        Args:
            group_idx: Channel group index
            epoch: Current epoch
            n_samples: Number of samples to generate (randomly selected)
        """
        model = self.models[group_idx]
        model.eval()
        
        vis_config = self.config.get_visualization_config()
        
        # Get test and training samples
        test_loader = self.data_module.get_test_loader(group_idx)
        train_loader = self.data_module.get_train_loader(group_idx)
        
        # Collect multiple batches to randomly sample from
        test_samples = []
        train_samples = []
        
        # Collect test samples
        for i, test_batch in enumerate(test_loader):
            test_samples.append(test_batch)
            if i >= 5:  # Collect from first few batches
                break
        
        # Randomly select test samples
        import random
        all_test_inputs = torch.cat([batch['input'] for batch in test_samples], dim=0)
        all_test_targets = torch.cat([batch['target'] for batch in test_samples], dim=0)
        all_test_mask_indices = torch.cat([batch['mask_indices'] for batch in test_samples], dim=0)
        
        # Random indices for test samples
        test_indices = random.sample(range(len(all_test_inputs)), min(n_samples, len(all_test_inputs)))
        test_inputs = all_test_inputs[test_indices].to(self.device)
        test_targets = all_test_targets[test_indices].to(self.device)
        test_mask_indices = all_test_mask_indices[test_indices].to(self.device)
        
        # Get training batch if requested
        train_inputs = None
        train_targets = None
        train_mask_indices = None
        
        if vis_config['show_train_samples']:
            # Collect training samples
            for i, train_batch in enumerate(train_loader):
                train_samples.append(train_batch)
                if i >= 5:  # Collect from first few batches
                    break
            
            # Randomly select training samples
            all_train_inputs = torch.cat([batch['input'] for batch in train_samples], dim=0)
            all_train_targets = torch.cat([batch['target'] for batch in train_samples], dim=0)
            all_train_mask_indices = torch.cat([batch['mask_indices'] for batch in train_samples], dim=0)
            
            # Random indices for training samples
            train_indices = random.sample(range(len(all_train_inputs)), min(n_samples, len(all_train_inputs)))
            train_inputs = all_train_inputs[train_indices].to(self.device)
            train_targets = all_train_targets[train_indices].to(self.device)
            train_mask_indices = all_train_mask_indices[train_indices].to(self.device)
        
        with torch.no_grad():
            test_outputs = model(test_inputs)
            train_outputs = model(train_inputs) if train_inputs is not None else None
        
        # Move to CPU for visualization
        test_inputs = test_inputs.cpu().numpy()
        test_outputs = test_outputs.cpu().numpy()
        test_targets = test_targets.cpu().numpy()
        test_mask_indices = test_mask_indices.cpu().numpy()
        
        if train_inputs is not None:
            train_inputs = train_inputs.cpu().numpy()
            train_outputs = train_outputs.cpu().numpy()
            train_targets = train_targets.cpu().numpy()
            train_mask_indices = train_mask_indices.cpu().numpy()
        
        # Create enhanced visualizations for each sample
        for i in range(n_samples):
            # Reconstruct original signals from input and targets
            test_original = test_inputs[i].copy()
            test_original[:, test_mask_indices[i]] = test_targets[i]
            
            train_original = None
            train_denoised = None
            if train_inputs is not None:
                train_original = train_inputs[i].copy()
                train_original[:, train_mask_indices[i]] = train_targets[i]
                train_denoised = train_outputs[i]
            
            # Create comprehensive visualization
            save_dir = os.path.join(
                self.config.log_dir,
                f'evaluation_group_{group_idx}_epoch_{epoch}_sample_{i}'
            )
            
            create_enhanced_evaluation_plots(
                original=test_original,
                denoised=test_outputs[i],
                mask_indices=test_mask_indices[i],
                train_original=train_original,
                train_denoised=train_denoised,
                save_dir=save_dir,
                sampling_rate=self.config.sampling_rate
            )
        
        logger.info(f"Enhanced evaluation samples saved for group {group_idx}, epoch {epoch}")
        
    def generate_training_progress_plot(self):
        """Generate training progress plot for all groups"""
        save_path = os.path.join(self.config.log_dir, 'training_progress.png')
        self.visualizer.plot_training_progress(
            self.train_losses,
            self.val_losses,
            save_path=save_path
        )
    
    def plot_training_curves(self):
        """Plot training curves for all groups"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        for group_idx in range(4):
            ax = axes[group_idx]
            
            epochs = range(1, len(self.train_losses[group_idx]) + 1)
            
            ax.plot(epochs, self.train_losses[group_idx], 'b-', label='Training Loss')
            ax.plot(epochs, self.val_losses[group_idx], 'r-', label='Validation Loss')
            
            ax.set_title(f'Group {group_idx + 1} Training Curves')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Loss')
            ax.legend()
            ax.grid(True)
        
        plt.tight_layout()
        
        # Save figure
        save_path = os.path.join(self.config.log_dir, 'training_curves.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Training curves saved to {save_path}")
    
    def train(self):
        """
        Main training loop
        """
        logger.info("Starting training...")
        logger.info(f"Configuration:\n{self.config.summary()}")
        
        start_time = time.time()
        
        try:
            # Train each group sequentially
            for group_idx in range(4):
                logger.info(f"\nTraining Group {group_idx + 1}/4")
                logger.info(f"Channels: {self.config.channel_groups[group_idx]}")
                
                group_start_time = time.time()
                
                for epoch in range(self.config.num_epochs):
                    # Train epoch
                    train_loss = self.train_epoch(group_idx, epoch)
                    
                    # Validate epoch
                    val_loss = self.validate_epoch(group_idx, epoch)
                    
                    # Update learning rate
                    self.schedulers[group_idx].step(val_loss)
                    
                    # Update statistics
                    self.train_losses[group_idx].append(train_loss)
                    self.val_losses[group_idx].append(val_loss)
                    
                    # Check if best model
                    is_best = val_loss < self.best_losses[group_idx]
                    if is_best:
                        self.best_losses[group_idx] = val_loss
                    
                    # Save model
                    if (epoch + 1) % self.config.save_every == 0:
                        self.save_model(group_idx, epoch, val_loss, is_best)
                    
                    # Generate evaluation samples
                    if (epoch + 1) % self.config.eval_every == 0:
                        self.generate_evaluation_samples(group_idx, epoch)
                    
                    # Log progress
                    logger.info(f"Epoch {epoch+1}/{self.config.num_epochs} - "
                              f"Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
                    
                    # Log to tensorboard
                    self.writer.add_scalar(f'Loss/Train_Avg_Group_{group_idx}', train_loss, epoch)
                    self.writer.add_scalar(f'Loss/Val_Avg_Group_{group_idx}', val_loss, epoch)
                    
                    # Log learning rate
                    current_lr = self.optimizers[group_idx].param_groups[0]['lr']
                    self.writer.add_scalar(f'LR/Group_{group_idx}', current_lr, epoch)
                
                # Save final model
                self.save_model(group_idx, self.config.num_epochs - 1, val_loss, is_best)
                
                group_time = time.time() - group_start_time
                logger.info(f"Group {group_idx + 1} training completed in {group_time/60:.2f} minutes")
                logger.info(f"Best validation loss: {self.best_losses[group_idx]:.6f}")
        
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
        
        except Exception as e:
            logger.error(f"Training failed with error: {e}")
            raise
        
        finally:
            # Plot training curves
            self.generate_training_progress_plot()
            
            # Close tensorboard writer
            self.writer.close()
            
            total_time = time.time() - start_time
            logger.info(f"Training completed in {total_time/60:.2f} minutes")
            
            # Print final statistics
            logger.info("\nFinal Results:")
            for group_idx in range(4):
                logger.info(f"Group {group_idx + 1} - Best validation loss: {self.best_losses[group_idx]:.6f}")

def train_model(config: Config):
    """
    Train Noise2Sim model
    
    Args:
        config: Configuration object
    """
    if config.single_model:
        # Use single model training
        train_single_model(config)
    elif config.parallel_groups:
        # Use parallel training for 4 separate models
        train_model_parallel(config)
    else:
        # Use sequential training
        trainer = Trainer(config)
        trainer.train()

if __name__ == "__main__":
    # Create configuration
    config = Config()
    
    # Override some parameters for testing
    config.num_epochs = 2
    config.batch_size = 16
    config.save_every = 1
    config.eval_every = 1
    
    # Start training
    train_model(config)