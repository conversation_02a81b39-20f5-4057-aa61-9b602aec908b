#!/usr/bin/env python3
"""
Main entry point for Noise2Sim MCG denoising
"""

import argparse
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from noise2sim.config import Config
from noise2sim.train import train_model
from noise2sim.inference import Noise2SimInference, load_best_models
from noise2sim.evaluation import evaluate_denoising
from noise2sim.utils import setup_logging, load_mcg_data

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Noise2Sim: Unsupervised MCG Denoising",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train model
  python main.py train --config config.yaml --epochs 100

  # Denoise a single file
  python main.py denoise --input data.txt --output denoised.txt

  # Batch denoise multiple files
  python main.py batch --input-dir data/ --output-dir denoised/

  # Evaluate denoising performance
  python main.py evaluate --original data.txt --denoised denoised.txt
        """
    )
    
    # Add subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Train command
    train_parser = subparsers.add_parser('train', help='Train Noise2Sim model')
    train_parser.add_argument('--config', type=str, help='Configuration file path')
    train_parser.add_argument('--data-dir', type=str, help='Data directory')
    train_parser.add_argument('--epochs', type=int, default=150, help='Number of epochs')
    train_parser.add_argument('--batch-size', type=int, default=128, help='Batch size')
    train_parser.add_argument('--lr', type=float, default=0.0004, help='Learning rate')
    train_parser.add_argument('--patch-length', type=int, default=256, #choices=[1024, 2048,4096],
                             help='Patch length')
    train_parser.add_argument('--resume', type=str, help='Resume from checkpoint')
    train_parser.add_argument('--single-model', action='store_true', 
                             help='Train single shared model using data from all 4 groups')
    
    # Denoise command
    denoise_parser = subparsers.add_parser('denoise', help='Denoise a single file')
    denoise_parser.add_argument('--input', type=str, required=True, help='Input file path')
    denoise_parser.add_argument('--output', type=str, required=True, help='Output file path')
    denoise_parser.add_argument('--models', type=str, help='Directory containing trained models')
    denoise_parser.add_argument('--config', type=str, help='Configuration file path')
    
    # Batch denoise command
    batch_parser = subparsers.add_parser('batch', help='Batch denoise multiple files')
    batch_parser.add_argument('--input-dir', type=str, required=True, help='Input directory')
    batch_parser.add_argument('--output-dir', type=str, required=True, help='Output directory')
    batch_parser.add_argument('--models', type=str, help='Directory containing trained models')
    batch_parser.add_argument('--config', type=str, help='Configuration file path')
    batch_parser.add_argument('--pattern', type=str, default='*.txt', help='File pattern')
    
    # Evaluate command
    evaluate_parser = subparsers.add_parser('evaluate', help='Evaluate denoising performance')
    evaluate_parser.add_argument('--original', type=str, required=True, help='Original signal file')
    evaluate_parser.add_argument('--denoised', type=str, required=True, help='Denoised signal file')
    evaluate_parser.add_argument('--output-dir', type=str, help='Output directory for results')
    evaluate_parser.add_argument('--sampling-rate', type=int, default=1000, help='Sampling rate')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Test the complete pipeline')
    test_parser.add_argument('--data-dir', type=str, help='Data directory')
    test_parser.add_argument('--quick', action='store_true', help='Quick test with reduced parameters')
    
    # Parse arguments
    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        return
    
    # Setup logging
    log_dir = "logs/noise2sim"
    os.makedirs(log_dir, exist_ok=True)
    setup_logging(log_dir)
    
    logger = logging.getLogger(__name__)
    logger.info(f"Noise2Sim started with command: {args.command}")
    
    try:
        if args.command == 'train':
            run_training(args)
        elif args.command == 'denoise':
            run_denoising(args)
        elif args.command == 'batch':
            run_batch_denoising(args)
        elif args.command == 'evaluate':
            run_evaluation(args)
        elif args.command == 'test':
            run_test(args)
        else:
            parser.print_help()
    
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

def run_training(args):
    """Run training"""
    logger = logging.getLogger(__name__)
    logger.info("Starting training...")
    
    # Create configuration
    config = Config()
    
    # Override with command line arguments
    if args.data_dir:
        config.data_dir = args.data_dir
    if args.epochs:
        config.num_epochs = args.epochs
    if args.batch_size:
        config.batch_size = args.batch_size
    if args.lr:
        config.learning_rate = args.lr
    if args.patch_length:
        config.patch_length = args.patch_length
    if hasattr(args, 'single_model') and args.single_model:
        config.single_model = True
        # Update configuration after changing single_model
        if config.single_model:
            config.input_channels = 9
            config.parallel_groups = False
            logger.info("Single model mode enabled: training one shared model using data from all 4 groups")
    
    # Print configuration
    logger.info(f"Training configuration:\n{config.summary()}")
    
    # Start training
    train_model(config)
    
    logger.info("Training completed successfully!")

def run_denoising(args):
    """Run denoising on a single file"""
    logger = logging.getLogger(__name__)
    logger.info(f"Denoising file: {args.input}")
    
    # Create configuration
    config = Config()
    
    # Load models
    if args.models:
        model_paths = {}
        for group_idx in range(4):
            model_path = os.path.join(args.models, f'best_model_group_{group_idx}.pth')
            if os.path.exists(model_path):
                model_paths[group_idx] = model_path
    else:
        model_paths = load_best_models(config)
    
    if not model_paths:
        logger.error("No trained models found. Please train models first or specify model directory.")
        return
    
    # Create inference pipeline
    inference = Noise2SimInference(config, model_paths)
    
    # Denoise file
    try:
        original, denoised = inference.denoise_file(args.input, args.output)
        logger.info(f"Denoising completed. Output saved to: {args.output}")
        
        # Print basic statistics
        residual = original - denoised
        logger.info(f"Residual statistics:")
        logger.info(f"  Mean: {np.mean(residual):.6f}")
        logger.info(f"  Std: {np.std(residual):.6f}")
        logger.info(f"  Max: {np.max(np.abs(residual)):.6f}")
        
    except Exception as e:
        logger.error(f"Denoising failed: {e}")
        raise

def run_batch_denoising(args):
    """Run batch denoising"""
    logger = logging.getLogger(__name__)
    logger.info(f"Batch denoising: {args.input_dir} -> {args.output_dir}")
    
    # Create configuration
    config = Config()
    
    # Load models
    if args.models:
        model_paths = {}
        for group_idx in range(4):
            model_path = os.path.join(args.models, f'best_model_group_{group_idx}.pth')
            if os.path.exists(model_path):
                model_paths[group_idx] = model_path
    else:
        model_paths = load_best_models(config)
    
    if not model_paths:
        logger.error("No trained models found. Please train models first or specify model directory.")
        return
    
    # Create inference pipeline
    inference = Noise2SimInference(config, model_paths)
    
    # Run batch denoising
    try:
        results = inference.batch_denoise(args.input_dir, args.output_dir, args.pattern)
        logger.info(f"Batch denoising completed. Processed {len(results)} files.")
        
        # Print results
        for input_file, output_file in results.items():
            logger.info(f"  {os.path.basename(input_file)} -> {os.path.basename(output_file)}")
            
    except Exception as e:
        logger.error(f"Batch denoising failed: {e}")
        raise

def run_evaluation(args):
    """Run evaluation"""
    logger = logging.getLogger(__name__)
    logger.info(f"Evaluating: {args.original} vs {args.denoised}")
    
    # Load signals
    try:
        original_signal, _ = load_mcg_data(args.original)
        denoised_signal, _ = load_mcg_data(args.denoised)
        
        if original_signal.shape != denoised_signal.shape:
            logger.error(f"Shape mismatch: original {original_signal.shape}, denoised {denoised_signal.shape}")
            return
        
        # Run evaluation
        output_dir = args.output_dir or f"evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        results = evaluate_denoising(
            original_signal,
            denoised_signal,
            save_dir=output_dir,
            sampling_rate=args.sampling_rate
        )
        
        # Print summary
        logger.info("Evaluation Summary:")
        logger.info(f"  SNR Improvement: {results['signal_quality']['snr_improvement_db']:.2f} dB")
        logger.info(f"  Correlation: {results['signal_quality']['correlation']:.6f}")
        logger.info(f"  RMSE: {results['signal_quality']['rmse']:.6f}")
        logger.info(f"  Results saved to: {output_dir}")
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        raise

def run_test(args):
    """Run pipeline test"""
    logger = logging.getLogger(__name__)
    logger.info("Running pipeline test...")
    
    # Create test configuration
    config = Config()
    
    if args.data_dir:
        config.data_dir = args.data_dir
    
    if args.quick:
        config.num_epochs = 2
        config.batch_size = 16
        config.patch_length = 1024
    
    # Test data loading
    logger.info("Testing data loading...")
    try:
        from noise2sim.dataset import MCGDataModule
        data_module = MCGDataModule(config)
        stats = data_module.get_dataset_stats()
        logger.info(f"Dataset statistics: {stats}")
    except Exception as e:
        logger.error(f"Data loading test failed: {e}")
        return
    
    # Test model creation
    logger.info("Testing model creation...")
    try:
        from noise2sim.model import create_model
        model, loss_fn = create_model(config)
        model_info = model.get_model_info()
        logger.info(f"Model created with {model_info['total_parameters']:,} parameters")
    except Exception as e:
        logger.error(f"Model creation test failed: {e}")
        return
    
    # Test training (quick)
    if args.quick:
        logger.info("Testing training (quick)...")
        try:
            train_model(config)
            logger.info("Training test completed")
        except Exception as e:
            logger.error(f"Training test failed: {e}")
            return
    
    # Test inference
    logger.info("Testing inference...")
    try:
        # Get test file
        train_files, test_files = config.get_data_paths()
        if test_files:
            test_file = test_files[0]
            
            # Load models
            model_paths = load_best_models(config)
            
            if model_paths:
                inference = Noise2SimInference(config, model_paths)
                original, denoised = inference.denoise_file(test_file)
                logger.info(f"Inference test completed. Signal shape: {original.shape}")
            else:
                logger.warning("No trained models found for inference test")
        else:
            logger.warning("No test files found for inference test")
            
    except Exception as e:
        logger.error(f"Inference test failed: {e}")
        return
    
    logger.info("Pipeline test completed successfully!")

if __name__ == "__main__":
    main()