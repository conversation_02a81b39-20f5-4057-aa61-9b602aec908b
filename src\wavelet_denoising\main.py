"""
小波降噪实验框架主脚本
Author: Assistant
Date: 2025-07-17
Description: 模块化的小波降噪实验平台，支持多种算法对比和评估
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from pathlib import Path
import logging
from typing import Dict,List
# 添加当前目录到Python路径
# current_dir = os.path.dirname(os.path.abspath(__file__))
# sys.path.insert(0, current_dir)
# current_dir = os.path.abspath('')
# sys.path.insert(0, current_dir)

# 导入自定义模块
# 自动检测和修正导入路径
try:
    from data_loader import load_data, extract_channels, segment_data, preprocess_signals, visualize_data_overview, get_channel_groups
    from denoising_algorithms import (
        denoise_baseline_wavelet, denoise_svd, denoise_bivariate_shrinkage,
        denoise_dtcwt, denoise_residual_vmd, denoise_iterative_wavelet,
        compare_all_methods
    )
    from evaluation import DenoiseEvaluator, plot_wave_comparison, plot_spectrum_comparison
    from report_generator import ReportGenerator
except ImportError:
    from src.wavelet_denoising.data_loader import load_data, extract_channels, segment_data, preprocess_signals, visualize_data_overview, get_channel_groups
    from src.wavelet_denoising.denoising_algorithms import (
        denoise_baseline_wavelet, denoise_svd, denoise_bivariate_shrinkage,
        denoise_dtcwt, denoise_residual_vmd, denoise_iterative_wavelet,
        compare_all_methods
    )
    from src.wavelet_denoising.evaluation import DenoiseEvaluator, plot_wave_comparison, plot_spectrum_comparison
    from src.wavelet_denoising.report_generator import ReportGenerator

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 配置区域 ====================
# 可以直接修改这些变量来改变实验参数，无需复杂配置文件

# 1. 数据配置 - 自动路径检测
def find_data_file():
    """自动查找数据文件"""
    possible_paths = [
        "files/降噪北京301的标准源2025-7-14/20250711标准源/PLAG_2025_000147.tdms_L.txt",
        "../../files/降噪北京301的标准源2025-7-14/20250711标准源/PLAG_2025_000147.tdms_L.txt",
        "../../../files/降噪北京301的标准源2025-7-14/20250711标准源/PLAG_2025_000147.tdms_L.txt"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return possible_paths[0]  # 默认返回第一个

FILE_PATH = find_data_file()
START_TIME = 0.0         # 开始时间（秒）
DURATION = 10.0          # 持续时间（秒）
SAMPLING_RATE = 1000     # 采样率（Hz）

# 2. 通道选择配置
SELECTED_CHANNELS = [ 2, 3, 11, 15]  # 选择要处理的信号通道索引（0-based，最多36个）
USE_ALL_CHANNELS = False             # 如果True，则处理所有36个通道

# 3. 预处理配置
PREPROCESSING_METHOD = 'none'  # 'none', 'standardize', 'normalize', 'demean'

# 4. 降噪方法选择（选择要测试的方法）
METHODS_TO_TEST = {
    'baseline_wavelet': True,      # 标准小波基准
    'svd': False,                   # SVD多通道降噪
    'bivariate_shrinkage': False,   # 双变量收缩
    'dtcwt': False,                 # 双树复小波
    'residual_vmd': True,          # 小波+残差VMD
    'iterative_wavelet': True      # 迭代小波
}

# 5. 算法参数配置
ALGORITHM_PARAMS = {
    'baseline_wavelet': {
        'wavelet_type': 'sym8',
        'level': 6,
        'threshold_method': 'soft',
        'threshold_mode': 'signal_adaptive',
        'threshold_scale': 0.35
    },
    'svd': {
        'k': None  # None表示自动选择
    },
    'bivariate_shrinkage': {
        'wavelet_type': 'db8',
        'level': 6
    },
    'dtcwt': {
        'level': 6,
        'threshold_scale': 0.35
    },
    'residual_vmd': {
        'wavelet_type': 'db8',
        'vmd_k': 5,
        'vmd_alpha': 2000
    },
    'iterative_wavelet': {
        'iterations': 3,
        'wavelet_type': 'db8'
    }
}

# 6. 可视化配置
SHOW_DATA_OVERVIEW = True        # 显示数据概览
SHOW_INDIVIDUAL_PROCESS = True   # 显示每个方法的处理过程
SHOW_COMPARISON = True           # 显示方法对比
SHOW_EVALUATION = True           # 显示综合评估

# 7. 输出配置
SAVE_RESULTS = False             # 是否保存结果到文件
GENERATE_REPORT = True           # 是否生成详细报告
OUTPUT_DIR = "../../output/wavelet_denoising"

# ==================== 主要功能函数 ====================

def load_and_prepare_data():
    """加载和准备数据"""
    logger.info("开始加载和准备数据...")
    
    # 检查文件是否存在
    if not os.path.exists(FILE_PATH):
        logger.error(f"数据文件不存在: {FILE_PATH}")
        return None, None, None
    
    # 加载数据
    data = load_data(FILE_PATH)
    if data is None:
        return None, None, None
    
    # 提取通道
    idx_column, signal_channels, reference_channels = extract_channels(data)
    
    # 截取数据段
    signal_segment = segment_data(signal_channels, START_TIME, DURATION, SAMPLING_RATE)
    reference_segment = segment_data(reference_channels, START_TIME, DURATION, SAMPLING_RATE)
    
    # 预处理
    processed_signals = preprocess_signals(signal_segment, PREPROCESSING_METHOD)
    processed_references = preprocess_signals(reference_segment, PREPROCESSING_METHOD)
    
    logger.info(f"数据准备完成: 信号{processed_signals.shape}, 参考{processed_references.shape}")
    
    # 可视化数据概览
    if SHOW_DATA_OVERVIEW:
        visualize_data_overview(processed_signals, processed_references, SAMPLING_RATE)
    
    return processed_signals, processed_references, signal_segment

def select_channels_for_processing(signals):
    """选择要处理的通道"""
    if USE_ALL_CHANNELS:
        selected_signals = signals
        selected_indices = list(range(signals.shape[1]))
    else:
        # 确保选择的通道在有效范围内
        valid_channels = [ch for ch in SELECTED_CHANNELS if ch < signals.shape[1]]
        if not valid_channels:
            logger.warning("没有有效的通道选择，使用前5个通道")
            valid_channels = list(range(min(5, signals.shape[1])))
        
        selected_signals = signals[:, valid_channels]
        selected_indices = valid_channels
    
    logger.info(f"选择处理通道: {selected_indices}")
    return selected_signals, selected_indices

def apply_denoising_methods(signals):
    """应用所选的降噪方法"""
    logger.info("开始应用降噪方法...")
    
    results = {}
    
    # 对每个通道分别处理（单通道方法）
    for ch_idx in range(signals.shape[1]):
        signal = signals[:, ch_idx]
        ch_results = {}
        
        logger.info(f"处理通道 {ch_idx+1}/{signals.shape[1]}")
        
        # 基准小波降噪
        if METHODS_TO_TEST.get('baseline_wavelet', False):
            try:
                params = ALGORITHM_PARAMS['baseline_wavelet']
                denoised = denoise_baseline_wavelet(
                    signal, 
                    wavelet_type=params['wavelet_type'],
                    level=params['level'],
                    threshold_method=params['threshold_method'],
                    threshold_mode=params['threshold_mode'],
                    threshold_scale=params.get('threshold_scale', None),
                    visualize=SHOW_INDIVIDUAL_PROCESS
                )
                ch_results['Baseline Wavelet'] = denoised
                logger.info(f"通道{ch_idx} - 基准小波降噪完成")
            except Exception as e:
                logger.error(f"基准小波降噪失败: {e}")
        
        # 双变量收缩降噪
        if METHODS_TO_TEST.get('bivariate_shrinkage', False):
            try:
                params = ALGORITHM_PARAMS['bivariate_shrinkage']
                denoised = denoise_bivariate_shrinkage(
                    signal,
                    wavelet_type=params['wavelet_type'],
                    level=params['level'],
                    visualize=SHOW_INDIVIDUAL_PROCESS
                )
                ch_results['Bivariate Shrinkage'] = denoised
                logger.info(f"通道{ch_idx} - 双变量收缩降噪完成")
            except Exception as e:
                logger.error(f"双变量收缩降噪失败: {e}")
        
        # DTCWT降噪
        if METHODS_TO_TEST.get('dtcwt', False):
            try:
                params = ALGORITHM_PARAMS['dtcwt']
                denoised = denoise_dtcwt(
                    signal,
                    level=params['level'],
                    threshold_scale=params.get('threshold_scale', None),
                    visualize=SHOW_INDIVIDUAL_PROCESS
                )
                ch_results['DTCWT'] = denoised
                logger.info(f"通道{ch_idx} - DTCWT降噪完成")
            except Exception as e:
                logger.error(f"DTCWT降噪失败: {e}")
        
        # 小波+残差VMD降噪
        if METHODS_TO_TEST.get('residual_vmd', False):
            try:
                params = ALGORITHM_PARAMS['residual_vmd']
                denoised = denoise_residual_vmd(
                    signal,
                    wavelet_type=params['wavelet_type'],
                    vmd_k=params['vmd_k'],
                    vmd_alpha=params['vmd_alpha'],
                    visualize=SHOW_INDIVIDUAL_PROCESS
                )
                ch_results['Residual VMD'] = denoised
                logger.info(f"通道{ch_idx} - 残差VMD降噪完成")
            except Exception as e:
                logger.error(f"残差VMD降噪失败: {e}")
        
        # 迭代小波降噪
        if METHODS_TO_TEST.get('iterative_wavelet', False):
            try:
                params = ALGORITHM_PARAMS['iterative_wavelet']
                denoised = denoise_iterative_wavelet(
                    signal,
                    iterations=params['iterations'],
                    wavelet_type=params['wavelet_type'],
                    threshold_scale=params.get('threshold_scale', None),
                    visualize=SHOW_INDIVIDUAL_PROCESS
                )
                ch_results['Iterative Wavelet'] = denoised
                logger.info(f"通道{ch_idx} - 迭代小波降噪完成")
            except Exception as e:
                logger.error(f"迭代小波降噪失败: {e}")
        
        results[ch_idx] = ch_results
    
    # SVD多通道降噪（需要所有通道一起处理）
    if METHODS_TO_TEST.get('svd', False):
        try:
            params = ALGORITHM_PARAMS['svd']
            denoised_multichannel = denoise_svd(
                signals,
                k=params['k'],
                visualize=SHOW_INDIVIDUAL_PROCESS
            )
            
            # 将结果分配到各个通道
            for ch_idx in range(signals.shape[1]):
                if ch_idx not in results:
                    results[ch_idx] = {}
                results[ch_idx]['SVD'] = denoised_multichannel[:, ch_idx]
            
            logger.info("SVD多通道降噪完成")
        except Exception as e:
            logger.error(f"SVD降噪失败: {e}")
    
    return results

def comprehensive_evaluation(original_signals, denoised_results, reference_signals, selected_indices):
    """综合评估所有方法"""
    logger.info("开始综合评估...")
    
    # 初始化评估器
    evaluator = DenoiseEvaluator(sampling_rate=SAMPLING_RATE)
    
    # 获取通道分组信息
    channel_groups = get_channel_groups()
    
    # 调整通道分组以匹配选择的通道
    adjusted_channel_groups = {}
    for ref_idx, channels in channel_groups.items():
        adjusted_channels = []
        for ch in channels:
            if (ch-1) in selected_indices:  # 转换为0-based索引
                adjusted_channels.append(selected_indices.index(ch-1))
        if adjusted_channels:
            adjusted_channel_groups[ref_idx] = adjusted_channels
    
    # 获取所有方法名称
    all_methods = set()
    for ch_results in denoised_results.values():
        all_methods.update(ch_results.keys())
    
    all_methods = list(all_methods)
    logger.info(f"评估方法: {all_methods}")
    
    # 为每种方法构建多通道结果
    evaluation_results = []
    
    for method in all_methods:
        # 构建该方法的多通道降噪结果
        method_results = []
        for ch_idx in range(original_signals.shape[1]):
            if ch_idx in denoised_results and method in denoised_results[ch_idx]:
                method_results.append(denoised_results[ch_idx][method])
            else:
                # 如果某通道没有该方法的结果，使用原始信号
                method_results.append(original_signals[:, ch_idx])
        
        method_denoised = np.column_stack(method_results)
        
        # 评估该方法
        try:
            evaluation_result = evaluator.comprehensive_evaluation(
                original_signals, method_denoised, reference_signals,
                adjusted_channel_groups, method
            )
            evaluation_results.append(evaluation_result)
        except Exception as e:
            logger.error(f"评估方法 {method} 失败: {e}")
    
    # 可视化评估结果
    if SHOW_EVALUATION and evaluation_results:
        evaluator.visualize_evaluation_results(evaluation_results)
    
    return evaluation_results

def visualize_method_comparison(original_signals, denoised_results, reference_signals, selected_indices):
    """可视化方法对比"""
    if not SHOW_COMPARISON:
        return
    
    logger.info("生成方法对比可视化...")
    
    # 选择第一个通道进行详细对比
    if original_signals.shape[1] > 0:
        ch_idx = 0
        original_signal = original_signals[:, ch_idx]
        reference_signal = reference_signals[:, 0] if reference_signals.shape[1] > 0 else None
        
        # 获取该通道所有方法的结果
        ch_results = denoised_results.get(ch_idx, {})
        
        if ch_results:
            # 创建对比图
            n_methods = len(ch_results)
            fig, axes = plt.subplots(n_methods + 2, 1, figsize=(15, 3*(n_methods + 2)))
            
            time_axis = np.arange(len(original_signal)) / SAMPLING_RATE
            
            # 原始信号
            axes[0].plot(time_axis, original_signal, 'b-', alpha=0.7)
            axes[0].set_title(f'Original Signal - Channel {selected_indices[ch_idx]+1}')
            axes[0].set_ylabel('Amplitude')
            axes[0].grid(True)
            
            # 参考信号
            if reference_signal is not None:
                axes[1].plot(time_axis, reference_signal, 'g-', alpha=0.7)
                axes[1].set_title('Reference Signal')
                axes[1].set_ylabel('Amplitude')
                axes[1].grid(True)
            else:
                axes[1].text(0.5, 0.5, 'No Reference Signal', ha='center', va='center', transform=axes[1].transAxes)
                axes[1].set_title('Reference Signal (N/A)')
            
            # 各种降噪方法
            for i, (method, denoised) in enumerate(ch_results.items()):
                axes[i+2].plot(time_axis, denoised, 'r-', alpha=0.8)
                axes[i+2].set_title(f'{method} - Denoised')
                axes[i+2].set_ylabel('Amplitude')
                axes[i+2].grid(True)
            
            axes[-1].set_xlabel('Time (s)')
            plt.tight_layout()
            plt.show()
            
            # 频谱对比
            for method, denoised in ch_results.items():
                plot_spectrum_comparison(original_signal, denoised, SAMPLING_RATE, 
                                       f'{method} - Channel {selected_indices[ch_idx]+1}')

def save_results_to_file(denoised_results, selected_indices):
    """保存结果到文件"""
    if not SAVE_RESULTS:
        return
    
    try:
        # 创建输出目录
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        
        # 保存每个方法的结果
        for method in set().union(*[ch_results.keys() for ch_results in denoised_results.values()]):
            output_file = os.path.join(OUTPUT_DIR, f"{method.replace(' ', '_').lower()}_results.txt")
            
            # 构建该方法的多通道数据
            method_data = []
            for ch_idx in range(len(selected_indices)):
                if ch_idx in denoised_results and method in denoised_results[ch_idx]:
                    method_data.append(denoised_results[ch_idx][method])
            
            if method_data:
                # 转置以匹配原始数据格式 (samples, channels)
                method_array = np.column_stack(method_data)
                
                # 添加索引列
                idx_column = np.arange(method_array.shape[0]).reshape(-1, 1)
                output_array = np.hstack([idx_column, method_array])
                
                # 保存
                np.savetxt(output_file, output_array, fmt='%.6f', delimiter='\t')
                logger.info(f"保存 {method} 结果到: {output_file}")
        
    except Exception as e:
        logger.error(f"保存结果失败: {e}")

def generate_detailed_report(experiment_data: Dict, evaluation_results: List[Dict]):
    """生成详细的实验报告"""
    try:
        # 创建报告生成器
        report_generator = ReportGenerator(output_dir=OUTPUT_DIR)
        
        # 生成报告
        report_path = report_generator.generate_experiment_report(
            experiment_data, evaluation_results
        )
        
        print(f"\n📊 详细实验报告已生成: {report_path}")
        print("报告包含：")
        print("- 算法原理详解")
        print("- 图表解读指南")
        print("- 参数调优建议")
        print("- 应用场景分析")
        
    except Exception as e:
        logger.error(f"生成报告失败: {e}")

def main():
    """主函数"""
    print("="*80)
    print("小波降噪实验框架")
    print("="*80)
    
    # 打印配置信息
    print(f"数据文件: {FILE_PATH}")
    print(f"时间段: {START_TIME}s - {START_TIME + DURATION}s")
    print(f"采样率: {SAMPLING_RATE} Hz")
    print(f"预处理方法: {PREPROCESSING_METHOD}")
    print(f"选择的方法: {[method for method, enabled in METHODS_TO_TEST.items() if enabled]}")
    print("-"*80)
    
    try:
        # 1. 加载和准备数据
        processed_signals, processed_references, original_signals = load_and_prepare_data()
        if processed_signals is None:
            logger.error("数据加载失败，程序退出")
            return
        
        # 2. 选择处理通道
        selected_signals, selected_indices = select_channels_for_processing(processed_signals)
        selected_references = processed_references  # 参考信号全部使用
        selected_originals = original_signals[:, selected_indices] if original_signals is not None else selected_signals
        
        # 3. 应用降噪方法
        denoised_results = apply_denoising_methods(selected_signals)
        
        if not denoised_results:
            logger.error("没有成功的降噪结果")
            return
        
        # 4. 可视化方法对比
        visualize_method_comparison(selected_originals, denoised_results, 
                                  selected_references, selected_indices)
        
        # 5. 综合评估
        evaluation_results = comprehensive_evaluation(selected_originals, denoised_results,
                                                    selected_references, selected_indices)
        
        # 6. 保存结果
        save_results_to_file(denoised_results, selected_indices)
        
        # 7. 生成详细报告
        if GENERATE_REPORT:
            generate_detailed_report(experiment_data={
                'file_path': FILE_PATH,
                'start_time': START_TIME,
                'duration': DURATION,
                'sampling_rate': SAMPLING_RATE,
                'selected_channels': selected_indices,
                'preprocessing_method': PREPROCESSING_METHOD,
                'tested_methods': [method for method, enabled in METHODS_TO_TEST.items() if enabled],
                'algorithm_params': ALGORITHM_PARAMS,
                'python_version': sys.version,
                'signal_length': len(selected_originals)
            }, evaluation_results=evaluation_results)
        
        print("\n" + "="*80)
        print("实验完成！")
        print("="*80)
        
        # 打印简要总结
        if evaluation_results:
            print("\n性能总结 (按综合得分排序):")
            sorted_results = sorted(evaluation_results, 
                                  key=lambda x: x.get('overall_score', 0), 
                                  reverse=True)
            for i, result in enumerate(sorted_results[:3]):  # 显示前3名
                method = result['method_name']
                score = result.get('overall_score', 0)
                correlation = result.get('correlation_metrics', {}).get('overall_mean_correlation', 0)
                print(f"{i+1}. {method}: 综合得分={score:.3f}, 平均相关性={correlation:.3f}")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()

# ==================== 快速测试函数 ====================

def quick_test_single_method(method_name='baseline_wavelet', channel_idx=0):
    """快速测试单个方法"""
    print(f"快速测试: {method_name}")
    
    # 简化配置
    global METHODS_TO_TEST, SELECTED_CHANNELS, SHOW_INDIVIDUAL_PROCESS
    METHODS_TO_TEST = {method_name: True}
    SELECTED_CHANNELS = [channel_idx]
    SHOW_INDIVIDUAL_PROCESS = True
    
    # 运行主程序
    main()

def compare_top_methods():
    """对比最佳方法"""
    print("对比最佳方法")
    
    # 启用推荐的方法
    global METHODS_TO_TEST
    METHODS_TO_TEST = {
        'baseline_wavelet': True,
        'bivariate_shrinkage': True, 
        'iterative_wavelet': True,
        'residual_vmd': True
    }
    
    # 运行主程序
    main()

if __name__ == "__main__":
    # 正常运行模式
    main()
    
    # 取消注释以下行进行快速测试
    # quick_test_single_method('baseline_wavelet', 0)
    # compare_top_methods()