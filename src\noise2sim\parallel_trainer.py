"""
Multi-GPU parallel training for Noise2Sim MCG denoising
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Tuple, List, Dict, Optional
import logging

from .config import Config
from .lazy_dataset import create_mcg_data_module
from .model import create_model, UNet1D, Noise2SimLoss
from .utils import (
    set_seed, 
    save_checkpoint, 
    load_checkpoint, 
    count_parameters,
    get_device,
    setup_logging
)
from .visualization import Noise2SimVisualizer, create_enhanced_evaluation_plots

logger = logging.getLogger(__name__)

class ParallelGroupTrainer:
    """
    Single group trainer for parallel execution
    """
    
    def __init__(self, config: Config, group_idx: int, device: torch.device):
        """
        Initialize single group trainer
        
        Args:
            config: Configuration object
            group_idx: Channel group index (0-3)
            device: GPU device for this group
        """
        self.config = config
        self.group_idx = group_idx
        self.device = device
        
        # Create model and optimizer
        self.model, self.loss_fn = create_model(config)
        self.model = self.model.to(device)
        
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-5
        )
        
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            patience=10,
            factor=0.5,
            verbose=True
        )
        
        # Training statistics
        self.train_losses = []
        self.val_losses = []
        self.best_loss = float('inf')
        
        # Data loaders will be set by parent trainer
        self.train_loader = None
        self.test_loader = None
        
        logger.info(f"Group {group_idx} trainer initialized on device {device}")
    
    def set_data_loaders(self, train_loader: DataLoader, test_loader: DataLoader):
        """Set data loaders for this group"""
        self.train_loader = train_loader
        self.test_loader = test_loader
    
    def train_epoch(self, epoch: int) -> float:
        """
        Train one epoch for this group
        
        Args:
            epoch: Current epoch number
            
        Returns:
            Average training loss
        """
        self.model.train()
        running_loss = 0.0
        num_batches = 0
        
        pbar = tqdm(self.train_loader, desc=f"Group {self.group_idx+1} Epoch {epoch+1}", 
                   position=self.group_idx, leave=False)
        
        for batch_idx, batch in enumerate(pbar):
            # Move to device
            inputs = batch['input'].to(self.device)
            targets = batch['target'].to(self.device)
            mask_indices = batch['mask_indices'].to(self.device)
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(inputs)
            
            # Compute loss
            loss = self.loss_fn(outputs, targets, mask_indices)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # Update parameters
            self.optimizer.step()
            
            # Update statistics
            running_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f'{loss.item():.6f}',
                'avg_loss': f'{running_loss/num_batches:.6f}'
            })
        
        avg_loss = running_loss / num_batches
        self.train_losses.append(avg_loss)
        return avg_loss
    
    def validate_epoch(self, epoch: int) -> float:
        """
        Validate one epoch for this group
        
        Args:
            epoch: Current epoch number
            
        Returns:
            Average validation loss
        """
        self.model.eval()
        running_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in self.test_loader:
                # Move to device
                inputs = batch['input'].to(self.device)
                targets = batch['target'].to(self.device)
                mask_indices = batch['mask_indices'].to(self.device)
                
                # Forward pass
                outputs = self.model(inputs)
                
                # Compute loss
                loss = self.loss_fn(outputs, targets, mask_indices)
                
                # Update statistics
                running_loss += loss.item()
                num_batches += 1
        
        avg_loss = running_loss / num_batches
        self.val_losses.append(avg_loss)
        return avg_loss
    
    def save_model(self, epoch: int, loss: float, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint_path = os.path.join(
            self.config.checkpoint_dir,
            f'model_group_{self.group_idx}_epoch_{epoch}.pth'
        )
        
        save_checkpoint(
            self.model,
            self.optimizer,
            epoch,
            loss,
            checkpoint_path
        )
        
        if is_best:
            best_path = os.path.join(
                self.config.checkpoint_dir,
                f'best_model_group_{self.group_idx}.pth'
            )
            save_checkpoint(
                self.model,
                self.optimizer,
                epoch,
                loss,
                best_path
            )
    
    def generate_evaluation_samples(self, epoch: int, n_samples: int = 2):
        """Generate evaluation samples for this group"""
        self.model.eval()
        
        # Get test batch
        test_batch = next(iter(self.test_loader))
        test_inputs = test_batch['input'][:n_samples].to(self.device)
        test_targets = test_batch['target'][:n_samples].to(self.device)
        test_mask_indices = test_batch['mask_indices'][:n_samples].to(self.device)
        
        with torch.no_grad():
            test_outputs = self.model(test_inputs)
        
        # Move to CPU for visualization
        test_inputs = test_inputs.cpu().numpy()
        test_outputs = test_outputs.cpu().numpy()
        test_targets = test_targets.cpu().numpy()
        test_mask_indices = test_mask_indices.cpu().numpy()
        
        # Create visualization for one sample
        if len(test_outputs) > 0:
            # Reconstruct original signal
            test_original = test_inputs[0].copy()
            test_original[:, test_mask_indices[0]] = test_targets[0]
            
            # Create comprehensive visualization
            save_dir = os.path.join(
                self.config.log_dir,
                f'parallel_evaluation_group_{self.group_idx}_epoch_{epoch}'
            )
            
            create_enhanced_evaluation_plots(
                original=test_original,
                denoised=test_outputs[0],
                mask_indices=test_mask_indices[0],
                save_dir=save_dir,
                sampling_rate=self.config.sampling_rate
            )
        
        logger.info(f"Group {self.group_idx} evaluation samples generated for epoch {epoch}")

class ParallelTrainer:
    """
    Multi-GPU parallel trainer for Noise2Sim model
    """
    
    def __init__(self, config: Config):
        """
        Initialize parallel trainer
        
        Args:
            config: Configuration object
        """
        self.config = config
        
        # Setup logging
        setup_logging(config.log_dir)
        
        # Set random seed
        set_seed(42)
        
        # Check GPU availability
        if not torch.cuda.is_available():
            raise RuntimeError("CUDA not available for parallel training")
        
        num_gpus = torch.cuda.device_count()
        if num_gpus < 4:
            logger.warning(f"Only {num_gpus} GPUs available, but 4 groups need training")
        
        # Initialize data module
        self.data_module = create_mcg_data_module(config)
        
        # Initialize group trainers
        self.group_trainers = {}
        self.devices = []
        for group_idx in range(4):
            # Assign GPU device (cycle through available GPUs)
            device_idx = group_idx % num_gpus
            device = torch.device(f'cuda:{device_idx}')
            self.devices.append(device)
            # Create group trainer
            group_trainer = ParallelGroupTrainer(config, group_idx, device)
             # Set data loaders
            train_loader = self.data_module.get_train_loader(group_idx)
            test_loader = self.data_module.get_test_loader(group_idx)
            group_trainer.set_data_loaders(train_loader, test_loader)
            self.group_trainers[group_idx] = group_trainer
        
        # Initialize tensorboard writer
        self.writer = SummaryWriter(config.log_dir)
        
        # Initialize visualizer
        self.visualizer = Noise2SimVisualizer(config.sampling_rate)
        
        logger.info(f"Parallel trainer initialized")
        logger.info(f"Using {num_gpus} GPUs for 4 groups")
        logger.info(f"Device assignment: {self.devices}")
        # Log model info
        model_info = self.group_trainers[0].model.get_model_info()
        logger.info(f"Model parameters per group: {model_info['total_parameters']:,}")
    
    def train_epoch_parallel(self, epoch: int) -> Dict[int, float]:
        """
        Train one epoch for all groups in parallel
        
        Args:
            epoch: Current epoch number
            
        Returns:
            Dictionary of training losses for each group
        """
        train_losses = {}
        
        # Use ThreadPoolExecutor for parallel training
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit training tasks for all groups
            future_to_group = {
                executor.submit(trainer.train_epoch, epoch): group_idx
                for group_idx, trainer in self.group_trainers.items()
            }
            
            # Collect results
            for future in as_completed(future_to_group):
                group_idx = future_to_group[future]
                try:
                    loss = future.result()
                    train_losses[group_idx] = loss
                    logger.info(f"Group {group_idx} training loss: {loss:.6f}")
                except Exception as e:
                    logger.error(f"Group {group_idx} training failed: {e}")
                    train_losses[group_idx] = float('inf')
        
        return train_losses
    
    def validate_epoch_parallel(self, epoch: int) -> Dict[int, float]:
        """
        Validate one epoch for all groups in parallel
        
        Args:
            epoch: Current epoch number
            
        Returns:
            Dictionary of validation losses for each group
        """
        val_losses = {}
        
        # Use ThreadPoolExecutor for parallel validation
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit validation tasks for all groups
            future_to_group = {
                executor.submit(trainer.validate_epoch, epoch): group_idx
                for group_idx, trainer in self.group_trainers.items()
            }
            
            # Collect results
            for future in as_completed(future_to_group):
                group_idx = future_to_group[future]
                try:
                    loss = future.result()
                    val_losses[group_idx] = loss
                    logger.info(f"Group {group_idx} validation loss: {loss:.6f}")
                except Exception as e:
                    logger.error(f"Group {group_idx} validation failed: {e}")
                    val_losses[group_idx] = float('inf')
        
        return val_losses
    
    def save_models(self, epoch: int, val_losses: Dict[int, float]):
        """Save models for all groups"""
        for group_idx, trainer in self.group_trainers.items():
            val_loss = val_losses.get(group_idx, float('inf'))
            is_best = val_loss < trainer.best_loss
            
            if is_best:
                trainer.best_loss = val_loss
            
            if (epoch + 1) % self.config.save_every == 0:
                trainer.save_model(epoch, val_loss, is_best)
    
    def generate_evaluation_samples_parallel(self, epoch: int):
        """Generate evaluation samples for all groups in parallel"""
        if (epoch + 1) % self.config.eval_every == 0:
            with ThreadPoolExecutor(max_workers=4) as executor:
                # Submit evaluation tasks for all groups
                futures = [
                    executor.submit(trainer.generate_evaluation_samples, epoch)
                    for trainer in self.group_trainers.values()
                ]
                
                # Wait for completion
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"Evaluation generation failed: {e}")
    
    def generate_training_progress_plot(self):
        """Generate training progress plot for all groups"""
        train_losses = {i: trainer.train_losses for i, trainer in self.group_trainers.items()}
        val_losses = {i: trainer.val_losses for i, trainer in self.group_trainers.items()}
        
        save_path = os.path.join(self.config.log_dir, 'parallel_training_progress.png')
        self.visualizer.plot_training_progress(train_losses, val_losses, save_path=save_path)
    
    def train(self):
        """Main parallel training loop"""
        logger.info("Starting parallel training...")
        logger.info(f"Configuration:\\n{self.config.summary()}")
        
        start_time = time.time()
        
        try:
            for epoch in range(self.config.num_epochs):
                epoch_start_time = time.time()
                
                # Train all groups in parallel
                train_losses = self.train_epoch_parallel(epoch)
                
                # Validate all groups in parallel
                val_losses = self.validate_epoch_parallel(epoch)
                
                # Update learning rate schedulers
                for group_idx, trainer in self.group_trainers.items():
                    val_loss = val_losses.get(group_idx, float('inf'))
                    trainer.scheduler.step(val_loss)
                
                # Save models
                self.save_models(epoch, val_losses)
                
                # Generate evaluation samples
                self.generate_evaluation_samples_parallel(epoch)
                
                # Log to tensorboard
                for group_idx in range(4):
                    train_loss = train_losses.get(group_idx, float('inf'))
                    val_loss = val_losses.get(group_idx, float('inf'))
                    
                    self.writer.add_scalar(f'Loss/Train_Group_{group_idx}', train_loss, epoch)
                    self.writer.add_scalar(f'Loss/Val_Group_{group_idx}', val_loss, epoch)
                    
                    # Log learning rate
                    current_lr = self.group_trainers[group_idx].optimizer.param_groups[0]['lr']
                    self.writer.add_scalar(f'LR/Group_{group_idx}', current_lr, epoch)
                
                # Log epoch summary
                epoch_time = time.time() - epoch_start_time
                avg_train_loss = np.mean(list(train_losses.values()))
                avg_val_loss = np.mean(list(val_losses.values()))
                
                logger.info(f"Epoch {epoch+1}/{self.config.num_epochs} - "
                          f"Avg Train Loss: {avg_train_loss:.6f}, "
                          f"Avg Val Loss: {avg_val_loss:.6f}, "
                          f"Time: {epoch_time:.2f}s")
        
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
        
        except Exception as e:
            logger.error(f"Training failed with error: {e}")
            raise
        
        finally:
            # Generate final training progress plot
            self.generate_training_progress_plot()
            
            # Close tensorboard writer
            self.writer.close()
            
            total_time = time.time() - start_time
            logger.info(f"Parallel training completed in {total_time/60:.2f} minutes")
            
            # Print final statistics
            logger.info("\\nFinal Results:")
            for group_idx, trainer in self.group_trainers.items():
                logger.info(f"Group {group_idx + 1} - Best validation loss: {trainer.best_loss:.6f}")

def train_model_parallel(config: Config):
    """
    Train Noise2Sim model using parallel training
    
    Args:
        config: Configuration object
    """
    trainer = ParallelTrainer(config)
    trainer.train()

if __name__ == "__main__":
    # Test parallel training
    config = Config()
    config.num_epochs = 2
    config.batch_size = 32
    config.parallel_groups = True
    
    train_model_parallel(config)