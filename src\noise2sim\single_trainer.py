"""
Single model trainer for Noise2Sim MCG denoising
Trains one shared model using data from all 4 channel groups
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import os
from typing import Tuple, List, Dict, Optional
import logging

from .config import Config
from .lazy_dataset import create_mcg_data_module
from .model import create_model, UNet1D, Noise2SimLoss
from .utils import (
    set_seed, 
    save_checkpoint, 
    load_checkpoint, 
    count_parameters,
    get_device,
    setup_logging
)
from .visualization import Noise2SimVisualizer, create_enhanced_evaluation_plots

logger = logging.getLogger(__name__)

class SingleModelTrainer:
    """
    Single model trainer for Noise2Sim
    Trains one shared 9ch→9ch model using data from all 4 groups
    """
    
    def __init__(self, config: Config):
        """
        Initialize single model trainer
        
        Args:
            config: Configuration object
        """
        self.config = config
        
        # Validate single model mode
        if not config.single_model:
            raise ValueError("SingleModelTrainer requires config.single_model=True")
        
        # Setup logging
        setup_logging(config.log_dir)
        
        # Set random seed
        set_seed(42)
        
        # Get device
        self.device = get_device()
        
        # Initialize data module
        self.data_module = create_mcg_data_module(config)
        
        # Create model and optimizer
        self.model, self.loss_fn = create_model(config)
        self.model = self.model.to(self.device)
        
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-5
        )
        
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            patience=10,
            factor=0.5,
            verbose=True
        )
        
        # Get data loaders (single mixed loader)
        self.train_loader = self.data_module.get_train_loader(0)  # group_idx doesn't matter for single model
        self.test_loader = self.data_module.get_test_loader(0)
        
        # Training statistics
        self.train_losses = []
        self.val_losses = []
        self.best_loss = float('inf')
        
        # Initialize tensorboard writer
        self.writer = SummaryWriter(config.log_dir)
        
        # Initialize visualizer
        self.visualizer = Noise2SimVisualizer(config.sampling_rate)
        
        logger.info(f"Single model trainer initialized")
        logger.info(f"Device: {self.device}")
        
        # Log model info
        model_info = self.model.get_model_info()
        logger.info(f"Model parameters: {model_info['total_parameters']:,}")
        
        # Log dataset info
        stats = self.data_module.get_dataset_stats()
        logger.info(f"Training samples: {stats['mixed_data']['train_samples']:,}")
        logger.info(f"Test samples: {stats['mixed_data']['test_samples']:,}")
    
    def train_epoch(self, epoch: int) -> float:
        """
        Train one epoch
        
        Args:
            epoch: Current epoch number
            
        Returns:
            Average training loss
        """
        self.model.train()
        running_loss = 0.0
        num_batches = 0
        
        # Track group distribution
        group_counts = {0: 0, 1: 0, 2: 0, 3: 0}
        
        pbar = tqdm(self.train_loader, desc=f"Single Model Epoch {epoch+1}")
        
        for batch_idx, batch in enumerate(pbar):
            # Move to device
            inputs = batch['input'].to(self.device)
            targets = batch['target'].to(self.device)
            mask_indices = batch['mask_indices'].to(self.device)
            group_indices = batch['group_idx'].to(self.device)
            
            # Track group distribution
            for group_idx in group_indices.flatten():
                group_counts[group_idx.item()] += 1
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(inputs)
            
            # Compute loss
            loss = self.loss_fn(outputs, targets, mask_indices)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # Update parameters
            self.optimizer.step()
            
            # Update statistics
            running_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f'{loss.item():.6f}',
                'avg_loss': f'{running_loss/num_batches:.6f}',
                'groups': f"{group_counts[0]}/{group_counts[1]}/{group_counts[2]}/{group_counts[3]}"
            })
        
        avg_loss = running_loss / num_batches
        self.train_losses.append(avg_loss)
        
        # Log group distribution
        total_samples = sum(group_counts.values())
        group_percentages = {k: (v/total_samples)*100 for k, v in group_counts.items()}
        logger.info(f"Epoch {epoch+1} group distribution: " + 
                   ", ".join([f"G{k}: {v:.1f}%" for k, v in group_percentages.items()]))
        
        return avg_loss
    
    def validate_epoch(self, epoch: int) -> float:
        """
        Validate one epoch
        
        Args:
            epoch: Current epoch number
            
        Returns:
            Average validation loss
        """
        self.model.eval()
        running_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in self.test_loader:
                # Move to device
                inputs = batch['input'].to(self.device)
                targets = batch['target'].to(self.device)
                mask_indices = batch['mask_indices'].to(self.device)
                
                # Forward pass
                outputs = self.model(inputs)
                
                # Compute loss
                loss = self.loss_fn(outputs, targets, mask_indices)
                
                # Update statistics
                running_loss += loss.item()
                num_batches += 1
        
        avg_loss = running_loss / num_batches
        self.val_losses.append(avg_loss)
        return avg_loss
    
    def save_model(self, epoch: int, loss: float, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint_path = os.path.join(
            self.config.checkpoint_dir,
            f'single_model_epoch_{epoch}.pth'
        )
        
        save_checkpoint(
            self.model,
            self.optimizer,
            epoch,
            loss,
            checkpoint_path
        )
        
        if is_best:
            best_path = os.path.join(
                self.config.checkpoint_dir,
                'best_single_model.pth'
            )
            save_checkpoint(
                self.model,
                self.optimizer,
                epoch,
                loss,
                best_path
            )
    
    def generate_evaluation_samples(self, epoch: int, n_samples: int = 5):
        """Generate evaluation samples from randomly selected test data"""
        self.model.eval()
        
        vis_config = self.config.get_visualization_config()
        
        # Collect multiple batches to randomly sample from
        test_samples = []
        train_samples = []
        
        # Collect test samples
        for i, test_batch in enumerate(self.test_loader):
            test_samples.append(test_batch)
            if i >= 5:  # Collect from first few batches
                break
        
        # Randomly select test samples
        import random
        all_test_inputs = torch.cat([batch['input'] for batch in test_samples], dim=0)
        all_test_targets = torch.cat([batch['target'] for batch in test_samples], dim=0)
        all_test_mask_indices = torch.cat([batch['mask_indices'] for batch in test_samples], dim=0)
        all_test_group_indices = torch.cat([batch['group_idx'] for batch in test_samples], dim=0)
        
        # Random indices for test samples
        test_indices = random.sample(range(len(all_test_inputs)), min(n_samples, len(all_test_inputs)))
        test_inputs = all_test_inputs[test_indices].to(self.device)
        test_targets = all_test_targets[test_indices].to(self.device)
        test_mask_indices = all_test_mask_indices[test_indices].to(self.device)
        test_group_indices = all_test_group_indices[test_indices].to(self.device)
        
        # Get training samples if requested
        train_inputs = None
        train_targets = None
        train_mask_indices = None
        train_group_indices = None
        
        if vis_config.get('show_train_samples', False):
            # Collect training samples
            for i, train_batch in enumerate(self.train_loader):
                train_samples.append(train_batch)
                if i >= 5:  # Collect from first few batches
                    break
            
            # Randomly select training samples
            all_train_inputs = torch.cat([batch['input'] for batch in train_samples], dim=0)
            all_train_targets = torch.cat([batch['target'] for batch in train_samples], dim=0)
            all_train_mask_indices = torch.cat([batch['mask_indices'] for batch in train_samples], dim=0)
            all_train_group_indices = torch.cat([batch['group_idx'] for batch in train_samples], dim=0)
            
            # Random indices for training samples
            train_indices = random.sample(range(len(all_train_inputs)), min(n_samples, len(all_train_inputs)))
            train_inputs = all_train_inputs[train_indices].to(self.device)
            train_targets = all_train_targets[train_indices].to(self.device)
            train_mask_indices = all_train_mask_indices[train_indices].to(self.device)
            train_group_indices = all_train_group_indices[train_indices].to(self.device)
        
        with torch.no_grad():
            test_outputs = self.model(test_inputs)
            train_outputs = self.model(train_inputs) if train_inputs is not None else None
        
        # Move to CPU for visualization
        test_inputs = test_inputs.cpu().numpy()
        test_outputs = test_outputs.cpu().numpy()
        test_targets = test_targets.cpu().numpy()
        test_mask_indices = test_mask_indices.cpu().numpy()
        test_group_indices = test_group_indices.cpu().numpy()
        
        if train_inputs is not None:
            train_inputs = train_inputs.cpu().numpy()
            train_outputs = train_outputs.cpu().numpy()
            train_targets = train_targets.cpu().numpy()
            train_mask_indices = train_mask_indices.cpu().numpy()
            train_group_indices = train_group_indices.cpu().numpy()
        
        # Create visualization for multiple samples
        for i in range(len(test_outputs)):
            # Reconstruct original test signal
            test_original = test_inputs[i].copy()
            test_original[:, test_mask_indices[i]] = test_targets[i]
            group_idx = test_group_indices[i, 0]
            
            # Prepare training data if available
            train_original = None
            train_denoised = None
            if train_inputs is not None and i < len(train_inputs):
                train_original = train_inputs[i].copy()
                train_original[:, train_mask_indices[i]] = train_targets[i]
                train_denoised = train_outputs[i]
            
            # Create comprehensive visualization
            save_dir = os.path.join(
                self.config.log_dir,
                f'single_model_evaluation_epoch_{epoch}_sample_{i}_group_{group_idx}'
            )
            
            create_enhanced_evaluation_plots(
                original=test_original,
                denoised=test_outputs[i],
                mask_indices=test_mask_indices[i],
                train_original=train_original,
                train_denoised=train_denoised,
                save_dir=save_dir,
                sampling_rate=self.config.sampling_rate
            )
        
        logger.info(f"Single model evaluation: {len(test_outputs)} samples generated for epoch {epoch}")
    
    def generate_training_progress_plot(self):
        """Generate training progress plot"""
        save_path = os.path.join(self.config.log_dir, 'single_model_training_progress.png')
        
        # Create progress plot for single model
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        epochs = range(1, len(self.train_losses) + 1)
        
        # Training and validation loss
        ax1.plot(epochs, self.train_losses, 'b-', label='Training Loss', linewidth=2)
        ax1.plot(epochs, self.val_losses, 'r-', label='Validation Loss', linewidth=2)
        ax1.set_title('Single Model Training Progress', fontsize=12)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Add best loss annotation
        if self.val_losses:
            best_epoch = np.argmin(self.val_losses) + 1
            best_loss = min(self.val_losses)
            ax1.annotate(f'Best: {best_loss:.4f}', 
                        xy=(best_epoch, best_loss),
                        xytext=(10, 10), textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                        arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        # Learning rate
        if hasattr(self.scheduler, '_last_lr'):
            lrs = [self.optimizer.param_groups[0]['lr']] * len(epochs)
            ax2.plot(epochs, lrs, 'g-', linewidth=2)
            ax2.set_title('Learning Rate', fontsize=12)
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('Learning Rate')
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"Training progress plot saved to {save_path}")
    
    def train(self):
        """Main training loop"""
        logger.info("Starting single model training...")
        logger.info(f"Configuration:\\n{self.config.summary()}")
        
        start_time = time.time()
        
        try:
            for epoch in range(self.config.num_epochs):
                epoch_start_time = time.time()
                
                # Train epoch
                train_loss = self.train_epoch(epoch)
                
                # Validate epoch
                val_loss = self.validate_epoch(epoch)
                
                # Update learning rate scheduler
                self.scheduler.step(val_loss)
                
                # Save model
                is_best = val_loss < self.best_loss
                if is_best:
                    self.best_loss = val_loss
                
                if (epoch + 1) % self.config.save_every == 0:
                    self.save_model(epoch, val_loss, is_best)
                
                # Generate evaluation samples
                if (epoch + 1) % self.config.eval_every == 0:
                    self.generate_evaluation_samples(epoch)
                
                # Log to tensorboard
                self.writer.add_scalar('Loss/Train', train_loss, epoch)
                self.writer.add_scalar('Loss/Val', val_loss, epoch)
                
                # Log learning rate
                current_lr = self.optimizer.param_groups[0]['lr']
                self.writer.add_scalar('LR', current_lr, epoch)
                
                # Log epoch summary
                epoch_time = time.time() - epoch_start_time
                
                logger.info(f"Epoch {epoch+1}/{self.config.num_epochs} - "
                          f"Train Loss: {train_loss:.6f}, "
                          f"Val Loss: {val_loss:.6f}, "
                          f"Time: {epoch_time:.2f}s")
        
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
        
        except Exception as e:
            logger.error(f"Training failed with error: {e}")
            raise
        
        finally:
            # Generate final training progress plot
            self.generate_training_progress_plot()
            
            # Close tensorboard writer
            self.writer.close()
            
            total_time = time.time() - start_time
            logger.info(f"Single model training completed in {total_time/60:.2f} minutes")
            logger.info(f"Best validation loss: {self.best_loss:.6f}")

def train_single_model(config: Config):
    """
    Train single Noise2Sim model
    
    Args:
        config: Configuration object with single_model=True
    """
    trainer = SingleModelTrainer(config)
    trainer.train()

if __name__ == "__main__":
    # Test single model training
    config = Config()
    config.single_model = True
    config.num_epochs = 2
    config.batch_size = 32
    
    train_single_model(config)