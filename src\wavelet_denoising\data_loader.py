"""
数据准备模块
Author: Assistant
Date: 2025-07-17
Description: 支持多通道数据加载、预处理和分段
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Optional, List
import logging

logger = logging.getLogger(__name__)

def load_data(file_path: str) -> np.ndarray:
    """
    加载数据文件，支持不同分隔符
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        data: 形状为 (samples, channels) 的数据数组
    """
    try:
        try:
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')
            
        logger.debug(f"成功加载数据文件: {file_path}, 形状: {data.shape}")
        return data
        
    except Exception as e:
        logger.error(f"读取文件出错: {e}")
        return None

def extract_channels(data: np.ndarray) -> <PERSON><PERSON>[np.ndarray, np.ndarray, np.ndarray]:
    """
    从41通道数据中分离信号通道和标准源通道
    
    Args:
        data: 原始数据 (samples, 41) - [idx, 36个信号通道, 4个标准源通道]
        
    Returns:
        idx_column: 索引列
        signal_channels: 36个信号通道 (samples, 36)  
        reference_channels: 4个标准源通道 (samples, 4)
    """
    if data.shape[1] != 41:
        raise ValueError(f"期望41通道数据，实际得到{data.shape[1]}通道")
    
    idx_column = data[:, 0]                # 第1列：索引
    signal_channels = data[:, 1:37]        # 第2-37列：36个信号通道
    reference_channels = data[:, 37:41]    # 第38-41列：4个标准源通道
    
    logger.debug(f"成功提取通道: 信号通道{signal_channels.shape}, 标准源通道{reference_channels.shape}")
    return idx_column, signal_channels, reference_channels

def segment_data(data: np.ndarray, start_time: float = 0.0, duration: float = 10.0, 
                 sampling_rate: int = 1000) -> np.ndarray:
    """
    从长时程数据中截取有代表性的片段
    
    Args:
        data: 输入数据 (samples, channels)
        start_time: 开始时间（秒）
        duration: 持续时间（秒）
        sampling_rate: 采样率（Hz）
        
    Returns:
        segmented_data: 截取的数据段
    """
    start_idx = int(start_time * sampling_rate)
    end_idx = int((start_time + duration) * sampling_rate)
    
    if end_idx > data.shape[0]:
        end_idx = data.shape[0]
        actual_duration = (end_idx - start_idx) / sampling_rate
        logger.warning(f"请求时长超出数据范围，调整为: {actual_duration:.2f}秒")
    
    segmented_data = data[start_idx:end_idx]
    logger.info(f"截取数据段: {start_time}s-{(end_idx-start_idx)/sampling_rate:.2f}s, 形状: {segmented_data.shape}")
    
    return segmented_data

def preprocess_signals(signals: np.ndarray, method: str = 'standardize') -> np.ndarray:
    """
    信号预处理
    
    Args:
        signals: 输入信号 (samples, channels)
        method: 预处理方法 ('standardize', 'normalize', 'demean', 'none')
        
    Returns:
        processed_signals: 预处理后的信号
    """
    if method == 'none':
        return signals
    
    processed_signals = signals.copy()
    
    for i in range(signals.shape[1]):
        signal = signals[:, i]
        
        if method == 'standardize':
            # 零均值，单位方差
            processed_signals[:, i] = (signal - np.mean(signal)) / np.std(signal)
        elif method == 'normalize':
            # 归一化到[0,1]
            signal_min, signal_max = np.min(signal), np.max(signal)
            if signal_max != signal_min:
                processed_signals[:, i] = (signal - signal_min) / (signal_max - signal_min)
        elif method == 'demean':
            # 仅去均值
            processed_signals[:, i] = signal - np.mean(signal)
    
    logger.info(f"信号预处理完成，方法: {method}")
    return processed_signals

def visualize_data_overview(signal_channels: np.ndarray, reference_channels: np.ndarray, 
                           sampling_rate: int = 1000, max_channels: int = 6):
    """
    可视化数据概览
    
    Args:
        signal_channels: 信号通道数据
        reference_channels: 标准源通道数据
        sampling_rate: 采样率
        max_channels: 最多显示的信号通道数
    """
    time_axis = np.arange(signal_channels.shape[0]) / sampling_rate
    
    fig, axes = plt.subplots(3, 1, figsize=(12, 10))
    
    # 绘制部分信号通道
    channels_to_plot = min(max_channels, signal_channels.shape[1])
    for i in range(channels_to_plot):
        axes[0].plot(time_axis, signal_channels[:, i], alpha=0.7, label=f'Channel {i+1}')
    axes[0].set_title('Signal Channels (Sample)')
    axes[0].set_xlabel('Time (s)')
    axes[0].set_ylabel('Amplitude')
    axes[0].legend()
    axes[0].grid(True)
    
    # 绘制标准源通道
    for i in range(reference_channels.shape[1]):
        axes[1].plot(time_axis, reference_channels[:, i], label=f'Reference {i+1}')
    axes[1].set_title('Reference Channels')
    axes[1].set_xlabel('Time (s)')
    axes[1].set_ylabel('Amplitude')
    axes[1].legend()
    axes[1].grid(True)
    
    # 绘制功率谱密度
    from scipy.signal import welch
    
    # 选择第一个信号通道和第一个标准源通道进行频谱分析
    freqs_signal, psd_signal = welch(signal_channels[:, 0], sampling_rate, nperseg=1024)
    freqs_ref, psd_ref = welch(reference_channels[:, 0], sampling_rate, nperseg=1024)
    
    axes[2].semilogy(freqs_signal, psd_signal, label='Signal Ch1')
    axes[2].semilogy(freqs_ref, psd_ref, label='Reference Ch1')
    axes[2].set_title('Power Spectral Density')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_ylabel('PSD')
    axes[2].legend()
    axes[2].grid(True)
    
    plt.tight_layout()
    plt.show()

def get_channel_groups() -> dict:
    """
    获取通道分组信息，用于后续相关性分析
    
    Returns:
        channel_groups: 通道分组字典，键为参考通道索引，值为对应的信号通道列表
    """
    return {
        0: list(range(1, 10)),   # 参考通道1对应信号通道1-9  
        1: list(range(10, 19)),  # 参考通道2对应信号通道10-18
        2: list(range(19, 28)),  # 参考通道3对应信号通道19-27
        3: list(range(28, 37))   # 参考通道4对应信号通道28-36
    }

# 示例使用
if __name__ == "__main__":
    # 示例：加载和预处理数据
    file_path = "../../files/降噪北京301的标准源2025-7-14/20250711标准源/PLAG_2025_000147.tdms_L.txt"
    
    # 加载数据
    data = load_data(file_path)
    if data is not None:
        # 提取通道
        idx, signals, references = extract_channels(data)
        
        # 截取前10秒数据
        signals_segment = segment_data(signals, start_time=0, duration=10)
        references_segment = segment_data(references, start_time=0, duration=10)
        
        # 预处理
        processed_signals = preprocess_signals(signals_segment, method='standardize')
        
        # 可视化
        visualize_data_overview(processed_signals, references_segment)
        
        print(f"数据处理完成")
        print(f"信号通道形状: {processed_signals.shape}")
        print(f"标准源通道形状: {references_segment.shape}")