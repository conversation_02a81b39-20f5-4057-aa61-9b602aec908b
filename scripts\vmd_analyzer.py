"""
Author: <PERSON><PERSON>
email: <EMAIL>
file: vmd_analyzer.py
date: 2025/6/9
desc:
61通道数据VMD分析工具，支持数据加载、VMD分解、频谱分析和结果可视化。
"""
import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
from vmdpy import VMD  # 核心 VMD库
import os
import glob
import pandas as pd
from scipy import signal

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
st.set_page_config(page_title="61通道数据VMD分析工具", layout="wide")

# 帮助信息
def show_help():
    st.markdown("""
    ### VMD参数说明
    - **alpha**: 平衡参数，控制带宽。较大的alpha会导致较窄的带宽。
    - **tau**: 双重上升时间步长，通常为0（噪声容忍度）或接近于0的小值。
    - **K**: 要恢复的模态数量。根据信号复杂性选择合适的值。
    - **DC**: 如果为True，则第一个模态将保持在DC（0频率）。
    - **init**: 初始化方式
        - 0: 所有omega从0开始
        - 1: 所有omega均匀分布
        - 2: 所有omega随机初始化
    - **tol**: 收敛准则的容忍度，通常约为1e-6。
    """)

# 数据加载函数
@st.cache_data
def load_data(file_path):
    try:
        try:
            # 首先尝试使用默认分隔符
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            # 如果失败，尝试使用逗号分隔符
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')
        
        # 检查是否需要调整形状
        if data.shape[0] != 60000 or data.shape[1] != 61:
            if data.size == 60000 * 61:
                data = data.T
        
        return data
    except Exception as e:
        st.error(f"读取文件出错: {e}")
        return None

# 绘制频谱图
def plot_spectrum(data, fs, channel=0):
    f, Pxx = signal.welch(data[:, channel], fs, nperseg=1024)
    fig, ax = plt.subplots(figsize=(10, 4))
    ax.semilogy(f, Pxx)
    ax.set_title(f'通道 {channel+1} 频谱')
    ax.set_xlabel('频率 [Hz]')
    ax.set_ylabel('功率谱密度 [V^2/Hz]')
    return fig

# 应用VMD并绘制结果
def apply_vmd(signal_1d, alpha, tau, K, DC, init, tol, fs):
    # 应用VMD
    u, u_hat, omega = VMD(signal_1d, alpha, tau, K, DC, init, tol)
    
    # 创建图表
    fig, axes = plt.subplots(K+1, 1, figsize=(12, 2*(K+1)))
    
    # 绘制原始信号
    time = np.arange(len(signal_1d)) / fs
    axes[0].plot(time, signal_1d)
    axes[0].set_title('原始信号')
    axes[0].set_xlabel('时间 [秒]')
    axes[0].set_ylabel('幅度')
    
    # 绘制分解后的各模态
    for i in range(K):
        axes[i+1].plot(time, u[i, :])
        axes[i+1].set_title(f'分解模态 {i+1}，中心频率: {omega[-1, i]:.2f}')
        axes[i+1].set_xlabel('时间 [秒]')
        axes[i+1].set_ylabel('幅度')
    
    plt.tight_layout()
    return fig, u

def main():
    st.title("61通道数据VMD分析工具")
    
    # 侧边栏：数据加载和参数设置
    with st.sidebar:
        st.header("数据选择")
        data_path = st.text_input("数据目录路径", value="E:\\个人临时文件夹\\王友好\\61通道\\第三代测试数据\\第三代测试数据")
        
        # 找到所有子目录
        if os.path.exists(data_path):
            subdirs = [d for d in os.listdir(data_path) if os.path.isdir(os.path.join(data_path, d))]
            selected_subdir = st.selectbox("选择子目录", options=subdirs)
            
            # 在选定的子目录中查找61通道.txt文件
            full_subdir_path = os.path.join(data_path, selected_subdir)
            data_files = [f for f in os.listdir(full_subdir_path) if '61通道.txt' in f]
            
            if data_files:
                selected_file = st.selectbox("选择数据文件", options=data_files)
                full_file_path = os.path.join(full_subdir_path, selected_file)
            else:
                st.warning(f"在 {full_subdir_path} 中未找到61通道.txt文件")
                full_file_path = None
        else:
            st.warning(f"目录 {data_path} 不存在")
            full_file_path = None
            
        st.header("VMD参数")
        sampling_rate = st.number_input("采样率 (Hz)", min_value=1, value=1000)
        
        # 是否应用VMD
        apply_vmd_flag = st.checkbox("应用VMD分解", value=False)
        
        if apply_vmd_flag:
            col1, col2 = st.columns(2)
            with col1:
                alpha = st.number_input("Alpha", min_value=0.0, value=2000.0)
                tau = st.number_input("Tau", min_value=0.0, value=0.0)
                K = st.number_input("模态数量 (K)", min_value=1, max_value=10, value=5)
            
            with col2:
                DC = st.checkbox("保持第一模态在DC", value=True)
                init = st.radio("初始化方式", options=[0, 1, 2], 
                                format_func={0: "全部从0开始", 1: "均匀分布", 2: "随机初始化"}.get)
                tol = st.number_input("收敛容忍度", min_value=1e-10, value=1e-6, format="%.2e")
        
        # 显示帮助按钮
        if st.button("显示VMD参数说明"):
            show_help()
    
    # 主区域：数据可视化
    if full_file_path and os.path.exists(full_file_path):
        # 加载数据
        data = load_data(full_file_path)
        
        if data is not None:
            st.success(f"成功加载数据，形状: {data.shape}")
            
            # 选择通道
            st.subheader("通道选择")
            channel = st.slider("选择通道", min_value=0, max_value=data.shape[1]-1, value=0)
            
            # 显示原始波形
            st.subheader("原始波形")
            fig, ax = plt.subplots(figsize=(10, 4))
            
            # 是否显示全部波形或一部分
            show_full = st.checkbox("显示全部波形", value=False)
            if show_full:
                time = np.arange(len(data)) / sampling_rate
                ax.plot(time, data[:, channel])
                ax.set_xlabel("时间 [秒]")
            else:
                # 只显示前10000个点
                points_to_show = min(10000, len(data))
                time = np.arange(points_to_show) / sampling_rate
                ax.plot(time, data[:points_to_show, channel])
                ax.set_xlabel("时间 [秒]")
            
            ax.set_title(f"通道 {channel+1} 的原始波形")
            ax.set_ylabel("幅度")
            st.pyplot(fig)
            
            # 显示频谱
            st.subheader("频谱分析")
            spec_fig = plot_spectrum(data, sampling_rate, channel)
            st.pyplot(spec_fig)
            
            # 应用VMD
            if apply_vmd_flag:
                st.subheader("VMD分解结果")
                
                # 通知用户VMD处理可能需要时间
                with st.spinner("正在进行VMD分解，这可能需要一些时间..."):
                    # 选择要分析的数据段
                    segment_length = st.slider("选择分析长度（数据点数）", 
                                             min_value=1000, 
                                             max_value=min(60000, len(data)), 
                                             value=10000)
                    
                    segment_start = st.slider("选择起始位置", 
                                            min_value=0, 
                                            max_value=max(0, len(data) - segment_length),
                                            value=0)
                    
                    # 获取所选数据段
                    signal_segment = data[segment_start:segment_start+segment_length, channel]
                    
                    # 应用VMD并显示结果
                    vmd_fig, vmd_components = apply_vmd(signal_segment, alpha, tau, K, DC, init, tol, sampling_rate)
                    st.pyplot(vmd_fig)
                    
                    # 下载按钮
                    vmd_df = pd.DataFrame(vmd_components.T, columns=[f"模态_{i+1}" for i in range(K)])
                    vmd_csv = vmd_df.to_csv(index=False).encode('utf-8')
                    st.download_button(
                        label="下载VMD分解结果",
                        data=vmd_csv,
                        file_name=f"vmd_results_channel_{channel+1}.csv",
                        mime='text/csv',
                    )
        else:
            st.error("无法加载数据文件")
    else:
        st.info("请选择数据文件")

if __name__ == "__main__":
    main() 