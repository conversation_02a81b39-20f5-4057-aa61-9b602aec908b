"""
配置管理系统
Author: Assistant
Date: 2025-07-15
Description: 负责配置文件的加载、验证和管理
"""

import yaml
import os
import logging
import numpy as np
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path

@dataclass
class ProcessingConfig:
    """处理配置类"""
    channels: List[int] = field(default_factory=lambda: [1, 2, 3, 4, 5, 6, 7, 8, 9])
    start_time: float = 0
    duration: float = 10
    start_sample: int = 0
    sample_length: int = 10000
    sampling_rate: int = 1000

@dataclass
class VMDConfig:
    """VMD配置类"""
    K: int = 5
    alpha: float = 2000.0
    tau: float = 0.0
    DC: bool = True
    init: int = 1
    tol: float = 1e-7

@dataclass
class FilterRule:
    """滤波规则类"""
    keep: bool = True
    filter_type: str = "none"  # none, low, high, band
    low_cutoff: Optional[float] = None
    high_cutoff: Optional[float] = None

@dataclass
class FilterPolicy:
    """滤波策略类"""
    name: str = "default"
    description: str = ""
    mode_rules: Dict[str, FilterRule] = field(default_factory=dict)

@dataclass
class ChannelGroup:
    """通道组配置类"""
    channels: List[int] = field(default_factory=list)
    reference_channel: int = 0

@dataclass
class DataStructure:
    """数据结构配置类"""
    idx_channel: int = 1
    data_channels: List[int] = field(default_factory=lambda: [2, 37])
    groups: Dict[str, ChannelGroup] = field(default_factory=dict)
    reference_channels: List[int] = field(default_factory=lambda: [38, 39, 40, 41])

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config = {}
        self.setup_logging()
        self.load_config()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler("../../logs/config.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                self.logger.error(f"配置文件不存在: {self.config_path}")
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            self.logger.info(f"成功加载配置文件: {self.config_path}")
            self.validate_config()
            return self.config
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise
    
    def validate_config(self):
        """验证配置文件"""
        required_sections = ['project', 'data', 'vmd', 'filter_policies', 'data_structure']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必需的部分: {section}")
        
        # 验证通道配置
        channels = self.config['data']['processing']['channels']
        if not isinstance(channels, list) or not all(isinstance(c, int) for c in channels):
            raise ValueError("通道配置必须是整数列表")
        
        # 验证VMD参数
        vmd_config = self.config['vmd']
        if vmd_config['K'] <= 0:
            raise ValueError("VMD模态数量K必须大于0")
        
        self.logger.info("配置文件验证通过")
    
    def get_processing_config(self) -> ProcessingConfig:
        """获取处理配置"""
        proc_config = self.config['data']['processing']
        return ProcessingConfig(
            channels=proc_config['channels'],
            start_time=proc_config['time_segment']['start'],
            duration=proc_config['time_segment']['duration'],
            start_sample=proc_config['sample_segment']['start'],
            sample_length=proc_config['sample_segment']['length'],
            sampling_rate=proc_config['sampling_rate']
        )
    
    def get_vmd_config(self) -> VMDConfig:
        """获取VMD配置"""
        vmd_config = self.config['vmd']
        return VMDConfig(
            K=vmd_config['K'],
            alpha=vmd_config['alpha'],
            tau=vmd_config['tau'],
            DC=vmd_config['DC'],
            init=vmd_config['init'],
            tol=vmd_config['tol']
        )
    
    def get_filter_policy(self, policy_name: Optional[str] = None) -> FilterPolicy:
        """获取滤波策略"""
        if policy_name is None:
            policy_name = self.config.get('current_policy', 'default')
        
        if policy_name not in self.config['filter_policies']:
            raise ValueError(f"滤波策略 '{policy_name}' 不存在")
        
        policy_config = self.config['filter_policies'][policy_name]
        
        # 转换mode_rules为FilterRule对象
        mode_rules = {}
        for rule_name, rule_config in policy_config.get('mode_rules', {}).items():
            mode_rules[rule_name] = FilterRule(
                keep=rule_config.get('keep', True),
                filter_type=rule_config.get('filter_type', 'none'),
                low_cutoff=rule_config.get('low_cutoff'),
                high_cutoff=rule_config.get('high_cutoff')
            )
        
        return FilterPolicy(
            name=policy_config['name'],
            description=policy_config.get('description', ''),
            mode_rules=mode_rules
        )
    
    def get_data_structure(self) -> DataStructure:
        """获取数据结构配置"""
        ds_config = self.config['data_structure']
        
        # 转换groups为ChannelGroup对象
        groups = {}
        for group_name, group_config in ds_config.get('groups', {}).items():
            groups[group_name] = ChannelGroup(
                channels=list(range(group_config['channels'][0], group_config['channels'][1] + 1)),
                reference_channel=group_config['reference_channel']
            )
        
        return DataStructure(
            idx_channel=ds_config['idx_channel'],
            data_channels=list(range(ds_config['data_channels'][0], ds_config['data_channels'][1] + 1)),
            groups=groups,
            reference_channels=ds_config['reference_channels']
        )
    
    def get_path(self, path_key: str) -> Path:
        """获取路径配置"""
        path_mapping = {
            'input': self.config['data']['input_path'],
            'output': self.config['data']['output_path'],
            'logs': self.config['logging']['file'],
            'cache': self.config['cache']['directory']
        }
        
        if path_key not in path_mapping:
            raise ValueError(f"路径配置 '{path_key}' 不存在")
        
        return Path(path_mapping[path_key])
    
    def get_output_config(self) -> Dict[str, Any]:
        """获取输出配置"""
        return self.config.get('output', {})
    
    def get_parallel_config(self) -> Dict[str, Any]:
        """获取并行处理配置"""
        return self.config.get('parallel', {})
    
    def get_quality_config(self) -> Dict[str, Any]:
        """获取质量控制配置"""
        return self.config.get('quality', {})
    
    def update_config(self, section: str, key: str, value: Any):
        """更新配置"""
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            self.logger.info(f"配置文件已保存: {self.config_path}")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            raise
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.get_path('../../output'),
            Path(self.get_path('../../logs')).parent,
            self.get_path('cache')
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"目录已创建: {directory}")
    
    def get_policy_names(self) -> List[str]:
        """获取所有可用的策略名称"""
        return list(self.config['filter_policies'].keys())
    
    def apply_strategy_to_modes(self, vmd_omega: np.ndarray, fs: int, policy_name: Optional[str] = None) -> List[FilterRule]:
        """
        根据策略将滤波规则应用到VMD模态
        
        Args:
            vmd_omega: VMD分解得到的频率信息
            fs: 采样频率
            policy_name: 策略名称
            
        Returns:
            List[FilterRule]: 每个模态对应的滤波规则
        """
        import numpy as np
        
        policy = self.get_filter_policy(policy_name)
        K = vmd_omega.shape[1]  # 模态数量
        
        # 计算每个模态的中心频率
        center_freqs = []
        for i in range(K):
            center_freq_hz = vmd_omega[-1, i] * fs / (2 * np.pi)
            center_freqs.append(center_freq_hz)
        
        # 找到最低频模态
        lowest_freq_idx = np.argmin(center_freqs)
        
        # 应用策略规则
        mode_rules = []
        for i in range(K):
            if i == lowest_freq_idx and 'lowest_freq' in policy.mode_rules:
                # 应用最低频规则
                mode_rules.append(policy.mode_rules['lowest_freq'])
            elif center_freqs[i] > 50 and 'high_freq' in policy.mode_rules:
                # 应用高频规则
                mode_rules.append(policy.mode_rules['high_freq'])
            elif 'others' in policy.mode_rules:
                # 应用其他模态规则
                mode_rules.append(policy.mode_rules['others'])
            else:
                # 默认规则：保留但不滤波
                mode_rules.append(FilterRule(keep=True, filter_type='none'))
        
        return mode_rules


# 配置管理器单例
config_manager = ConfigManager()