[Dialogs]
UserInfo=0
FeatureInfo1=1
SingleDirectory=1
InstallationType=0
FeatureTree=0
License=0
License2=1
NICertificate=0
WinFastStartup=1
ConfirmStart=1
End=1

[Distribution]
Title=1
GUID={41DAC769-4DF9-49F1-A347-C95AA34D855E}
PackageGUID={F6BD69B2-26CD-468D-863F-C33ADD47AB47}
Version=1.0.0
IDPath=nidist.id
Has64bitPart=1

[EndDialog]
Readme=5;30,50,330,150;static

[Files]
License2TextFile0009=NI;license\NI Released License Agreement - English.rtf
License2TextFile0010=NI;license\NI Released License Agreement - Spanish.rtf
License2TextFile0016=NI;license\NI Released License Agreement - Italian.rtf
License2TextFile0007=NI;license\NI Released License Agreement - German.rtf
License2TextFile0012=NI;license\NI Released License Agreement - French.rtf
License2TextFile0017=NI;license\NI Released License Agreement - Japanese.rtf
License2TextFile0018=NI;license\NI Released License Agreement - Korean.rtf
License2TextFile2052=NI;license\NI Released License Agreement - Simplified Chinese.rtf

[Graphics]
TopBar=IDB_TOPSTRIP,IDB_LARGETOPSTRIP
FullScreen=IDB_FULLSCREEN,IDB_LARGEFULLSCREEN

[InitProgress]
left=55
top=165
right=340
bottom=179
red=206
green=203
blue=173

[InstallLevels]
Default=100

[Localization]
Languages=9
ForceLanguage=9
CustomRes0009=supportfiles\customResource0009.dll
StandardLangXfm0007=supportfiles\nistdtransbase.mst;supportfiles\nistdtrans0007.mst
StandardLangXfm0012=supportfiles\nistdtransbase.mst;supportfiles\nistdtrans0012.mst
StandardLangXfm0017=supportfiles\nistdtransbase.mst;supportfiles\nistdtrans0017.mst
StandardLangXfm0018=supportfiles\nistdtransbase.mst;supportfiles\nistdtrans0018.mst
StandardLangXfm2052=supportfiles\nistdtransbase.mst;supportfiles\nistdtrans2052.mst

[Merged]
Path=supportfiles\merged.cab

[OS]
VersionNTMin=6,1,7601,1,0
VersionNTMax=

[Settings]
WelcomeAutoAdvance=1

[SingleDirectoryDialog]
DirText1=4
DirText2=7
Dir1=INSTALLDIR.MDF100
Dir1EditLabel=8
Dir2=NIDIR
Dir2EditLabel=9

[WinFastStartupDialog]
CheckboxDefaultState=1

[Strings]
WelcomeTitle=2;15,20,260,45
WelcomeText=3;15,55,260,115

[Utilities]
UpgradeManagerPostProcessor=supportfiles\niPie.exe

[Parts]
NI-USI.msi=
NI-USI64.msi=
LV2015runtime.msi=
LV2015rtdnet.msi=
tdms.msi=
tdms64.msi=
MetaUninstaller.msi=
logos.msi=
logos64.msi=
MDFSupport.msi=
VC2010RTEx86.msi=
VC2010RTEx64.msi=
LV2015rteres.msi=
activex.msi=
activex64.msi=
nisvcloc.msi=
ni_syswebsrvr.msi=
mkl.msi=
mkl64.msi=
NIWebServer_LVRTE.msi=
LVRT_NBFifo_2015.msi=
NITraceEngine.msi=
NITraceEngine64.msi=
nicurl.msi=
nicurl64.msi=
VC2008RTEx86.msi=
VC2008RTEx64.msi=
mDNSResponder.msi=
mDNSResponder_W64.msi=
dep_framework.msi=
ni_error_report.msi=
MStudioCW3DGraph.msi=
NIER_Interface.msi=
NIER_Interface64.msi=
NISysLogUtils.msi=
CVI_LowLevelDriverOriginal.msi=
CVI_LowLevelDriverUpdated.msi=
KillBit.msi=
KillBit64.msi=
LogosXT.msi=
LogosXT64.msi=
VC2015Core.msi=
VC2015-32Wrapper.msi=
VC2015-64Wrapper.msi=
EULADepot2.msi=
ni_sysweb_base.msi=
ni_sysweb_base64.msi=
ni_sysappsrvr.msi=
ni_sysappsrvr64.msi=
ssl_LVRTEsupp.msi=
niauth.msi=
niauth64.msi=
NI_SysStatePub.msi=
NI_SysStatePub64.msi=
ni_ssl.msi=
ni_ssl64.msi=
MSIProperties.msi=
MSIProperties64.msi=
install.msi=
[NI-USI.msi]
Path=bin\p33\NI-USI.msi
Volume=1
ProductCode={8897C6A1-B3D5-4333-BAE8-62FFC97D4F22}
ProductId={8897C6A1-B3D5-4333-BAE8-62FFC97D4F22}
LangXfm0007=bin\p33\NI-USI.msi_deu.mst
LangXfm0012=bin\p33\NI-USI.msi_fra.mst
LangXfm0017=bin\p33\NI-USI.msi_jpn.mst
LangXfm0018=bin\p33\NI-USI.msi_kor.mst
LangXfm2052=bin\p33\NI-USI.msi_chs.mst
[NI-USI64.msi]
Path=bin\p33\NI-USI64.msi
Volume=1
ProductCode={A81D3973-AA2B-46AF-A881-3A4D9F0E1F50}
LangXfm0007=bin\p33\NI-USI64.msi_deu.mst
LangXfm0012=bin\p33\NI-USI64.msi_fra.mst
LangXfm0017=bin\p33\NI-USI64.msi_jpn.mst
LangXfm0018=bin\p33\NI-USI64.msi_kor.mst
LangXfm2052=bin\p33\NI-USI64.msi_chs.mst
[LV2015runtime.msi]
Path=bin\p32\LV2015runtime.msi
Volume=1
ProductCode={9BF41E88-9689-42A4-BD50-56F2280B97F0}
ProductId={9BF41E88-9689-42A4-BD50-56F2280B97F0}
LangXfm0007=bin\p32\lvrte_deu.mst
LangXfm0012=bin\p32\lvrte_fra.mst
LangXfm0017=bin\p32\lvrte_jpn.mst
LangXfm0018=bin\p32\lvrte_kor.mst
LangXfm2052=bin\p32\lvrte_chs.mst
[LV2015rtdnet.msi]
Path=bin\p32\LV2015rtdnet.msi
Volume=1
ProductCode={2A81C927-83A2-44CF-8774-60B36A27E60C}
[tdms.msi]
Path=bin\p25\tdms.msi
Volume=1
ProductCode={29FBD1D9-6A19-4CF6-935F-C0EFCFB6CA3D}
ProductId={29FBD1D9-6A19-4CF6-935F-C0EFCFB6CA3D}
LangXfm0007=bin\p25\tdms_deu.mst
LangXfm0012=bin\p25\tdms_fra.mst
LangXfm0017=bin\p25\tdms_jpn.mst
LangXfm0018=bin\p25\tdms_kor.mst
LangXfm2052=bin\p25\tdms_chs.mst
[tdms64.msi]
Path=bin\p25\tdms64.msi
Volume=1
ProductCode={4E79753B-0A5E-4DF6-821A-5C160572CF5F}
LangXfm0007=bin\p25\tdms64_deu.mst
LangXfm0012=bin\p25\tdms64_fra.mst
LangXfm0017=bin\p25\tdms64_jpn.mst
LangXfm0018=bin\p25\tdms64_kor.mst
LangXfm2052=bin\p25\tdms64_chs.mst
[MetaUninstaller.msi]
Path=bin\p0\MU\MetaUninstaller.msi
Volume=1
ProductCode={B5A21D83-CE94-46F2-8622-6DBAE8828FC0}
ProductId={B5A21D83-CE94-46F2-8622-6DBAE8828FC0}
[logos.msi]
Path=bin\p31\logos.msi
Volume=1
ProductCode={68428815-FD93-4897-BC4A-EC5E3194C4E7}
ProductId={68428815-FD93-4897-BC4A-EC5E3194C4E7}
LangXfm0007=bin\p31\logos_deu.mst
LangXfm0012=bin\p31\logos_fra.mst
LangXfm0017=bin\p31\logos_jpn.mst
LangXfm0018=bin\p31\logos_kor.mst
LangXfm2052=bin\p31\logos_chs.mst
[logos64.msi]
Path=bin\p31\logos64.msi
Volume=1
ProductCode={EDF0B62F-D6FC-414B-8BC6-CB055D1702D6}
LangXfm0007=bin\p31\logos64_deu.mst
LangXfm0012=bin\p31\logos64_fra.mst
LangXfm0017=bin\p31\logos64_jpn.mst
LangXfm0018=bin\p31\logos64_kor.mst
LangXfm2052=bin\p31\logos64_chs.mst
[MDFSupport.msi]
Path=bin\p28\MDF\MDFSupport.msi
Volume=1
ProductCode={6E8EA788-2E49-4596-A418-CAD2559CECA7}
ProductId={6E8EA788-2E49-4596-A418-CAD2559CECA7}
[VC2010RTEx86.msi]
Path=bin\p3\VC2010RTEx86.msi
Volume=1
ProductCode={405BA31C-3A17-47EC-9075-D18B09387C32}
ProductId={405BA31C-3A17-47EC-9075-D18B09387C32}
[VC2010RTEx64.msi]
Path=bin\p3\VC2010RTEx64.msi
Volume=1
ProductCode={F2465E37-FF02-424B-8701-22C2331F06FC}
[LV2015rteres.msi]
Path=bin\p30\lvrteres\LV2015rteres.msi
Volume=1
ProductCode={1F71BB6C-796E-4E10-84EF-2032F401D03F}
ProductId={1F71BB6C-796E-4E10-84EF-2032F401D03F}
LangXfm0007=bin\p30\lvrteres\lvrteres_deu.mst
LangXfm0012=bin\p30\lvrteres\lvrteres_fra.mst
LangXfm0017=bin\p30\lvrteres\lvrteres_jpn.mst
LangXfm0018=bin\p30\lvrteres\lvrteres_kor.mst
LangXfm2052=bin\p30\lvrteres\lvrteres_chs.mst
[activex.msi]
Path=bin\p23\activex.msi
Volume=1
ProductCode={CE7D38A4-66ED-4D33-A385-D4A2CF4BFF81}
ProductId={CE7D38A4-66ED-4D33-A385-D4A2CF4BFF81}
LangXfm0007=bin\p23\activ32_deu.mst
LangXfm0012=bin\p23\activ32_fra.mst
LangXfm0017=bin\p23\activ32_jpn.mst
LangXfm0018=bin\p23\activ32_kor.mst
LangXfm2052=bin\p23\activ32_chs.mst
[activex64.msi]
Path=bin\p23\activex64.msi
Volume=1
ProductCode={177D42FE-F7B7-4AFA-901F-3A72E859903F}
LangXfm0007=bin\p23\activ64_deu.mst
LangXfm0012=bin\p23\activ64_fra.mst
LangXfm0017=bin\p23\activ64_jpn.mst
LangXfm0018=bin\p23\activ64_kor.mst
LangXfm2052=bin\p23\activ64_chs.mst
[nisvcloc.msi]
Path=bin\p16\svcloc\nisvcloc.msi
Volume=1
ProductCode={214BEF10-ED6E-4198-A7EC-893BC3550330}
ProductId={214BEF10-ED6E-4198-A7EC-893BC3550330}
LangXfm0007=bin\p16\svcloc\svcloc_deu.mst
LangXfm0012=bin\p16\svcloc\svcloc_fra.mst
LangXfm0017=bin\p16\svcloc\svcloc_jpn.mst
LangXfm0018=bin\p16\svcloc\svcloc_kor.mst
LangXfm2052=bin\p16\svcloc\svcloc_chs.mst
[ni_syswebsrvr.msi]
Path=bin\p17\sys_w00\ni_syswebsrvr.msi
Volume=1
ProductCode={E242D2FA-5A8C-49A6-A43F-04FEB0053009}
ProductId={E242D2FA-5A8C-49A6-A43F-04FEB0053009}
LangXfm0007=bin\p17\sys_w00\sys_w00_deu.mst
LangXfm0012=bin\p17\sys_w00\sys_w00_fra.mst
LangXfm0017=bin\p17\sys_w00\sys_w00_jpn.mst
LangXfm0018=bin\p17\sys_w00\sys_w00_kor.mst
LangXfm2052=bin\p17\sys_w00\sys_w00_chs.mst
[mkl.msi]
Path=bin\p29\mkl.msi
Volume=1
ProductCode={ECB572E6-5CE3-4E9E-B1B3-16A00E02153A}
ProductId={ECB572E6-5CE3-4E9E-B1B3-16A00E02153A}
LangXfm0007=bin\p29\MKL2015_deu.mst
LangXfm0012=bin\p29\MKL2015_fra.mst
LangXfm0017=bin\p29\MKL2015_jpn.mst
LangXfm0018=bin\p29\MKL2015_kor.mst
LangXfm2052=bin\p29\MKL2015_chs.mst
[mkl64.msi]
Path=bin\p29\mkl64.msi
Volume=1
ProductCode={70107084-C0AE-49B7-B588-C64901E546C3}
LangXfm0007=bin\p29\MKL2000_deu.mst
LangXfm0012=bin\p29\MKL2000_fra.mst
LangXfm0017=bin\p29\MKL2000_jpn.mst
LangXfm0018=bin\p29\MKL2000_kor.mst
LangXfm2052=bin\p29\MKL2000_chs.mst
[NIWebServer_LVRTE.msi]
Path=bin\p18\LabVI00\NIWebServer_LVRTE.msi
Volume=1
ProductCode={0815C7BF-1A46-4AD0-80D5-E509F9783E5B}
ProductId={0815C7BF-1A46-4AD0-80D5-E509F9783E5B}
LangXfm0007=bin\p18\LabVI00\LabVI00_deu.mst
LangXfm0012=bin\p18\LabVI00\LabVI00_fra.mst
LangXfm0017=bin\p18\LabVI00\LabVI00_jpn.mst
LangXfm0018=bin\p18\LabVI00\LabVI00_kor.mst
LangXfm2052=bin\p18\LabVI00\LabVI00_chs.mst
[LVRT_NBFifo_2015.msi]
Path=bin\p9\LVRT_00\LVRT_NBFifo_2015.msi
Volume=1
ProductCode={B682E9AB-D9F8-4DE3-B103-0CE769835CE8}
ProductId={B682E9AB-D9F8-4DE3-B103-0CE769835CE8}
LangXfm0007=bin\p9\LVRT_00\LVRT_00_deu.mst
LangXfm0012=bin\p9\LVRT_00\LVRT_00_fra.mst
LangXfm0017=bin\p9\LVRT_00\LVRT_00_jpn.mst
LangXfm0018=bin\p9\LVRT_00\LVRT_00_kor.mst
LangXfm2052=bin\p9\LVRT_00\LVRT_00_chs.mst
[NITraceEngine.msi]
Path=bin\p13\NITraceEngine.msi
Volume=1
ProductCode={8301A002-940A-4588-A760-043776F1CA6E}
ProductId={8301A002-940A-4588-A760-043776F1CA6E}
[NITraceEngine64.msi]
Path=bin\p13\NITraceEngine64.msi
Volume=1
ProductCode={AB2584A1-AE0E-4171-B5DD-D89B01C4DF3A}
[nicurl.msi]
Path=bin\p20\nicurl.msi
Volume=1
ProductCode={18C5DCAB-EF32-48F8-9789-2A33DE1CA2CB}
ProductId={18C5DCAB-EF32-48F8-9789-2A33DE1CA2CB}
[nicurl64.msi]
Path=bin\p20\nicurl64.msi
Volume=1
ProductCode={62BBFF5E-0929-49AE-89A9-BF764B313825}
[VC2008RTEx86.msi]
Path=bin\p2\VC2008RTEx86.msi
Volume=1
ProductCode={A4FAF873-938B-43B8-BBF2-E4B2C0CE730B}
ProductId={A4FAF873-938B-43B8-BBF2-E4B2C0CE730B}
[VC2008RTEx64.msi]
Path=bin\p2\VC2008RTEx64.msi
Volume=1
ProductCode={EDB711DF-86C3-4593-811D-98663B0CCE37}
[mDNSResponder.msi]
Path=bin\p10\mDNSResponder.msi
Volume=1
ProductCode={E6E0A909-7F30-4EE8-A493-F0329B57B187}
ProductId={E6E0A909-7F30-4EE8-A493-F0329B57B187}
[mDNSResponder_W64.msi]
Path=bin\p10\mDNSResponder_W64.msi
Volume=1
ProductCode={6D53B64D-C08F-4571-A666-969D8E3FF232}
[dep_framework.msi]
Path=bin\p7\NI_De00\dep_framework.msi
Volume=1
ProductCode={5DF6BA6E-A045-48FC-9AD8-39A0799FA79B}
ProductId={5DF6BA6E-A045-48FC-9AD8-39A0799FA79B}
[ni_error_report.msi]
Path=bin\p21\ni_error\ni_error_report.msi
Volume=1
ProductCode={B7685AF0-3026-43FA-A953-327C2185BAAF}
ProductId={B7685AF0-3026-43FA-A953-327C2185BAAF}
LangXfm0007=bin\p21\ni_error\ni_error_deu.mst
LangXfm0012=bin\p21\ni_error\ni_error_fra.mst
LangXfm0017=bin\p21\ni_error\ni_error_jpn.mst
LangXfm0018=bin\p21\ni_error\ni_error_kor.mst
LangXfm2052=bin\p21\ni_error\ni_error_chs.mst
[MStudioCW3DGraph.msi]
Path=bin\p34\MStudioCW3DGraph.msi
Volume=1
ProductCode={36E48952-DC01-4739-848E-EA7DC71FB485}
ProductId={36E48952-DC01-4739-848E-EA7DC71FB485}
[NIER_Interface.msi]
Path=bin\p11\NIER_Interface.msi
Volume=1
ProductCode={CE1D0ABA-7672-4A48-ACD8-9AFB1EA33809}
ProductId={CE1D0ABA-7672-4A48-ACD8-9AFB1EA33809}
[NIER_Interface64.msi]
Path=bin\p11\NIER_Interface64.msi
Volume=1
ProductCode={9D3C0B27-C240-47FE-BB9F-75CDBF1761FD}
[NISysLogUtils.msi]
Path=bin\p5\NISys00\NISysLogUtils.msi
Volume=1
ProductCode={607BAC56-66B2-4750-BC0F-271C2BE4DA01}
ProductId={607BAC56-66B2-4750-BC0F-271C2BE4DA01}
[CVI_LowLevelDriverOriginal.msi]
Path=bin\p8\CVI_LowLevelDriverOriginal.msi
Volume=1
ProductCode={E2821E0B-CB08-41BD-AE01-A9068F7B8771}
ProductId={E2821E0B-CB08-41BD-AE01-A9068F7B8771}
[CVI_LowLevelDriverUpdated.msi]
Path=bin\p8\CVI_LowLevelDriverUpdated.msi
Volume=1
ProductCode={32A1B380-2934-4AA9-AE43-80135A390646}
[KillBit.msi]
Path=bin\p12\KillBit.msi
Volume=1
ProductCode={FE7D71A2-BF64-441C-9489-BE6F6937B98E}
ProductId={FE7D71A2-BF64-441C-9489-BE6F6937B98E}
[KillBit64.msi]
Path=bin\p12\KillBit64.msi
Volume=1
ProductCode={A018997D-B0DD-4643-B9F2-166487C50E2F}
[LogosXT.msi]
Path=bin\p22\LogosXT.msi
Volume=1
ProductCode={F05EC50F-B2B4-4B87-A32C-6F9AD11D1B49}
ProductId={F05EC50F-B2B4-4B87-A32C-6F9AD11D1B49}
[LogosXT64.msi]
Path=bin\p22\LogosXT64.msi
Volume=1
ProductCode={E40CCD96-E0DB-4734-8978-068E99DEC731}
[VC2015Core.msi]
Path=bin\p4\VC2015Core.msi
Volume=1
ProductCode={5691A0DE-3987-4595-A7F8-91133A3C1437}
ProductId={5691A0DE-3987-4595-A7F8-91133A3C1437}
[VC2015-32Wrapper.msi]
Path=bin\p4\VC2015-32Wrapper.msi
Volume=1
ProductCode={8D3EFD86-2708-4C76-B684-4AB78E4F626B}
[VC2015-64Wrapper.msi]
Path=bin\p4\VC2015-64Wrapper.msi
Volume=1
ProductCode={591E0AAD-C43C-4220-901C-4DB8E562FC85}
[EULADepot2.msi]
Path=bin\p1\EULADepot2.msi
Volume=1
ProductCode={EC1AF45B-6906-4C88-A6C9-E8CE089CB8D2}
ProductId={EC1AF45B-6906-4C88-A6C9-E8CE089CB8D2}
[ni_sysweb_base.msi]
Path=bin\p24\ni_sysweb_base.msi
Volume=1
ProductCode={7EA82A5F-F077-4158-AC62-7FCD81311628}
ProductId={7EA82A5F-F077-4158-AC62-7FCD81311628}
[ni_sysweb_base64.msi]
Path=bin\p24\ni_sysweb_base64.msi
Volume=1
ProductCode={C6A1EE64-6B9E-4CDE-8FDF-201C4C87B0C1}
[ni_sysappsrvr.msi]
Path=bin\p27\ni_sysappsrvr.msi
Volume=1
ProductCode={58512B6A-4447-4930-AE2D-4A54F2E45D3E}
ProductId={58512B6A-4447-4930-AE2D-4A54F2E45D3E}
LangXfm0007=bin\p27\sys_a01_deu.mst
LangXfm0012=bin\p27\sys_a01_fra.mst
LangXfm0017=bin\p27\sys_a01_jpn.mst
LangXfm0018=bin\p27\sys_a01_kor.mst
LangXfm2052=bin\p27\sys_a01_chs.mst
[ni_sysappsrvr64.msi]
Path=bin\p27\ni_sysappsrvr64.msi
Volume=1
ProductCode={E000B4B7-C338-4BC6-B72D-D2D922F3A67F}
LangXfm0007=bin\p27\sys_a00_deu.mst
LangXfm0012=bin\p27\sys_a00_fra.mst
LangXfm0017=bin\p27\sys_a00_jpn.mst
LangXfm0018=bin\p27\sys_a00_kor.mst
LangXfm2052=bin\p27\sys_a00_chs.mst
[ssl_LVRTEsupp.msi]
Path=bin\p6\sslLVRTE\ssl_LVRTEsupp.msi
Volume=1
ProductCode={CBB0B504-8175-43D0-ACFC-04A7875D5EB1}
ProductId={CBB0B504-8175-43D0-ACFC-04A7875D5EB1}
[niauth.msi]
Path=bin\p19\niauth.msi
Volume=1
ProductCode={6E6C89C3-648D-4E5C-BF2E-1534F79D2FA7}
ProductId={6E6C89C3-648D-4E5C-BF2E-1534F79D2FA7}
[niauth64.msi]
Path=bin\p19\niauth64.msi
Volume=1
ProductCode={CD5A5BD7-7E08-4B2D-B4CD-C7D0499DF485}
[NI_SysStatePub.msi]
Path=bin\p14\NI_SysStatePub.msi
Volume=1
ProductCode={7B016017-2A10-49DE-87B9-BFAEF8745795}
ProductId={7B016017-2A10-49DE-87B9-BFAEF8745795}
[NI_SysStatePub64.msi]
Path=bin\p14\NI_SysStatePub64.msi
Volume=1
ProductCode={8B92BCAC-5615-4B30-B8A2-449899E627DF}
[ni_ssl.msi]
Path=bin\p26\ni_ssl.msi
Volume=1
ProductCode={FF56A0C1-3413-4024-89A6-0FB565B2E78C}
ProductId={FF56A0C1-3413-4024-89A6-0FB565B2E78C}
LangXfm0007=bin\p26\ssl_deu.mst
LangXfm0012=bin\p26\ssl_fra.mst
LangXfm0017=bin\p26\ssl_jpn.mst
LangXfm0018=bin\p26\ssl_kor.mst
LangXfm2052=bin\p26\ssl_chs.mst
[ni_ssl64.msi]
Path=bin\p26\ni_ssl64.msi
Volume=1
ProductCode={A81A6211-DF53-4CA7-B775-69740869C957}
LangXfm0007=bin\p26\ssl64_deu.mst
LangXfm0012=bin\p26\ssl64_fra.mst
LangXfm0017=bin\p26\ssl64_jpn.mst
LangXfm0018=bin\p26\ssl64_kor.mst
LangXfm2052=bin\p26\ssl64_chs.mst
[MSIProperties.msi]
Path=bin\p15\MetaUtils\MSIProperties.msi
Volume=1
ProductCode={4BF2C28F-3462-49CB-9942-6185CA169626}
ProductId={4BF2C28F-3462-49CB-9942-6185CA169626}
[MSIProperties64.msi]
Path=bin\p15\MetaUtils\MSIProperties64.msi
Volume=1
ProductCode={B31428E8-AB42-458D-B468-4B7416C4216B}
[install.msi]
Path=bin\dp\install.msi
Volume=1
ProductCode={8525DD9E-6004-4D83-A627-723717936042}
ProductId={8525DD9E-6004-4D83-A627-723717936042}
[LicenseTypes]
LicenseType2=NIStandardReleased
LicenseType3=Apache20
LicenseType4=Boost
LicenseType5=BSD3Clause
LicenseType6=CryptoPP
LicenseType7=FreeType
LicenseType8=HDF5
LicenseType9=ICU
LicenseType10=LGPL
LicenseType11=LLVM
LicenseType12=MARS
LicenseType13=MicrosoftPublicLicense
LicenseType14=MSVC 2015 Runtime
LicenseType15=MIT
LicenseType16=MIT-X-YAJL
LicenseType17=MIT-X
LicenseType18=Mozilla20
LicenseType19=NICTA
LicenseType20=OpenSSL
LicenseType21=STLPort
LicenseType22=UPX
LicenseType23=wxWindows
LicenseType24=WordNet21
LicenseType25=zlib
[LicenseType2]
IsDisplayedAlone=0
IsThirdParty=0
IsHideInMetaSuite=0
[LicenseType3]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType4]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType5]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType6]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType7]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType8]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType9]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType10]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType11]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType12]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType13]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType14]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType15]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType16]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType17]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType18]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType19]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType20]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType21]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType22]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType23]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType24]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[LicenseType25]
IsDisplayedAlone=0
IsThirdParty=1
IsHideInMetaSuite=0
[SilentInstallRequirements]
MustAcceptLicenses=1
[MDF]
MDFVersion=17.0.0.211
HasPatchSupport=0
DevPartSection=install.msi
