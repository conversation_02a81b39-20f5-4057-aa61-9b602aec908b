"""
Author: <PERSON><PERSON> (Enhanced by Assistant)
email: <EMAIL>
file: vmd_analyzer_optimized.py
date: 2025/7/15
desc:
61通道数据VMD分析工具的优化版本。
"""
import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
from vmdpy import VMD
import os
import pandas as pd
from scipy import signal
from scipy.fft import fft, fftfreq

# --- 初始设置 ---
try:
    plt.rcParams['font.family'] = 'SimHei'
    plt.rcParams['axes.unicode_minus'] = False
except:
    plt.rcParams['font.family'] = 'sans-serif'
    st.warning("未找到'SimHei'字体，已回退至默认字体。中文字符可能无法正常显示。")

st.set_page_config(page_title="高级VMD与直接滤波对比平台", layout="wide")

# --- 核心功能函数 ---

@st.cache_data
def load_data(file_path):
    """加载数据文件，支持不同分隔符"""
    try:
        try:
            data = np.loadtxt(file_path, encoding='utf-8')
        except ValueError:
            data = np.loadtxt(file_path, delimiter=',', encoding='utf-8')
        return data
    except Exception as e:
        st.error(f"读取文件出错: {e}")
        return None

@st.cache_data
def cached_vmd(signal_data, alpha, tau, K, DC, init, tol):
    """缓存VMD计算结果避免重复计算"""
    return VMD(signal_data, alpha, tau, K, DC, init, tol)

@st.cache_data
def compute_spectrum(signal_data, fs, max_freq=50):
    """计算信号频谱，限制显示频率范围到max_freq Hz"""
    n = len(signal_data)
    frequencies = fftfreq(n, 1/fs)
    spectrum = np.abs(fft(signal_data))

    # 只保留正频率部分且在max_freq范围内
    positive_mask = (frequencies >= 0) & (frequencies <= max_freq)
    return frequencies[positive_mask], spectrum[positive_mask]

def apply_filter(data, fs, filter_type, low_cutoff=None, high_cutoff=None, order=4):
    """应用滤波器（低通、高通、带通）"""
    nyquist = 0.5 * fs
    if filter_type == 'low' and low_cutoff is not None and 0 < low_cutoff < nyquist:
        normal_cutoff = low_cutoff / nyquist
        b, a = signal.butter(order, normal_cutoff, btype='low', analog=False)
    elif filter_type == 'high' and high_cutoff is not None and 0 < high_cutoff < nyquist:
        normal_cutoff = high_cutoff / nyquist
        b, a = signal.butter(order, normal_cutoff, btype='high', analog=False)
    elif filter_type == 'band' and low_cutoff is not None and high_cutoff is not None and 0 < low_cutoff < high_cutoff < nyquist:
        low = low_cutoff / nyquist
        high = high_cutoff / nyquist
        b, a = signal.butter(order, [low, high], btype='band', analog=False)
    else:
        return data

    filtered_data = signal.filtfilt(b, a, data)
    return filtered_data

def create_mode_display(mode_data, time_axis, mode_idx, center_freq, channel_num, mode_type="原始"):
    """创建单个模态的显示组件"""
    with st.expander(f"模态 {mode_idx+1} ({center_freq:.2f} Hz) - {mode_type}", expanded=False):
        col1, col2 = st.columns([1, 1])

        with col1:
            st.markdown("**时域信号**")
            fig_time, ax_time = plt.subplots(figsize=(6, 2))
            ax_time.plot(time_axis, mode_data, linewidth=1)
            ax_time.grid(True, alpha=0.3)
            ax_time.set_xlabel("时间 (s)", fontsize=8)
            ax_time.set_ylabel("幅值", fontsize=8)
            ax_time.tick_params(labelsize=8)
            plt.tight_layout()
            st.pyplot(fig_time, use_container_width=True)

        with col2:
            st.markdown("**频域分析 (≤50Hz)**")
            freq, spectrum = compute_spectrum(mode_data, st.session_state.analysis_results[channel_num]['fs'])
            fig_freq, ax_freq = plt.subplots(figsize=(6, 2))
            ax_freq.plot(freq, spectrum, linewidth=1)
            ax_freq.grid(True, alpha=0.3)
            ax_freq.set_xlabel("频率 (Hz)", fontsize=8)
            ax_freq.set_ylabel("幅值", fontsize=8)
            ax_freq.tick_params(labelsize=8)
            plt.tight_layout()
            st.pyplot(fig_freq, use_container_width=True)

# --- 初始化 Session State ---
def initialize_session_state():
    """初始化会话状态"""
    if 'run_analysis' not in st.session_state:
        st.session_state.run_analysis = False
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = {}
    if 'processed_signals' not in st.session_state:
        st.session_state.processed_signals = {}

# --- Streamlit 应用主函数 ---
def main():
    initialize_session_state()
    st.title("🔬 高级VMD与直接滤波对比分析平台")

    # 自定义CSS样式
    st.markdown("""
        <style>
        .compact-container {
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
            margin: 5px 0;
        }
        .mode-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .stExpander > div:first-child {
            background-color: #f0f0f0;
        }
        </style>
    """, unsafe_allow_html=True)

    st.markdown("---")

    # --- 侧边栏 ---
    with st.sidebar:
        st.header("📁 1. 数据选择")
        default_path = r"D:\data\去噪长周期\降噪北京301的标准源2025-7-14\20250711标准源"
        data_path = st.text_input("数据目录路径", value=default_path)

        full_file_path, data = None, None
        if os.path.exists(data_path):
            files = [f for f in os.listdir(data_path) if
                     f.endswith('.txt') and os.path.isfile(os.path.join(data_path, f))]
            if files:
                selected_file = st.selectbox("选择数据文件", options=files)
                full_file_path = os.path.join(data_path, selected_file)
                data = load_data(full_file_path)
            else:
                st.warning(f"在 {data_path} 中未找到.txt文件")
        else:
            st.warning(f"目录 {data_path} 不存在")

        if data is not None:
            st.success(f"✅ 已加载: {os.path.basename(full_file_path)}\n📊 形状: {data.shape}")

            st.header("⚙️ 2. 分析设置")
            all_channels = list(range(1, data.shape[1] + 1))
            selected_channels = st.multiselect("选择分析通道 (可多选)", options=all_channels, default=all_channels[0:1])

            segment_length = st.number_input("分析长度（点数）", min_value=1000, max_value=len(data), value=10000)
            max_start_pos = max(0, len(data) - segment_length)
            segment_start = st.number_input("分析起始位置（点）", min_value=0, max_value=max_start_pos, value=0)

            st.header("🔧 3. VMD参数")
            sampling_rate = st.number_input("采样率 (Hz)", min_value=1, value=1000)
            alpha = st.number_input("Alpha (带宽限制)", value=2000.0)
            K = st.number_input("模态数量 (K)", min_value=1, max_value=20, value=5)
            tau = st.number_input("Tau (噪声容忍度)", value=0.0)
            DC = st.checkbox("第一个模态为DC分量", True)
            init = st.radio("初始化方式", [0, 1, 2], index=1,
                           format_func={0: "全零", 1: "均匀", 2: "随机"}.get)
            tol = st.number_input("收敛容忍度", value=1e-7, format="%.1e")

            st.header("🚀 4. 执行分解")
            if st.button("🚀 执行VMD分解", disabled=not selected_channels, type="primary"):
                st.session_state.run_analysis = True
                st.session_state.analysis_results = {}
                st.session_state.processed_signals = {}

                progress_bar = st.progress(0, text="VMD分解进度...")
                with st.spinner("正在对所有选定通道进行VMD分解..."):
                    for i, channel in enumerate(selected_channels):
                        signal_to_process = data[segment_start: segment_start + segment_length, channel - 1]
                        u, u_hat, omega = cached_vmd(signal_to_process, alpha, tau, K, DC, init, tol)
                        st.session_state.analysis_results[channel] = {
                            'u': u, 'omega': omega, 'original_segment': signal_to_process,
                            'fs': sampling_rate, 'K': K
                        }
                        st.session_state.processed_signals[channel] = {}
                        progress_bar.progress((i + 1) / len(selected_channels),
                                            text=f"通道 {channel} 分解完成")
                st.success("✅ 所有选定通道的VMD分解完成！")
                st.rerun()

        elif full_file_path:
            st.error("❌ 数据加载失败，请检查文件。")

    # 主界面
    if not st.session_state.run_analysis or not st.session_state.analysis_results:
        st.info("👈 请在左侧侧边栏配置参数并开始分析。")
        return

    analyzed_channels = list(st.session_state.analysis_results.keys())
    tabs = st.tabs([f"📈 通道 {ch}" for ch in analyzed_channels])

    for i, tab in enumerate(tabs):
        with tab:
            channel_num = analyzed_channels[i]
            channel_results = st.session_state.analysis_results[channel_num]
            u, omega, original_segment, fs, K = (channel_results[key] for key in
                                                 ['u', 'omega', 'original_segment', 'fs', 'K'])
            time_axis = np.arange(len(original_segment)) / fs

            st.header(f"📊 通道 {channel_num} 分析结果")

            # 1. 原始信号显示
            with st.container():
                st.subheader("📈 原始信号片段")
                col1, col2 = st.columns([1, 1])

                with col1:
                    st.markdown("**时域信号**")
                    fig_orig, ax_orig = plt.subplots(figsize=(8, 3))
                    ax_orig.plot(time_axis, original_segment, 'k-', linewidth=1)
                    ax_orig.grid(True, alpha=0.3)
                    ax_orig.set_xlabel("时间 (s)")
                    ax_orig.set_ylabel("幅值")
                    ax_orig.set_title(f"通道 {channel_num} 原始信号")
                    plt.tight_layout()
                    st.pyplot(fig_orig, use_container_width=True)

                with col2:
                    st.markdown("**频域分析 (≤50Hz)**")
                    freq, spectrum = compute_spectrum(original_segment, fs)
                    fig_freq, ax_freq = plt.subplots(figsize=(8, 3))
                    ax_freq.plot(freq, spectrum, 'k-', linewidth=1)
                    ax_freq.grid(True, alpha=0.3)
                    ax_freq.set_xlabel("频率 (Hz)")
                    ax_freq.set_ylabel("幅值")
                    ax_freq.set_title("原始信号频谱")
                    plt.tight_layout()
                    st.pyplot(fig_freq, use_container_width=True)

            st.markdown("---")

            # 2. VMD原始模态分析
            st.subheader("🔍 VMD原始模态分析")

            # 概览图
            with st.expander("📊 所有模态概览", expanded=True):
                fig_overview, ax_overview = plt.subplots(figsize=(12, 4))
                for i_mode in range(K):
                    center_freq_hz = omega[-1, i_mode] * fs / (2 * np.pi)
                    ax_overview.plot(time_axis, u[i_mode, :],
                                   label=f"M{i_mode + 1} ({center_freq_hz:.1f}Hz)",
                                   alpha=0.8, linewidth=1)
                ax_overview.legend(fontsize='small', ncol=3)
                ax_overview.grid(True, alpha=0.3)
                ax_overview.set_xlabel("时间 (s)")
                ax_overview.set_ylabel("幅值")
                ax_overview.set_title("VMD分解所有模态")
                plt.tight_layout()
                st.pyplot(fig_overview, use_container_width=True)

            # 使用原生容器实现滚动
            with st.container(height=800):
                st.markdown("**🔬 独立模态分析 (时域+频域)**")
                for i_mode in range(K):
                    center_freq_hz = omega[-1, i_mode] * fs / (2 * np.pi)
                    create_mode_display(u[i_mode, :], time_axis, i_mode, center_freq_hz, channel_num, "原始")

            st.markdown("---")

            # 3. VMD重建控制区
            st.subheader("🛠️ VMD信号重建控制")

            with st.form(f"reconstruction_form_{channel_num}"):
                st.markdown("**配置重建参数：**")

                # 创建表格头部
                header_cols = st.columns([1, 1, 2, 2, 2])
                header_cols[0].markdown("**模态**")
                header_cols[1].markdown("**保留**")
                header_cols[2].markdown("**滤波类型**")
                header_cols[3].markdown("**低截止(Hz)**")
                header_cols[4].markdown("**高截止(Hz)**")

                configs = []
                for i_mode in range(K):
                    center_freq_hz = omega[-1, i_mode] * fs / (2 * np.pi)
                    cols = st.columns([1, 1, 2, 2, 2])

                    with cols[0]:
                        st.write(f"M{i_mode + 1}")
                        st.caption(f"{center_freq_hz:.1f}Hz")

                    with cols[1]:
                        keep = st.checkbox("", value=i_mode < K - 1,
                                         key=f"k_{i_mode}_{channel_num}",
                                         label_visibility="collapsed")

                    with cols[2]:
                        ft = st.selectbox("", ["无", "低通", "高通", "带通"],
                                        key=f"f_{i_mode}_{channel_num}",
                                        label_visibility="collapsed")

                    # 根据滤波类型动态禁用输入框
                    lc_disabled = ft not in ["低通", "带通"]
                    hc_disabled = ft not in ["高通", "带通"]

                    with cols[3]:
                        lc = st.number_input("", value=10.0, step=1.0, format="%.1f",
                                           key=f"lc_{i_mode}_{channel_num}",
                                           label_visibility="collapsed",
                                           disabled=lc_disabled)

                    with cols[4]:
                        hc = st.number_input("", value=100.0, step=1.0, format="%.1f",
                                           key=f"hc_{i_mode}_{channel_num}",
                                           label_visibility="collapsed",
                                           disabled=hc_disabled)

                    configs.append({"keep": keep, "ft": ft, "lc": lc, "hc": hc})

                if st.form_submit_button("🔧 执行VMD重建", type="primary"):
                    with st.spinner(f"通道 {channel_num} 重建中..."):
                        recon = np.zeros_like(original_segment)
                        processed_modes_data = []

                        for i_mode in range(K):
                            if configs[i_mode]["keep"]:
                                f_map = {"低通": "low", "高通": "high", "带通": "band"}
                                ft_type = f_map.get(configs[i_mode]["ft"])

                                if ft_type:
                                    processed_mode = apply_filter(u[i_mode, :], fs, ft_type,
                                                                configs[i_mode]["lc"],
                                                                configs[i_mode]["hc"])
                                else:
                                    processed_mode = u[i_mode, :]

                                processed_modes_data.append({
                                    'mode': processed_mode,
                                    'label': f"M{i_mode + 1} ({configs[i_mode]['ft']})",
                                    'mode_idx': i_mode,
                                    'center_freq': omega[-1, i_mode] * fs / (2 * np.pi)
                                })
                                recon += processed_mode

                        st.session_state.processed_signals[channel_num]['reconstructed'] = recon
                        st.session_state.processed_signals[channel_num]['processed_modes'] = processed_modes_data
                    st.success("✅ VMD重建完成！")
                    st.rerun()

            # 4. VMD重建结果显示
            processed_info = st.session_state.processed_signals[channel_num]
            if 'processed_modes' in processed_info and processed_info['processed_modes']:
                st.subheader("📊 VMD重建分量分析")

                # 重建分量概览
                with st.expander("📈 重建分量概览", expanded=True):
                    fig_recon, ax_recon = plt.subplots(figsize=(12, 4))
                    for item in processed_info['processed_modes']:
                        ax_recon.plot(time_axis, item['mode'],
                                    label=item['label'], alpha=0.8, linewidth=1)
                    ax_recon.legend(fontsize='small', ncol=3)
                    ax_recon.grid(True, alpha=0.3)
                    ax_recon.set_xlabel("时间 (s)")
                    ax_recon.set_ylabel("幅值")
                    ax_recon.set_title("重建用分量")
                    plt.tight_layout()
                    st.pyplot(fig_recon, use_container_width=True)

                # 独立重建分量分析
                with st.container(height=800):
                    st.markdown("**🔬 独立重建分量分析 (时域+频域)**")
                    for item in processed_info['processed_modes']:
                        create_mode_display(item['mode'], time_axis, item['mode_idx'],
                                          item['center_freq'], channel_num, f"重建-{item['label']}")

            st.markdown("---")

            # 5. 直接滤波模块
            st.subheader("⚡ 直接信号滤波")

            col1, col2, col3, col4 = st.columns([2, 1.5, 1.5, 1.5])

            with col1:
                direct_ft = st.selectbox("滤波方式", ["低通", "高通", "带通"],
                                       key=f"direct_ft_{channel_num}")

            # 根据滤波类型显示相应的参数
            direct_lc, direct_hc = None, None
            if direct_ft == "低通":
                with col2:
                    direct_lc = st.number_input("截止频率(Hz)", value=50.0,
                                              key=f"direct_lc_{channel_num}")
            elif direct_ft == "高通":
                with col2:
                    direct_hc = st.number_input("截止频率(Hz)", value=100.0,
                                              key=f"direct_hc_{channel_num}")
            elif direct_ft == "带通":
                with col2:
                    direct_lc = st.number_input("低截止(Hz)", value=10.0,
                                              key=f"direct_lc_band_{channel_num}")
                with col3:
                    direct_hc = st.number_input("高截止(Hz)", value=100.0,
                                              key=f"direct_hc_band_{channel_num}")

            with col4:
                if st.button("⚡ 执行直接滤波",
                           key=f"direct_filter_btn_{channel_num}",
                           type="primary"):
                    with st.spinner(f"通道 {channel_num} 直接滤波中..."):
                        f_map = {"低通": "low", "高通": "high", "带通": "band"}
                        direct_filtered = apply_filter(original_segment, fs,
                                                     f_map.get(direct_ft),
                                                     direct_lc, direct_hc)
                        st.session_state.processed_signals[channel_num]['direct_filtered'] = direct_filtered
                    st.success("✅ 直接滤波完成！")
                    st.rerun()

            # 6. 最终效果总览
            st.markdown("---")
            st.subheader("🏁 综合对比分析")

            processed_info = st.session_state.processed_signals[channel_num]
            if 'reconstructed' in processed_info or 'direct_filtered' in processed_info:

                # 时域对比
                col1, col2 = st.columns([1, 1])

                with col1:
                    st.markdown("**时域对比**")
                    fig_comp, ax_comp = plt.subplots(figsize=(8, 4))
                    ax_comp.plot(time_axis, original_segment, 'k-', alpha=0.5,
                               linewidth=1, label='原始信号')

                    if 'direct_filtered' in processed_info:
                        ax_comp.plot(time_axis, processed_info['direct_filtered'], 'b-',
                                   linewidth=1.5, label='直接滤波')

                    if 'reconstructed' in processed_info:
                        ax_comp.plot(time_axis, processed_info['reconstructed'], 'r-',
                                   linewidth=1.5, label='VMD重建')

                    ax_comp.legend()
                    ax_comp.grid(True, alpha=0.3)
                    ax_comp.set_xlabel("时间 (s)")
                    ax_comp.set_ylabel("幅值")
                    ax_comp.set_title("时域信号对比")
                    plt.tight_layout()
                    st.pyplot(fig_comp, use_container_width=True)

                with col2:
                    st.markdown("**频域对比 (≤50Hz)**")
                    fig_freq_comp, ax_freq_comp = plt.subplots(figsize=(8, 4))

                    # 原始信号频谱
                    freq_orig, spectrum_orig = compute_spectrum(original_segment, fs)
                    ax_freq_comp.plot(freq_orig, spectrum_orig, 'k-', alpha=0.5,
                                    linewidth=1, label='原始信号')

                    if 'direct_filtered' in processed_info:
                        freq_direct, spectrum_direct = compute_spectrum(processed_info['direct_filtered'], fs)
                        ax_freq_comp.plot(freq_direct, spectrum_direct, 'b-',
                                        linewidth=1.5, label='直接滤波')

                    if 'reconstructed' in processed_info:
                        freq_recon, spectrum_recon = compute_spectrum(processed_info['reconstructed'], fs)
                        ax_freq_comp.plot(freq_recon, spectrum_recon, 'r-',
                                        linewidth=1.5, label='VMD重建')

                    ax_freq_comp.legend()
                    ax_freq_comp.grid(True, alpha=0.3)
                    ax_freq_comp.set_xlabel("频率 (Hz)")
                    ax_freq_comp.set_ylabel("幅值")
                    ax_freq_comp.set_title("频域对比")
                    plt.tight_layout()
                    st.pyplot(fig_freq_comp, use_container_width=True)

                # 下载按钮
                st.markdown("---")
                df_to_download = pd.DataFrame({
                    'Time (s)': time_axis,
                    'Original': original_segment
                })

                if 'reconstructed' in processed_info:
                    df_to_download['VMD_Reconstructed'] = processed_info['reconstructed']

                if 'direct_filtered' in processed_info:
                    df_to_download['Direct_Filtered'] = processed_info['direct_filtered']

                csv = df_to_download.to_csv(index=False).encode('utf-8-sig')
                st.download_button(
                    label=f"📥 下载通道 {channel_num} 处理结果 (.csv)",
                    data=csv,
                    file_name=f"processed_ch{channel_num}_{os.path.basename(full_file_path) if full_file_path else 'data'}.csv",
                    mime="text/csv",
                    key=f"download_btn_{channel_num}",
                    type="primary"
                )
            else:
                st.info("💡 请执行VMD重建或直接滤波以查看对比结果。")

if __name__ == "__main__":
    main()